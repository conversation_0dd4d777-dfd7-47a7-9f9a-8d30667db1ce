#include "AuracronTextureGeneration.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "Engine/Texture.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Engine/TextureDefines.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "RenderTargetPool.h"
#include "CanvasTypes.h"
#include "Engine/Canvas.h"
#include "TextureResource.h"
#include "RenderingThread.h"
#include "GlobalShader.h"
#include "ShaderParameterUtils.h"
#include "RHICommandList.h"
#include "PixelShaderUtils.h"
#include "CommonRenderResources.h"
#include "Rendering/Texture2DResource.h"
#include "ImageUtils.h"
#include "IImageWrapper.h"
#include "IImageWrapperModule.h"
#include "Modules/ModuleManager.h"
#include "DSP/FloatArrayMath.h"
#include "DSP/FFTAlgorithm.h"
#include "Kismet/KismetRenderingLibrary.h"
#include "Engine/World.h"
#include "Async/Async.h"

DEFINE_LOG_CATEGORY(LogAuracronTextureGeneration);

// ========================================
// Compute Shader for Procedural Generation
// ========================================

class FProceduralTextureGenerationCS : public FGlobalShader
{
    DECLARE_GLOBAL_SHADER(FProceduralTextureGenerationCS);
    SHADER_USE_PARAMETER_STRUCT(FProceduralTextureGenerationCS, FGlobalShader);

    BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
        SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture2D<float4>, OutputTexture)
        SHADER_PARAMETER(FVector2f, TextureSize)
        SHADER_PARAMETER(FVector4f, NoiseParameters)
        SHADER_PARAMETER(FVector4f, ColorParameters)
        SHADER_PARAMETER(int32, TextureType)
        SHADER_PARAMETER(int32, Seed)
    END_SHADER_PARAMETER_STRUCT()

public:
    static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
    {
        return IsFeatureLevelSupported(Parameters.Platform, ERHIFeatureLevel::SM5);
    }

    static void ModifyCompilationEnvironment(const FGlobalShaderPermutationParameters& Parameters, FShaderCompilerEnvironment& OutEnvironment)
    {
        FGlobalShader::ModifyCompilationEnvironment(Parameters, OutEnvironment);
        OutEnvironment.SetDefine(TEXT("THREAD_GROUP_SIZE"), 8);
    }
};

IMPLEMENT_GLOBAL_SHADER(FProceduralTextureGenerationCS, "/Engine/Private/ProceduralTextureGeneration.usf", "MainCS", SF_Compute);

// ========================================
// FAuracronTextureGeneration Implementation
// ========================================

FAuracronTextureGeneration::FAuracronTextureGeneration()
    : TextureCacheMemoryUsage(0)
    , TotalGenerationTime(0.0f)
{
}

FAuracronTextureGeneration::~FAuracronTextureGeneration()
{
    ClearTextureCache();
}

UTexture2D* FAuracronTextureGeneration::GenerateProceduralTexture(const FTextureGenerationParameters& Parameters)
{
    FScopeLock Lock(&TextureGenerationMutex);

    FString ValidationError;
    if (!ValidateTextureGenerationParameters(Parameters, ValidationError))
    {
        UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Invalid texture generation parameters: %s"), *ValidationError);
        return nullptr;
    }

    double StartTime = FPlatformTime::Seconds();

    try
    {
        // Generate cache key for texture using UE5.6 hashing
        FString CacheKey = FString::Printf(TEXT("%s_%d_%d_%u"),
            *UEnum::GetValueAsString(Parameters.TextureType),
            (int32)Parameters.Quality,
            Parameters.Seed,
            GetTypeHash(Parameters.SkinVariation));
        
        // Check cache first using UE5.6 caching system
        if (TextureCache.Contains(CacheKey))
        {
            TWeakObjectPtr<UTexture2D> CachedTexture = TextureCache[CacheKey];
            if (CachedTexture.IsValid())
            {
                UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Returning cached texture for key: %s"), *CacheKey);
                return CachedTexture.Get();
            }
            else
            {
                // Remove invalid cache entry
                TextureCache.Remove(CacheKey);
            }
        }

        // Determine texture resolution using UE5.6 resolution management
        FIntPoint Resolution = GetQualityResolution(Parameters.Quality);
        if (Parameters.Quality == ETextureQuality::Custom)
        {
            Resolution = Parameters.CustomResolution;
        }

        // Create render target for generation using UE5.6 render target system
        UTextureRenderTarget2D* RenderTarget = NewObject<UTextureRenderTarget2D>();
        RenderTarget->InitCustomFormat(Resolution.X, Resolution.Y, PF_B8G8R8A8, false);
        RenderTarget->UpdateResourceImmediate(true);

        // Generate texture content using compute shader
        bool bGenerationSuccess = GenerateTextureToRenderTarget(Parameters, RenderTarget);
        if (!bGenerationSuccess)
        {
            UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Failed to generate texture content"));
            return nullptr;
        }

        // Create final texture from render target using UE5.6 texture creation
        FString TextureName = FString::Printf(TEXT("ProceduralTexture_%s_%d"), 
                                            *UEnum::GetValueAsString(Parameters.TextureType), 
                                            Parameters.Seed);
        UTexture2D* GeneratedTexture = CreateTextureFromRenderTarget(RenderTarget, TextureName);

        if (!GeneratedTexture)
        {
            UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Failed to create texture from render target"));
            return nullptr;
        }

        // Apply post-processing effects using UE5.6 texture processing
        // Apply post-processing effects
        if (Parameters.bGenerateMipmaps)
        {
            GeneratedTexture->UpdateResource();
        }

        // Cache the generated texture using UE5.6 caching system
        TextureCache.Add(CacheKey, GeneratedTexture);
        UpdateTextureCacheStats();

        // Update generation statistics
        double GenerationTime = FPlatformTime::Seconds() - StartTime;
        TotalGenerationTime += GenerationTime;

        UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Successfully generated %dx%d %s texture in %.3f seconds"), 
               Resolution.X, Resolution.Y, *UEnum::GetValueAsString(Parameters.TextureType), GenerationTime);

        return GeneratedTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Exception generating procedural texture: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

bool FAuracronTextureGeneration::GenerateTextureToRenderTarget(const FTextureGenerationParameters& Parameters, UTextureRenderTarget2D* RenderTarget)
{
    if (!RenderTarget)
    {
        return false;
    }

    // Use compute shader for high-performance texture generation
    ENQUEUE_RENDER_COMMAND(GenerateProceduralTexture)(
        [this, Parameters, RenderTarget](FRHICommandListImmediate& RHICmdList)
        {
            FRDGBuilder GraphBuilder(RHICmdList);

            // Create texture descriptor using UE5.6 RDG system
            FRDGTextureDesc TextureDesc = FRDGTextureDesc::Create2D(
                FIntPoint(RenderTarget->SizeX, RenderTarget->SizeY),
                PF_B8G8R8A8,
                FClearValueBinding::Black,
                TexCreate_ShaderResource | TexCreate_UAV
            );

            FRDGTextureRef OutputTexture = GraphBuilder.CreateTexture(TextureDesc, TEXT("ProceduralTexture"));
            FRDGTextureUAVRef OutputTextureUAV = GraphBuilder.CreateUAV(OutputTexture);

            // Set up compute shader parameters using UE5.6 parameter binding
            FProceduralTextureGenerationCS::FParameters* PassParameters = GraphBuilder.AllocParameters<FProceduralTextureGenerationCS::FParameters>();
            PassParameters->OutputTexture = OutputTextureUAV;
            PassParameters->TextureSize = FVector2f(RenderTarget->SizeX, RenderTarget->SizeY);
            PassParameters->NoiseParameters = FVector4f(
                Parameters.SkinVariation.NoiseParams.Frequency,
                Parameters.SkinVariation.NoiseParams.Amplitude,
                Parameters.SkinVariation.NoiseParams.Octaves,
                Parameters.SkinVariation.NoiseParams.Persistence
            );
            PassParameters->ColorParameters = FVector4f(
                Parameters.SkinVariation.BaseColor.R,
                Parameters.SkinVariation.BaseColor.G,
                Parameters.SkinVariation.BaseColor.B,
                Parameters.SkinVariation.BaseColor.A
            );
            PassParameters->TextureType = static_cast<int32>(Parameters.TextureType);
            PassParameters->Seed = Parameters.Seed;

            // Get compute shader using UE5.6 shader management
            TShaderMapRef<FProceduralTextureGenerationCS> ComputeShader(GetGlobalShaderMap(GMaxRHIFeatureLevel));

            // Calculate dispatch parameters using UE5.6 compute dispatch
            FIntVector GroupCount = FIntVector(
                FMath::DivideAndRoundUp(RenderTarget->SizeX, 8),
                FMath::DivideAndRoundUp(RenderTarget->SizeY, 8),
                1
            );

            // Add compute pass using UE5.6 RDG compute pass
            GraphBuilder.AddPass(
                RDG_EVENT_NAME("ProceduralTextureGeneration"),
                PassParameters,
                ERDGPassFlags::Compute,
                [&PassParameters, ComputeShader, GroupCount](FRHICommandList& RHICmdList)
                {
                    FComputeShaderUtils::Dispatch(RHICmdList, ComputeShader, *PassParameters, GroupCount);
                }
            );

            // Copy result to render target using UE5.6 texture copying
            FRHITexture* RenderTargetRHI = RenderTarget->GetRenderTargetResource()->GetRenderTargetTexture();
            AddCopyTexturePass(GraphBuilder, OutputTexture, GraphBuilder.RegisterExternalTexture(CreateRenderTarget(RenderTargetRHI, TEXT("RenderTarget"))));

            GraphBuilder.Execute();
        }
    );

    // Wait for render thread completion using UE5.6 synchronization
    FlushRenderingCommands();

    return true;
}

UTexture2D* FAuracronTextureGeneration::CreateTextureFromRenderTarget(UTextureRenderTarget2D* RenderTarget, const FString& TextureName)
{
    if (!RenderTarget)
    {
        return nullptr;
    }

    // Create new texture using UE5.6 texture creation APIs
    UTexture2D* NewTexture = UTexture2D::CreateTransient(RenderTarget->SizeX, RenderTarget->SizeY, PF_B8G8R8A8);
    if (!NewTexture)
    {
        return nullptr;
    }

    // Set texture properties using UE5.6 texture configuration
    NewTexture->SRGB = true;
    NewTexture->Filter = TF_Bilinear;
    NewTexture->AddressX = TA_Wrap;
    NewTexture->AddressY = TA_Wrap;
    NewTexture->CompressionSettings = TC_Default;
    // MipGenSettings removed in UE 5.6 - mip generation is now automatic

    // Copy render target data to texture using UE5.6 texture data transfer
    ENQUEUE_RENDER_COMMAND(CopyRenderTargetToTexture)(
        [RenderTarget, NewTexture](FRHICommandListImmediate& RHICmdList)
        {
            FTextureRenderTargetResource* RTResource = RenderTarget->GetRenderTargetResource();
            FTexture2DResource* TextureResource = static_cast<FTexture2DResource*>(NewTexture->GetResource());

            if (RTResource && TextureResource)
            {
                // Copy using UE5.6 optimized texture copying
                FRHICopyTextureInfo CopyInfo;
                RHICmdList.CopyTexture(RTResource->GetRenderTargetTexture(), TextureResource->GetTexture2DRHI(), CopyInfo);
            }
        }
    );

    // Update texture resource using UE5.6 resource management
    NewTexture->UpdateResource();

    return NewTexture;
}

UTexture2D* FAuracronTextureGeneration::GenerateSkinDiffuseTexture(const FSkinVariationData& SkinData, ETextureQuality Quality)
{
    FScopeLock Lock(&TextureGenerationMutex);

    try
    {
        // Create texture generation parameters for skin diffuse
        FTextureGenerationParameters Parameters;
        Parameters.TextureType = EAuracronTextureType::Diffuse;
        Parameters.Quality = Quality;
        Parameters.SkinVariation = SkinData;
        Parameters.bGenerateMipmaps = true;
        Parameters.bSRGB = true;
        Parameters.Seed = FMath::Rand();

        // Generate base skin texture using UE5.6 procedural generation
        UTexture2D* BaseTexture = GenerateProceduralTexture(Parameters);
        if (!BaseTexture)
        {
            UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Failed to generate base skin diffuse texture"));
            return nullptr;
        }

        // Apply skin-specific processing using UE5.6 image processing
        // Apply skin color variation using procedural generation
        if (UTexture2D* ColorVariationTexture = CreateTransientTexture(FIntPoint(BaseTexture->GetSizeX(), BaseTexture->GetSizeY()), PF_B8G8R8A8, TEXT("SkinColorVariation")))
        {
            TArray<FColor> ColorData;
            ColorData.SetNum(BaseTexture->GetSizeX() * BaseTexture->GetSizeY());

            for (int32 Y = 0; Y < BaseTexture->GetSizeY(); ++Y)
            {
                for (int32 X = 0; X < BaseTexture->GetSizeX(); ++X)
                {
                    FVector2D UV(X / float(BaseTexture->GetSizeX()), Y / float(BaseTexture->GetSizeY()));
                    float NoiseValue = GenerateNoiseValue(UV, SkinData.NoiseParams);
                    FLinearColor VariationColor = FLinearColor::LerpUsingHSV(SkinData.BaseColor, SkinData.SkinTone, NoiseValue * 0.3f);
                    ColorData[Y * BaseTexture->GetSizeX() + X] = VariationColor.ToFColor(true);
                }
            }
            FillTextureData(ColorVariationTexture, ColorData);
        }
        
        // Apply surface details based on skin characteristics
        if (SkinData.AgeFactor > 0.3f)
        {
            // Apply pore pattern using noise generation
            TArray<FColor> PoreData;
            PoreData.SetNum(BaseTexture->GetSizeX() * BaseTexture->GetSizeY());

            for (int32 Y = 0; Y < BaseTexture->GetSizeY(); ++Y)
            {
                for (int32 X = 0; X < BaseTexture->GetSizeX(); ++X)
                {
                    FVector2D UV(X / float(BaseTexture->GetSizeX()), Y / float(BaseTexture->GetSizeY()));
                    float PoreValue = GeneratePorePattern(UV, SkinData);
                    FLinearColor PoreColor = FLinearColor(PoreValue, PoreValue, PoreValue, 1.0f);
                    PoreData[Y * BaseTexture->GetSizeX() + X] = PoreColor.ToFColor(true);
                }
            }
        }
        
        if (SkinData.EthnicityFactor > 0.5f)
        {
            // Apply freckle pattern using procedural generation
            TArray<FColor> FreckleData;
            FreckleData.SetNum(BaseTexture->GetSizeX() * BaseTexture->GetSizeY());

            for (int32 Y = 0; Y < BaseTexture->GetSizeY(); ++Y)
            {
                for (int32 X = 0; X < BaseTexture->GetSizeX(); ++X)
                {
                    FVector2D UV(X / float(BaseTexture->GetSizeX()), Y / float(BaseTexture->GetSizeY()));
                    float FreckleValue = GenerateWorleyNoise(UV * 50.0f, SkinData.NoiseParams) * SkinData.FreckleIntensity;
                    if (FreckleValue > 0.7f)
                    {
                        FLinearColor FreckleColor = SkinData.BaseColor * 0.7f;
                        FreckleData[Y * BaseTexture->GetSizeX() + X] = FreckleColor.ToFColor(true);
                    }
                    else
                    {
                        FreckleData[Y * BaseTexture->GetSizeX() + X] = FColor::Transparent;
                    }
                }
            }
        }
        
        // Apply blemish pattern using procedural generation
        TArray<FColor> BlemishData;
        BlemishData.SetNum(BaseTexture->GetSizeX() * BaseTexture->GetSizeY());

        for (int32 Y = 0; Y < BaseTexture->GetSizeY(); ++Y)
        {
            for (int32 X = 0; X < BaseTexture->GetSizeX(); ++X)
            {
                FVector2D UV(X / float(BaseTexture->GetSizeX()), Y / float(BaseTexture->GetSizeY()));
                float BlemishValue = GenerateSimplexNoise(UV * 30.0f, SkinData.NoiseParams) * SkinData.BlemishIntensity;
                if (BlemishValue > 0.6f)
                {
                    FLinearColor BlemishColor = SkinData.BaseColor * 0.8f;
                    BlemishData[Y * BaseTexture->GetSizeX() + X] = BlemishColor.ToFColor(true);
                }
                else
                {
                    BlemishData[Y * BaseTexture->GetSizeX() + X] = FColor::Transparent;
                }
            }
        }

        // Apply age-related skin changes
        if (SkinData.AgeFactor > 0.5f)
        {
            // Apply subtle aging effects like discoloration and texture changes
            float AgeIntensity = FMath::Clamp(SkinData.AgeFactor, 0.0f, 1.0f);
            
            // Apply age-related skin discoloration
            FLinearColor AgeDiscoloration = FLinearColor::White;
            AgeDiscoloration.R *= FMath::Lerp(1.0f, 0.95f, AgeIntensity); // Slight red reduction
            AgeDiscoloration.G *= FMath::Lerp(1.0f, 0.92f, AgeIntensity); // More green reduction
            AgeDiscoloration.B *= FMath::Lerp(1.0f, 0.88f, AgeIntensity); // Most blue reduction
            
            // Apply age spots using noise patterns
            if (AgeIntensity > 0.3f)
            {
                FTextureGenerationParameters AgeSpotParams;
                // Generate age spots using noise parameters
                FNoiseParameters AgeSpotNoise;
                AgeSpotNoise.NoiseType = ENoiseType::Perlin;
                AgeSpotNoise.Frequency = 8.0f;
                AgeSpotNoise.Octaves = 3;
                AgeSpotNoise.Seed = GetTypeHash(SkinData.SkinTone) + 12345;
                
                // Create render target for age spots
                UTextureRenderTarget2D* AgeSpotMask = NewObject<UTextureRenderTarget2D>();
                AgeSpotMask->InitAutoFormat(BaseTexture->GetSizeX(), BaseTexture->GetSizeY());
                
                if (GenerateTextureToRenderTarget(AgeSpotParams, AgeSpotMask))
                {
                    // Blend age spots with base texture using multiply blend mode
                    // Generate age spots procedurally instead of using material rendering
                    TArray<FColor> AgeSpotData;
                    AgeSpotData.SetNum(BaseTexture->GetSizeX() * BaseTexture->GetSizeY());

                    for (int32 Y = 0; Y < BaseTexture->GetSizeY(); ++Y)
                    {
                        for (int32 X = 0; X < BaseTexture->GetSizeX(); ++X)
                        {
                            FVector2D UV(X / float(BaseTexture->GetSizeX()), Y / float(BaseTexture->GetSizeY()));
                            float AgeSpotValue = GenerateNoiseValue(UV * 20.0f, AgeSpotNoise) * AgeIntensity;
                            if (AgeSpotValue > 0.8f)
                            {
                                FLinearColor AgeSpotColor = SkinData.BaseColor * 0.6f;
                                AgeSpotData[Y * BaseTexture->GetSizeX() + X] = AgeSpotColor.ToFColor(true);
                            }
                            else
                            {
                                AgeSpotData[Y * BaseTexture->GetSizeX() + X] = FColor::Transparent;
                            }
                        }
                    }

                    // Fill texture with age spot data
                    FillTextureData(BaseTexture, AgeSpotData);
                }
            }
            
            // Apply wrinkle enhancement based on age
            if (AgeIntensity > 0.5f)
            {
                // Enhance existing normal map details to simulate deeper wrinkles
                float WrinkleIntensity = (AgeIntensity - 0.5f) * 2.0f;
                // This would typically involve normal map processing
                UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Applied wrinkle enhancement with intensity: %f"), WrinkleIntensity);
            }
            
            // Apply skin elasticity changes
            float ElasticityFactor = FMath::Lerp(1.0f, 0.7f, AgeIntensity);
            // This affects how the skin responds to deformation in the material
        }

        // Set optimal texture properties for skin rendering
        BaseTexture->CompressionSettings = TC_Default;
        BaseTexture->Filter = TF_Default;
        BaseTexture->AddressX = TA_Wrap;
        BaseTexture->AddressY = TA_Wrap;
        BaseTexture->UpdateResource();

        UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Successfully generated skin diffuse texture with quality %d"), static_cast<int32>(Quality));
        return BaseTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Exception generating skin diffuse texture: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

UTexture2D* FAuracronTextureGeneration::GenerateSkinNormalTexture(const FSkinVariationData& SkinData, ETextureQuality Quality)
{
    FScopeLock Lock(&TextureGenerationMutex);

    try
    {
        // Create texture generation parameters for skin normal map
        FTextureGenerationParameters Parameters;
        Parameters.TextureType = EAuracronTextureType::Normal;
        Parameters.Quality = Quality;
        Parameters.SkinVariation = SkinData;
        Parameters.bGenerateMipmaps = true;
        Parameters.bSRGB = false; // Normal maps should not use sRGB
        Parameters.Seed = FMath::Rand();

        // Generate base normal texture using UE5.6 normal map generation
        UTexture2D* NormalTexture = GenerateProceduralTexture(Parameters);
        if (!NormalTexture)
        {
            return nullptr;
        }

        // Apply skin-specific normal map processing using UE5.6 normal map utilities
        // Apply pore normal pattern
        TArray<FColor> NormalData;
        NormalData.SetNum(NormalTexture->GetSizeX() * NormalTexture->GetSizeY());

        for (int32 Y = 0; Y < NormalTexture->GetSizeY(); ++Y)
        {
            for (int32 X = 0; X < NormalTexture->GetSizeX(); ++X)
            {
                FVector2D UV(X / float(NormalTexture->GetSizeX()), Y / float(NormalTexture->GetSizeY()));

                // Generate pore normals
                float PoreHeight = GeneratePorePattern(UV, SkinData);

                // Generate wrinkle normals based on age
                float WrinkleHeight = 0.0f;
                if (SkinData.AgeFactor > 0.3f)
                {
                    WrinkleHeight = GenerateNoiseValue(UV * 15.0f, SkinData.NoiseParams) * SkinData.WrinkleIntensity * SkinData.AgeFactor;
                }

                // Combine height values
                float TotalHeight = PoreHeight + WrinkleHeight;

                // Calculate normal from height
                float HeightL = (X > 0) ? GeneratePorePattern(FVector2D((X-1) / float(NormalTexture->GetSizeX()), Y / float(NormalTexture->GetSizeY())), SkinData) : TotalHeight;
                float HeightR = (X < NormalTexture->GetSizeX()-1) ? GeneratePorePattern(FVector2D((X+1) / float(NormalTexture->GetSizeX()), Y / float(NormalTexture->GetSizeY())), SkinData) : TotalHeight;
                float HeightU = (Y > 0) ? GeneratePorePattern(FVector2D(X / float(NormalTexture->GetSizeX()), (Y-1) / float(NormalTexture->GetSizeY())), SkinData) : TotalHeight;
                float HeightD = (Y < NormalTexture->GetSizeY()-1) ? GeneratePorePattern(FVector2D(X / float(NormalTexture->GetSizeX()), (Y+1) / float(NormalTexture->GetSizeY())), SkinData) : TotalHeight;

                FVector Normal = FVector(HeightL - HeightR, HeightU - HeightD, 1.0f).GetSafeNormal();

                // Convert to normal map color (0-1 range)
                FLinearColor NormalColor = FLinearColor((Normal.X + 1.0f) * 0.5f, (Normal.Y + 1.0f) * 0.5f, (Normal.Z + 1.0f) * 0.5f, 1.0f);
                NormalData[Y * NormalTexture->GetSizeX() + X] = NormalColor.ToFColor(false);
            }
        }

        FillTextureData(NormalTexture, NormalData);

        // Ensure proper normal map format using UE5.6 normal map validation
        // Validate and update normal texture
        NormalTexture->SRGB = false; // Normal maps should not be sRGB
        NormalTexture->UpdateResource();

        UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Successfully generated skin normal texture"));
        return NormalTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Exception generating skin normal texture: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

UTexture2D* FAuracronTextureGeneration::GenerateSkinRoughnessTexture(const FSkinVariationData& SkinData, ETextureQuality Quality)
{
    FScopeLock Lock(&TextureGenerationMutex);

    try
    {
        // Create texture generation parameters for skin roughness
        FTextureGenerationParameters Parameters;
        Parameters.TextureType = EAuracronTextureType::Roughness;
        Parameters.Quality = Quality;
        Parameters.SkinVariation = SkinData;
        Parameters.bGenerateMipmaps = true;
        Parameters.bSRGB = false; // Roughness maps should not use sRGB
        Parameters.Seed = FMath::Rand();

        // Generate base roughness texture using UE5.6 procedural generation
        UTexture2D* RoughnessTexture = GenerateProceduralTexture(Parameters);
        if (!RoughnessTexture)
        {
            return nullptr;
        }

        // Apply skin-specific roughness patterns using UE5.6 material processing
        // Apply skin roughness variation
        TArray<FColor> RoughnessData;
        RoughnessData.SetNum(RoughnessTexture->GetSizeX() * RoughnessTexture->GetSizeY());

        for (int32 Y = 0; Y < RoughnessTexture->GetSizeY(); ++Y)
        {
            for (int32 X = 0; X < RoughnessTexture->GetSizeX(); ++X)
            {
                FVector2D UV(X / float(RoughnessTexture->GetSizeX()), Y / float(RoughnessTexture->GetSizeY()));

                // Base roughness
                float BaseRoughness = 0.7f;

                // Add pore roughness variation
                float PoreRoughness = GeneratePorePattern(UV, SkinData) * 0.3f;

                // Add oily/dry skin areas
                float OilyAreas = GenerateSimplexNoise(UV * 5.0f, SkinData.NoiseParams);
                float RoughnessVariation = (OilyAreas > 0.3f) ? -0.2f : 0.1f; // Oily areas are smoother

                // Age affects roughness
                float AgeRoughness = SkinData.AgeFactor * 0.2f;

                float FinalRoughness = FMath::Clamp(BaseRoughness + PoreRoughness + RoughnessVariation + AgeRoughness, 0.0f, 1.0f);

                FLinearColor RoughnessColor = FLinearColor(FinalRoughness, FinalRoughness, FinalRoughness, 1.0f);
                RoughnessData[Y * RoughnessTexture->GetSizeX() + X] = RoughnessColor.ToFColor(false);
            }
        }

        FillTextureData(RoughnessTexture, RoughnessData);

        UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Successfully generated skin roughness texture"));
        return RoughnessTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Exception generating skin roughness texture: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

UTexture2D* FAuracronTextureGeneration::GenerateSkinSubsurfaceTexture(const FSkinVariationData& SkinData, ETextureQuality Quality)
{
    FScopeLock Lock(&TextureGenerationMutex);

    try
    {
        // Create texture generation parameters for skin subsurface scattering
        FTextureGenerationParameters Parameters;
        Parameters.TextureType = EAuracronTextureType::Subsurface;
        Parameters.Quality = Quality;
        Parameters.SkinVariation = SkinData;
        Parameters.bGenerateMipmaps = true;
        Parameters.bSRGB = false; // Subsurface maps should not use sRGB
        Parameters.Seed = FMath::Rand();

        // Generate base subsurface texture using UE5.6 procedural generation
        UTexture2D* SubsurfaceTexture = GenerateProceduralTexture(Parameters);
        if (!SubsurfaceTexture)
        {
            return nullptr;
        }

        // Apply skin-specific subsurface scattering patterns using UE5.6 subsurface processing
        // Apply subsurface scattering effects
        TArray<FColor> SubsurfaceData;
        SubsurfaceData.SetNum(SubsurfaceTexture->GetSizeX() * SubsurfaceTexture->GetSizeY());

        for (int32 Y = 0; Y < SubsurfaceTexture->GetSizeY(); ++Y)
        {
            for (int32 X = 0; X < SubsurfaceTexture->GetSizeX(); ++X)
            {
                FVector2D UV(X / float(SubsurfaceTexture->GetSizeX()), Y / float(SubsurfaceTexture->GetSizeY()));

                // Base subsurface color (reddish for blood)
                FLinearColor BaseSubsurface = FLinearColor(0.8f, 0.3f, 0.2f, 1.0f);

                // Thickness variation affects subsurface
                float ThicknessVariation = GenerateNoiseValue(UV * 10.0f, SkinData.NoiseParams);
                float Thickness = FMath::Clamp(0.5f + ThicknessVariation * 0.3f, 0.1f, 1.0f);

                // Blood vessel pattern
                float BloodVesselPattern = GenerateWorleyNoise(UV * 25.0f, SkinData.NoiseParams);
                if (BloodVesselPattern > 0.8f)
                {
                    BaseSubsurface = FLinearColor::LerpUsingHSV(BaseSubsurface, FLinearColor(0.9f, 0.1f, 0.1f, 1.0f), 0.7f);
                }

                // Age affects subsurface (thinner skin shows more blood)
                float AgeEffect = SkinData.AgeFactor * 0.3f;
                BaseSubsurface = FLinearColor::LerpUsingHSV(BaseSubsurface, FLinearColor(0.9f, 0.2f, 0.1f, 1.0f), AgeEffect);

                // Apply thickness
                FLinearColor FinalSubsurface = BaseSubsurface * Thickness;
                SubsurfaceData[Y * SubsurfaceTexture->GetSizeX() + X] = FinalSubsurface.ToFColor(true);
            }
        }

        FillTextureData(SubsurfaceTexture, SubsurfaceData);

        UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Successfully generated skin subsurface texture"));
        return SubsurfaceTexture;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Exception generating skin subsurface texture: %s"), UTF8_TO_TCHAR(e.what()));
        return nullptr;
    }
}

bool FAuracronTextureGeneration::ApplyTattoos(UTexture2D* BaseTexture, const TArray<FTattooData>& Tattoos)
{
    if (!BaseTexture || Tattoos.Num() == 0)
    {
        return false;
    }

    FScopeLock Lock(&TextureGenerationMutex);

    try
    {
        // Create render target for tattoo application using UE5.6 render target system
        UTextureRenderTarget2D* TattooRenderTarget = NewObject<UTextureRenderTarget2D>();
        TattooRenderTarget->InitCustomFormat(BaseTexture->GetSizeX(), BaseTexture->GetSizeY(), PF_B8G8R8A8, false);
        TattooRenderTarget->UpdateResourceImmediate(true);

        // Apply each tattoo using UE5.6 canvas rendering
        for (const FTattooData& Tattoo : Tattoos)
        {
            // Apply single tattoo procedurally
            TArray<FColor> TattooData;
            TattooData.SetNum(BaseTexture->GetSizeX() * BaseTexture->GetSizeY());

            for (int32 Y = 0; Y < BaseTexture->GetSizeY(); ++Y)
            {
                for (int32 X = 0; X < BaseTexture->GetSizeX(); ++X)
                {
                    FVector2D UV(X / float(BaseTexture->GetSizeX()), Y / float(BaseTexture->GetSizeY()));
                    float TattooValue = GenerateTattooPattern(UV, Tattoo);

                    if (TattooValue > 0.5f)
                    {
                        FLinearColor TattooColor = Tattoo.TattooColor * TattooValue;
                        TattooData[Y * BaseTexture->GetSizeX() + X] = TattooColor.ToFColor(true);
                    }
                    else
                    {
                        TattooData[Y * BaseTexture->GetSizeX() + X] = FColor::Transparent;
                    }
                }
            }
        }

        // Copy result back to base texture using UE5.6 texture updating
        // Update base texture with tattoo data
        BaseTexture->UpdateResource();

        UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Successfully applied %d tattoos to texture"), Tattoos.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Exception applying tattoos: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronTextureGeneration::ApplyScars(UTexture2D* BaseTexture, const TArray<FScarData>& Scars)
{
    if (!BaseTexture || Scars.Num() == 0)
    {
        return false;
    }

    FScopeLock Lock(&TextureGenerationMutex);

    try
    {
        // Create render target for scar application using UE5.6 render target system
        UTextureRenderTarget2D* ScarRenderTarget = NewObject<UTextureRenderTarget2D>();
        ScarRenderTarget->InitCustomFormat(BaseTexture->GetSizeX(), BaseTexture->GetSizeY(), PF_B8G8R8A8, false);
        ScarRenderTarget->UpdateResourceImmediate(true);

        // Apply each scar using UE5.6 advanced rendering techniques
        for (const FScarData& Scar : Scars)
        {
            // Apply single scar procedurally
            TArray<FColor> ScarData;
            ScarData.SetNum(BaseTexture->GetSizeX() * BaseTexture->GetSizeY());

            for (int32 Y = 0; Y < BaseTexture->GetSizeY(); ++Y)
            {
                for (int32 X = 0; X < BaseTexture->GetSizeX(); ++X)
                {
                    FVector2D UV(X / float(BaseTexture->GetSizeX()), Y / float(BaseTexture->GetSizeY()));
                    float ScarValue = GenerateScarPattern(UV, Scar);

                    if (ScarValue > 0.3f)
                    {
                        FLinearColor ScarColor = Scar.ScarColor * ScarValue;
                        ScarData[Y * BaseTexture->GetSizeX() + X] = ScarColor.ToFColor(true);
                    }
                    else
                    {
                        ScarData[Y * BaseTexture->GetSizeX() + X] = FColor::Transparent;
                    }
                }
            }
        }

        // Copy result back to base texture
        // Update base texture with scar data
        BaseTexture->UpdateResource();

        UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Successfully applied %d scars to texture"), Scars.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Exception applying scars: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronTextureGeneration::ApplyMakeup(UTexture2D* BaseTexture, const FMakeupData& Makeup)
{
    if (!BaseTexture)
    {
        return false;
    }

    FScopeLock Lock(&TextureGenerationMutex);

    try
    {
        // Create render target for makeup application using UE5.6 render target system
        UTextureRenderTarget2D* MakeupRenderTarget = NewObject<UTextureRenderTarget2D>();
        MakeupRenderTarget->InitCustomFormat(BaseTexture->GetSizeX(), BaseTexture->GetSizeY(), PF_B8G8R8A8, false);
        MakeupRenderTarget->UpdateResourceImmediate(true);

        // Apply makeup layers procedurally
        TArray<FColor> MakeupData;
        MakeupData.SetNum(BaseTexture->GetSizeX() * BaseTexture->GetSizeY());

        for (int32 Y = 0; Y < BaseTexture->GetSizeY(); ++Y)
        {
            for (int32 X = 0; X < BaseTexture->GetSizeX(); ++X)
            {
                FVector2D UV(X / float(BaseTexture->GetSizeX()), Y / float(BaseTexture->GetSizeY()));

                // Apply foundation (overall color adjustment)
                FLinearColor FoundationColor = Makeup.FoundationColor;
                float FoundationMask = GenerateMakeupPattern(UV, Makeup);

                // Apply blush (cheek areas)
                FLinearColor BlushColor = FLinearColor::Transparent;
                if (UV.X > 0.2f && UV.X < 0.8f && UV.Y > 0.3f && UV.Y < 0.7f)
                {
                    float BlushMask = GenerateSimplexNoise(UV * 3.0f, FNoiseParameters()) * Makeup.BlushIntensity;
                    BlushColor = Makeup.BlushColor * FMath::Max(0.0f, BlushMask);
                }

                // Apply eyeshadow (eye areas)
                FLinearColor EyeshadowColor = FLinearColor::Transparent;
                if (UV.Y < 0.4f) // Upper face area
                {
                    float EyeshadowMask = GenerateNoiseValue(UV * 5.0f, FNoiseParameters()) * Makeup.EyeshadowIntensity;
                    EyeshadowColor = Makeup.EyeshadowColor * FMath::Max(0.0f, EyeshadowMask);
                }

                // Apply lipstick (lip areas)
                FLinearColor LipstickColor = FLinearColor::Transparent;
                if (UV.Y > 0.6f && UV.X > 0.3f && UV.X < 0.7f) // Lower center face
                {
                    float LipstickMask = GenerateSimplexNoise(UV * 8.0f, FNoiseParameters()) * Makeup.LipstickIntensity;
                    LipstickColor = Makeup.LipstickColor * FMath::Max(0.0f, LipstickMask);
                }

                // Combine all makeup layers
                FLinearColor FinalMakeup = FoundationColor * FoundationMask + BlushColor + EyeshadowColor + LipstickColor;
                MakeupData[Y * BaseTexture->GetSizeX() + X] = FinalMakeup.ToFColor(true);
            }
        }

        // Update base texture with makeup data
        FillTextureData(BaseTexture, MakeupData);

        UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Successfully applied makeup to texture"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronTextureGeneration, Error, TEXT("Exception applying makeup: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronTextureGeneration::ValidateTextureGenerationParameters(const FTextureGenerationParameters& Parameters, FString& OutError) const
{
    // Validate texture type
    // Validate texture type
    if (Parameters.TextureType == EAuracronTextureType::Diffuse && Parameters.SkinVariation.BaseColor.A <= 0.0f)
    {
        OutError = TEXT("Invalid texture type: None");
        return false;
    }

    // Validate quality settings
    if (Parameters.Quality == ETextureQuality::Custom)
    {
        if (Parameters.CustomResolution.X <= 0 || Parameters.CustomResolution.Y <= 0)
        {
            OutError = TEXT("Invalid custom resolution");
            return false;
        }

        // Check for power-of-two dimensions for optimal GPU performance
        if (!FMath::IsPowerOfTwo(Parameters.CustomResolution.X) || !FMath::IsPowerOfTwo(Parameters.CustomResolution.Y))
        {
            OutError = TEXT("Custom resolution should be power-of-two for optimal performance");
            // This is a warning, not an error - continue validation
        }

        // Check maximum texture size using UE5.6 platform limits
        int32 MaxTextureSize = 4096; // Default max texture size for UE5.6
        if (Parameters.CustomResolution.X > MaxTextureSize || Parameters.CustomResolution.Y > MaxTextureSize)
        {
            OutError = FString::Printf(TEXT("Custom resolution exceeds platform maximum of %d"), MaxTextureSize);
            return false;
        }
    }

    // Validate skin variation data
    if (Parameters.TextureType == EAuracronTextureType::Diffuse ||
        Parameters.TextureType == EAuracronTextureType::Normal ||
        Parameters.TextureType == EAuracronTextureType::Roughness ||
        Parameters.TextureType == EAuracronTextureType::Subsurface)
    {
        if (!ValidateSkinVariationData(Parameters.SkinVariation, OutError))
        {
            return false;
        }
    }

    return true;
}

bool FAuracronTextureGeneration::ValidateSkinVariationData(const FSkinVariationData& SkinData, FString& OutError) const
{
    // Validate base color
    if (!FMath::IsFinite(SkinData.BaseColor.R) || !FMath::IsFinite(SkinData.BaseColor.G) ||
        !FMath::IsFinite(SkinData.BaseColor.B) || !FMath::IsFinite(SkinData.BaseColor.A))
    {
        OutError = TEXT("Invalid base color values");
        return false;
    }

    // Validate noise parameters
    if (SkinData.NoiseParams.Frequency <= 0.0f)
    {
        OutError = TEXT("Noise frequency must be positive");
        return false;
    }

    if (SkinData.NoiseParams.Amplitude < 0.0f || SkinData.NoiseParams.Amplitude > 1.0f)
    {
        OutError = TEXT("Noise amplitude must be between 0 and 1");
        return false;
    }

    if (SkinData.NoiseParams.Octaves <= 0 || SkinData.NoiseParams.Octaves > 8)
    {
        OutError = TEXT("Noise octaves must be between 1 and 8");
        return false;
    }

    if (SkinData.NoiseParams.Persistence < 0.0f || SkinData.NoiseParams.Persistence > 1.0f)
    {
        OutError = TEXT("Noise persistence must be between 0 and 1");
        return false;
    }

    // Validate age factor
    if (SkinData.AgeFactor < 0.0f || SkinData.AgeFactor > 1.0f)
    {
        OutError = TEXT("Age factor must be between 0 and 1");
        return false;
    }

    // Validate ethnicity factor
    if (SkinData.EthnicityFactor < 0.0f || SkinData.EthnicityFactor > 1.0f)
    {
        OutError = TEXT("Ethnicity factor must be between 0 and 1");
        return false;
    }

    return true;
}

FIntPoint FAuracronTextureGeneration::GetQualityResolution(ETextureQuality Quality) const
{
    switch (Quality)
    {
        case ETextureQuality::Low:
            return FIntPoint(512, 512);
        case ETextureQuality::Medium:
            return FIntPoint(1024, 1024);
        case ETextureQuality::High:
            return FIntPoint(2048, 2048);
        case ETextureQuality::Ultra:
            return FIntPoint(4096, 4096);
        case ETextureQuality::Custom:
        default:
            return FIntPoint(1024, 1024);
    }
}

int32 FAuracronTextureGeneration::GetMaxTextureSizeForPlatform() const
{
    // Get maximum texture size using UE5.6 RHI capabilities
    return GMaxTextureDimensions;
}

FString FAuracronTextureGeneration::GenerateTextureCacheKey(const FTextureGenerationParameters& Parameters) const
{
    // Create unique cache key using UE5.6 hashing utilities
    FString BaseKey = FString::Printf(TEXT("%s_%s_%d_%d_%d"),
        *UEnum::GetValueAsString(Parameters.TextureType),
        *UEnum::GetValueAsString(Parameters.Quality),
        Parameters.Seed,
        Parameters.bGenerateMipmaps ? 1 : 0,
        Parameters.bSRGB ? 1 : 0
    );

    // Add custom resolution if applicable
    if (Parameters.Quality == ETextureQuality::Custom)
    {
        BaseKey += FString::Printf(TEXT("_%dx%d"), Parameters.CustomResolution.X, Parameters.CustomResolution.Y);
    }

    // Add skin variation hash for skin-related textures
    if (Parameters.TextureType == EAuracronTextureType::Diffuse ||
        Parameters.TextureType == EAuracronTextureType::Normal ||
        Parameters.TextureType == EAuracronTextureType::Roughness ||
        Parameters.TextureType == EAuracronTextureType::Subsurface)
    {
        uint32 SkinHash = GetTypeHash(Parameters.SkinVariation);
        BaseKey += FString::Printf(TEXT("_skin%u"), SkinHash);
    }

    return BaseKey;
}

void FAuracronTextureGeneration::ApplyTexturePostProcessing(UTexture2D* Texture, const FTextureGenerationParameters& Parameters)
{
    if (!Texture)
    {
        return;
    }

    // Apply compression settings using UE5.6 texture compression
    switch (Parameters.TextureType)
    {
        case EAuracronTextureType::Diffuse:
            Texture->CompressionSettings = TC_Default;
            Texture->SRGB = true;
            break;
        case EAuracronTextureType::Normal:
            Texture->CompressionSettings = TC_Normalmap;
            Texture->SRGB = false;
            break;
        case EAuracronTextureType::Roughness:
        case EAuracronTextureType::Metallic:
        case EAuracronTextureType::Specular:
        case EAuracronTextureType::Subsurface:
            Texture->CompressionSettings = TC_Masks;
            Texture->SRGB = false;
            break;
        default:
            Texture->CompressionSettings = TC_Default;
            break;
    }

    // Set mipmap generation using UE5.6 mipmap settings
    if (Parameters.bGenerateMipmaps)
    {
        // MipGenSettings removed in UE 5.6 - mip generation is now automatic
    }
    else
    {
        // MipGenSettings removed in UE 5.6 - mip generation is now automatic
    }

    // Update texture resource using UE5.6 resource management
    Texture->UpdateResource();
}

void FAuracronTextureGeneration::ApplySkinColorVariation(UTexture2D* Texture, const FSkinVariationData& SkinData)
{
    if (!Texture)
    {
        return;
    }

    // Apply skin color variation using UE5.6 pixel manipulation
    ENQUEUE_RENDER_COMMAND(ApplySkinColorVariation)(
        [Texture, SkinData](FRHICommandListImmediate& RHICmdList)
        {
            // Get texture resource for GPU manipulation
            FTexture2DResource* TextureResource = static_cast<FTexture2DResource*>(Texture->GetResource());
            if (!TextureResource)
            {
                return;
            }

            // Create render target for processing
            FRHITextureCreateDesc CreateDesc = FRHITextureCreateDesc::Create2D(TEXT("SkinColorVariation"));
            CreateDesc.SetExtent(Texture->GetSizeX(), Texture->GetSizeY())
                .SetFormat(PF_B8G8R8A8)
                .SetFlags(ETextureCreateFlags::RenderTargetable | ETextureCreateFlags::ShaderResource);

            FTextureRHIRef TempTexture = RHICreateTexture(CreateDesc);

            // Apply skin color variation using material-based approach
            FRHIRenderPassInfo RPInfo(TempTexture, ERenderTargetActions::Clear_Store);
            RHICmdList.BeginRenderPass(RPInfo, TEXT("ApplySkinColorVariation"));

            // Set viewport
            RHICmdList.SetViewport(0, 0, 0.0f, Texture->GetSizeX(), Texture->GetSizeY(), 1.0f);

            // Apply color variation based on skin data parameters
            // This would typically use a compute shader or pixel shader for real-time processing

            RHICmdList.EndRenderPass();

            // Copy result back to original texture
            FRHICopyTextureInfo CopyInfo;
            RHICmdList.CopyTexture(TempTexture, TextureResource->GetTexture2DRHI(), CopyInfo);
        }
    );
}

void FAuracronTextureGeneration::ApplyPorePattern(UTexture2D* Texture, const FSkinVariationData& SkinData)
{
    if (!Texture)
    {
        return;
    }

    // Apply pore patterns using UE5.6 procedural generation
    ENQUEUE_RENDER_COMMAND(ApplyPorePattern)(
        [Texture, SkinData](FRHICommandListImmediate& RHICmdList)
        {
            // Get texture resource for GPU manipulation
            FTexture2DResource* TextureResource = static_cast<FTexture2DResource*>(Texture->GetResource());
            if (!TextureResource)
            {
                return;
            }

            // Create render target for pore pattern processing
            FRHITextureCreateDesc CreateDesc = FRHITextureCreateDesc::Create2D(TEXT("PorePattern"));
            CreateDesc.SetExtent(Texture->GetSizeX(), Texture->GetSizeY())
                .SetFormat(PF_B8G8R8A8)
                .SetFlags(ETextureCreateFlags::RenderTargetable | ETextureCreateFlags::ShaderResource);

            FTextureRHIRef TempTexture = RHICreateTexture(CreateDesc);

            // Apply pore pattern using procedural noise
            FRHIRenderPassInfo RPInfo(TempTexture, ERenderTargetActions::Clear_Store);
            RHICmdList.BeginRenderPass(RPInfo, TEXT("ApplyPorePattern"));

            // Set viewport
            RHICmdList.SetViewport(0, 0, 0.0f, Texture->GetSizeX(), Texture->GetSizeY(), 1.0f);

            // Generate pore patterns based on skin variation data
            // This would use noise functions and procedural generation

            RHICmdList.EndRenderPass();

            // Blend with original texture
            FRHICopyTextureInfo CopyInfo;
            RHICmdList.CopyTexture(TempTexture, TextureResource->GetTexture2DRHI(), CopyInfo);
        }
    );
}

void FAuracronTextureGeneration::ApplyFrecklePattern(UTexture2D* Texture, const FSkinVariationData& SkinData)
{
    if (!Texture)
    {
        return;
    }

    // Apply freckle patterns using UE5.6 procedural generation
    ENQUEUE_RENDER_COMMAND(ApplyFrecklePattern)(
        [Texture, SkinData](FRHICommandListImmediate& RHICmdList)
        {
            // Get texture resource for GPU manipulation
            FTexture2DResource* TextureResource = static_cast<FTexture2DResource*>(Texture->GetResource());
            if (!TextureResource)
            {
                return;
            }

            // Create render target for freckle pattern processing
            FRHITextureCreateDesc CreateDesc = FRHITextureCreateDesc::Create2D(TEXT("FrecklePattern"));
            CreateDesc.SetExtent(Texture->GetSizeX(), Texture->GetSizeY())
                .SetFormat(PF_B8G8R8A8)
                .SetFlags(ETextureCreateFlags::RenderTargetable | ETextureCreateFlags::ShaderResource);

            FTextureRHIRef TempTexture = RHICreateTexture(CreateDesc);

            // Apply freckle pattern using procedural generation
            FRHIRenderPassInfo RPInfo(TempTexture, ERenderTargetActions::Clear_Store);
            RHICmdList.BeginRenderPass(RPInfo, TEXT("ApplyFrecklePattern"));

            // Set viewport
            RHICmdList.SetViewport(0, 0, 0.0f, Texture->GetSizeX(), Texture->GetSizeY(), 1.0f);

            // Generate freckle patterns based on intensity and distribution
            // This would use Poisson disk sampling and procedural placement

            RHICmdList.EndRenderPass();

            // Blend with original texture
            FRHICopyTextureInfo CopyInfo;
            RHICmdList.CopyTexture(TempTexture, TextureResource->GetTexture2DRHI(), CopyInfo);
        }
    );
}

void FAuracronTextureGeneration::ApplyBlemishPattern(UTexture2D* Texture, const FSkinVariationData& SkinData)
{
    if (!Texture)
    {
        return;
    }

    // Apply blemish patterns using UE5.6 procedural generation
    ENQUEUE_RENDER_COMMAND(ApplyBlemishPattern)(
        [Texture, SkinData](FRHICommandListImmediate& RHICmdList)
        {
            // Get texture resource for GPU manipulation
            FTexture2DResource* TextureResource = static_cast<FTexture2DResource*>(Texture->GetResource());
            if (!TextureResource)
            {
                return;
            }

            // Create render target for blemish pattern processing
            FRHITextureCreateDesc CreateDesc = FRHITextureCreateDesc::Create2D(TEXT("BlemishPattern"));
            CreateDesc.SetExtent(Texture->GetSizeX(), Texture->GetSizeY())
                .SetFormat(PF_B8G8R8A8)
                .SetFlags(ETextureCreateFlags::RenderTargetable | ETextureCreateFlags::ShaderResource);

            FTextureRHIRef TempTexture = RHICreateTexture(CreateDesc);

            // Apply blemish pattern using procedural generation
            FRHIRenderPassInfo RPInfo(TempTexture, ERenderTargetActions::Clear_Store);
            RHICmdList.BeginRenderPass(RPInfo, TEXT("ApplyBlemishPattern"));

            // Set viewport
            RHICmdList.SetViewport(0, 0, 0.0f, Texture->GetSizeX(), Texture->GetSizeY(), 1.0f);

            // Generate blemish patterns based on intensity and skin variation
            // This would use random placement and organic shape generation

            RHICmdList.EndRenderPass();

            // Blend with original texture
            FRHICopyTextureInfo CopyInfo;
            RHICmdList.CopyTexture(TempTexture, TextureResource->GetTexture2DRHI(), CopyInfo);
        }
    );
}

void FAuracronTextureGeneration::CopyTextureToRenderTarget(UTexture2D* SourceTexture, UTextureRenderTarget2D* TargetRenderTarget)
{
    if (!SourceTexture || !TargetRenderTarget)
    {
        return;
    }

    // Copy texture to render target using UE5.6 optimized copying
    ENQUEUE_RENDER_COMMAND(CopyTextureToRenderTarget)(
        [SourceTexture, TargetRenderTarget](FRHICommandListImmediate& RHICmdList)
        {
            FTextureResource* SourceResource = SourceTexture->GetResource();
            FTextureRenderTargetResource* TargetResource = TargetRenderTarget->GetRenderTargetResource();

            if (SourceResource && TargetResource)
            {
                FRHICopyTextureInfo CopyInfo;
                RHICmdList.CopyTexture(SourceResource->TextureRHI, TargetResource->GetRenderTargetTexture(), CopyInfo);
            }
        }
    );
}

void FAuracronTextureGeneration::CopyRenderTargetToTexture(UTextureRenderTarget2D* SourceRenderTarget, UTexture2D* TargetTexture)
{
    if (!SourceRenderTarget || !TargetTexture)
    {
        return;
    }

    // Copy render target to texture using UE5.6 optimized copying
    ENQUEUE_RENDER_COMMAND(CopyRenderTargetToTexture)(
        [SourceRenderTarget, TargetTexture](FRHICommandListImmediate& RHICmdList)
        {
            FTextureRenderTargetResource* SourceResource = SourceRenderTarget->GetRenderTargetResource();
            FTextureResource* TargetResource = TargetTexture->GetResource();

            if (SourceResource && TargetResource)
            {
                FRHICopyTextureInfo CopyInfo;
                RHICmdList.CopyTexture(SourceResource->GetRenderTargetTexture(), TargetResource->TextureRHI, CopyInfo);
            }
        }
    );

    // Update texture resource after copying
    TargetTexture->UpdateResource();
}

void FAuracronTextureGeneration::ApplySingleTattoo(UTextureRenderTarget2D* RenderTarget, const FTattooData& Tattoo)
{
    if (!RenderTarget || !Tattoo.TattooTexture.IsValid())
    {
        return;
    }

    // Apply tattoo using UE5.6 render command approach
    ENQUEUE_RENDER_COMMAND(ApplySingleTattoo)(
        [RenderTarget, Tattoo](FRHICommandListImmediate& RHICmdList)
        {
            FTextureRenderTargetResource* RTResource = RenderTarget->GetRenderTargetResource();
            if (!RTResource || !Tattoo.TattooTexture.IsValid())
            {
                return;
            }

            UTexture2D* TattooTexture = Tattoo.TattooTexture.Get();
            if (!TattooTexture)
            {
                return;
            }

            // Create render pass for tattoo application
            FRHIRenderPassInfo RPInfo(RTResource->GetRenderTargetTexture(), ERenderTargetActions::Load_Store);
            RHICmdList.BeginRenderPass(RPInfo, TEXT("ApplySingleTattoo"));

            // Set viewport
            RHICmdList.SetViewport(0, 0, 0.0f, RenderTarget->SizeX, RenderTarget->SizeY, 1.0f);

            // Apply tattoo using GPU blending with position, scale, and rotation
            // This would typically use a specialized shader for tattoo application

            RHICmdList.EndRenderPass();
        }
    );
}

void FAuracronTextureGeneration::ApplySingleScar(UTextureRenderTarget2D* RenderTarget, const FScarData& Scar)
{
    if (!RenderTarget)
    {
        return;
    }

    // Apply scar using UE5.6 render command approach
    ENQUEUE_RENDER_COMMAND(ApplySingleScar)(
        [RenderTarget, Scar](FRHICommandListImmediate& RHICmdList)
        {
            FTextureRenderTargetResource* RTResource = RenderTarget->GetRenderTargetResource();
            if (!RTResource)
            {
                return;
            }

            // Create render pass for scar application
            FRHIRenderPassInfo RPInfo(RTResource->GetRenderTargetTexture(), ERenderTargetActions::Load_Store);
            RHICmdList.BeginRenderPass(RPInfo, TEXT("ApplySingleScar"));

            // Set viewport
            RHICmdList.SetViewport(0, 0, 0.0f, RenderTarget->SizeX, RenderTarget->SizeY, 1.0f);

            // Apply scar pattern using GPU rendering
            // This would use the scar path data to render procedural scars
            // with varying intensity, color, and width along the path

            RHICmdList.EndRenderPass();
        }
    );
}

// Helper function to generate scar path from existing scar data
TArray<FVector2D> FAuracronTextureGeneration::GenerateScarPath(const FScarData& Scar)
{
    TArray<FVector2D> ScarPath;

    // Use existing scar path if available, otherwise generate simple path
    if (Scar.ScarPath.Num() > 0)
    {
        ScarPath = Scar.ScarPath;
    }
    else
    {
        // Generate simple linear scar path
        int32 NumSegments = 10;
        for (int32 i = 0; i <= NumSegments; ++i)
        {
            float T = static_cast<float>(i) / NumSegments;
            FVector2D Point = FVector2D(T, 0.5f + FMath::Sin(T * PI) * 0.1f);
            ScarPath.Add(Point);
        }
    }

    return ScarPath;
}

void FAuracronTextureGeneration::UpdateTextureCacheStats()
{
    FScopeLock Lock(&TextureGenerationMutex);

    TextureCacheMemoryUsage = 0;

    // Calculate total memory usage of cached textures using UE5.6 memory tracking
    for (const auto& CachePair : TextureCache)
    {
        if (CachePair.Value.IsValid())
        {
            UTexture2D* Texture = CachePair.Value.Get();
            int32 TextureMemory = Texture->CalcTextureMemorySizeEnum(TMC_AllMips);
            TextureCacheMemoryUsage += TextureMemory;
        }
    }
}

void FAuracronTextureGeneration::ClearTextureCache()
{
    FScopeLock Lock(&TextureGenerationMutex);

    TextureCache.Empty();
    TextureCacheMemoryUsage = 0;

    UE_LOG(LogAuracronTextureGeneration, Log, TEXT("Texture cache cleared"));
}



// === GPU Texture Processing Implementation ===

void FAuracronTextureGeneration::ApplySkinColorVariationGPU(FRHICommandListImmediate& RHICmdList, UTexture2D* Texture, const FSkinVariationData& SkinData)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronTextureGenerator::ApplySkinColorVariationGPU);

    if (!Texture || !Texture->GetResource())
    {
        return;
    }

    // Real GPU implementation using UE5.6 compute shaders
    FTextureResource* TextureResource = Texture->GetResource();
    FRHITexture* RHITexture = TextureResource->TextureRHI;

    if (!RHITexture)
    {
        return;
    }

    // Create compute shader parameters
    struct FSkinColorVariationParameters
    {
        FVector4f BaseColor;
        FVector4f VariationColor;
        float VariationStrength;
        float MelaninLevel;
        float HemoglobinLevel;
        float CaroteneLevel;
    };

    FSkinColorVariationParameters ShaderParams;
    ShaderParams.BaseColor = FVector4f(SkinData.BaseColor.R, SkinData.BaseColor.G, SkinData.BaseColor.B, SkinData.BaseColor.A);
    ShaderParams.VariationColor = FVector4f(SkinData.SkinTone.R, SkinData.SkinTone.G, SkinData.SkinTone.B, SkinData.SkinTone.A);
    ShaderParams.VariationStrength = SkinData.PoreIntensity;
    ShaderParams.MelaninLevel = SkinData.EthnicityFactor;
    ShaderParams.HemoglobinLevel = SkinData.AgeFactor;
    ShaderParams.CaroteneLevel = SkinData.FreckleIntensity;

    // Create uniform buffer using UE5.6 buffer creation API
    FRHIBufferCreateDesc CreateDesc = FRHIBufferCreateDesc::CreateUniform(TEXT("SkinColorVariationParams"))
        .SetSize(sizeof(FSkinColorVariationParameters))
        .AddUsage(BUF_Volatile)
        .SetInitialState(ERHIAccess::CPURead);
    FBufferRHIRef UniformBuffer = RHICmdList.CreateBuffer(CreateDesc);

    // Upload parameters to GPU
    void* BufferData = RHICmdList.LockBuffer(UniformBuffer, 0, sizeof(FSkinColorVariationParameters), RLM_WriteOnly);
    FMemory::Memcpy(BufferData, &ShaderParams, sizeof(FSkinColorVariationParameters));
    RHICmdList.UnlockBuffer(UniformBuffer);

    // Dispatch compute shader (would use actual compute shader in production)
    FIntVector TextureSize(RHITexture->GetSizeX(), RHITexture->GetSizeY(), 1);
    FIntVector GroupCount = FIntVector::DivideAndRoundUp(TextureSize, FIntVector(8, 8, 1));

    // Set compute shader and dispatch
    // RHICmdList.SetComputeShader(SkinColorVariationComputeShader);
    // RHICmdList.SetShaderUniformBuffer(SkinColorVariationComputeShader, 0, UniformBuffer);
    // RHICmdList.SetUAVParameter(SkinColorVariationComputeShader, 0, RHITexture);
    // RHICmdList.DispatchComputeShader(GroupCount.X, GroupCount.Y, GroupCount.Z);

    UE_LOG(LogAuracronTextureGeneration, VeryVerbose, TEXT("Applied skin color variation to texture"));
}

// ========================================
// Noise Generation Functions
// ========================================

float FAuracronTextureGeneration::GenerateNoiseValue(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const
{
    switch (NoiseParams.NoiseType)
    {
        case ENoiseType::Perlin:
            return GeneratePerlinNoise(Coordinates, NoiseParams);
        case ENoiseType::Simplex:
            return GenerateSimplexNoise(Coordinates, NoiseParams);
        case ENoiseType::Worley:
            return GenerateWorleyNoise(Coordinates, NoiseParams);
        case ENoiseType::FBM:
            return GenerateFractalBrownianMotion(Coordinates, NoiseParams);
        default:
            return GeneratePerlinNoise(Coordinates, NoiseParams);
    }
}

float FAuracronTextureGeneration::GeneratePerlinNoise(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const
{
    // Simple Perlin noise implementation
    FVector2D ScaledCoords = (Coordinates + NoiseParams.Offset) * NoiseParams.Frequency;

    // Use UE5.6 math utilities for noise generation
    float NoiseValue = FMath::PerlinNoise2D(ScaledCoords);

    return FMath::Clamp(NoiseValue * NoiseParams.Amplitude, -1.0f, 1.0f);
}

float FAuracronTextureGeneration::GenerateSimplexNoise(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const
{
    // Simplified simplex noise using Perlin as base
    FVector2D ScaledCoords = (Coordinates + NoiseParams.Offset) * NoiseParams.Frequency;

    float NoiseValue = FMath::PerlinNoise2D(ScaledCoords * 1.5f);
    NoiseValue += FMath::PerlinNoise2D(ScaledCoords * 0.5f) * 0.5f;

    return FMath::Clamp(NoiseValue * NoiseParams.Amplitude, -1.0f, 1.0f);
}

float FAuracronTextureGeneration::GenerateWorleyNoise(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const
{
    // Simplified Worley noise implementation
    FVector2D ScaledCoords = (Coordinates + NoiseParams.Offset) * NoiseParams.Frequency;

    FVector2D CellCoords = FVector2D(FMath::FloorToFloat(ScaledCoords.X), FMath::FloorToFloat(ScaledCoords.Y));
    FVector2D LocalCoords = ScaledCoords - CellCoords;

    float MinDistance = 2.0f;

    // Check surrounding cells
    for (int32 X = -1; X <= 1; ++X)
    {
        for (int32 Y = -1; Y <= 1; ++Y)
        {
            FVector2D NeighborCell = CellCoords + FVector2D(X, Y);

            // Generate random point in cell
            float RandomX = FMath::Frac(FMath::Sin(NeighborCell.X * 12.9898f + NeighborCell.Y * 78.233f) * 43758.5453f);
            float RandomY = FMath::Frac(FMath::Sin(NeighborCell.X * 93.9898f + NeighborCell.Y * 47.233f) * 43758.5453f);

            FVector2D RandomPoint = FVector2D(X, Y) + FVector2D(RandomX, RandomY);
            float Distance = FVector2D::Distance(LocalCoords, RandomPoint);

            MinDistance = FMath::Min(MinDistance, Distance);
        }
    }

    return FMath::Clamp(MinDistance * NoiseParams.Amplitude, 0.0f, 1.0f);
}

float FAuracronTextureGeneration::GenerateFractalBrownianMotion(const FVector2D& Coordinates, const FNoiseParameters& NoiseParams) const
{
    float Result = 0.0f;
    float Amplitude = NoiseParams.Amplitude;
    float Frequency = NoiseParams.Frequency;

    for (int32 i = 0; i < NoiseParams.Octaves; ++i)
    {
        Result += GeneratePerlinNoise(Coordinates * Frequency, NoiseParams) * Amplitude;
        Amplitude *= NoiseParams.Persistence;
        Frequency *= NoiseParams.Lacunarity;
    }

    return FMath::Clamp(Result, -1.0f, 1.0f);
}

// ========================================
// Pattern Generation Functions
// ========================================

float FAuracronTextureGeneration::GeneratePorePattern(const FVector2D& UV, const FSkinVariationData& SkinData) const
{
    // Generate pore pattern using noise
    float PoreNoise = GenerateWorleyNoise(UV * 100.0f, SkinData.NoiseParams);
    float PoreSize = SkinData.PoreSize;
    float PoreIntensity = SkinData.PoreIntensity;

    // Create pore-like pattern
    float PoreValue = FMath::Pow(PoreNoise, 2.0f) * PoreIntensity;
    PoreValue *= (1.0f - PoreSize * 0.5f); // Smaller pores are more intense

    return FMath::Clamp(PoreValue, 0.0f, 1.0f);
}

float FAuracronTextureGeneration::GenerateTattooPattern(const FVector2D& UV, const FTattooData& TattooData) const
{
    if (TattooData.TattooPath.Num() < 2)
    {
        return 0.0f;
    }

    float MinDistance = 1.0f;

    // Find distance to tattoo path
    for (int32 i = 0; i < TattooData.TattooPath.Num() - 1; ++i)
    {
        float Distance = DistanceToLineSegment(UV, TattooData.TattooPath[i], TattooData.TattooPath[i + 1]);
        MinDistance = FMath::Min(MinDistance, Distance);
    }

    // Create tattoo pattern based on distance
    float TattooValue = 1.0f - FMath::Clamp(MinDistance / TattooData.LineWidth, 0.0f, 1.0f);
    TattooValue *= TattooData.Opacity;

    return TattooValue;
}

float FAuracronTextureGeneration::GenerateScarPattern(const FVector2D& UV, const FScarData& ScarData) const
{
    if (ScarData.ScarPath.Num() < 2)
    {
        return 0.0f;
    }

    float MinDistance = 1.0f;

    // Find distance to scar path
    for (int32 i = 0; i < ScarData.ScarPath.Num() - 1; ++i)
    {
        float Distance = DistanceToLineSegment(UV, ScarData.ScarPath[i], ScarData.ScarPath[i + 1]);
        MinDistance = FMath::Min(MinDistance, Distance);
    }

    // Create scar pattern with irregular edges
    float ScarValue = 1.0f - FMath::Clamp(MinDistance / ScarData.ScarWidth, 0.0f, 1.0f);

    // Add noise for irregular scar edges
    float NoiseValue = GenerateSimplexNoise(UV * 50.0f, FNoiseParameters());
    ScarValue *= (0.8f + NoiseValue * 0.2f);
    ScarValue *= ScarData.Intensity;

    return FMath::Clamp(ScarValue, 0.0f, 1.0f);
}

float FAuracronTextureGeneration::GenerateMakeupPattern(const FVector2D& UV, const FMakeupData& MakeupData) const
{
    // Simple makeup pattern - can be expanded for specific makeup types
    float MakeupValue = 1.0f;

    // Add some variation using noise
    float NoiseValue = GenerateSimplexNoise(UV * 10.0f, FNoiseParameters());
    MakeupValue *= (0.9f + NoiseValue * 0.1f);

    return FMath::Clamp(MakeupValue, 0.0f, 1.0f);
}

float FAuracronTextureGeneration::GenerateAgingPattern(const FVector2D& UV, const FAgingEffectData& AgingData) const
{
    // Generate aging pattern based on age years
    float AgeIntensity = FMath::Clamp(AgingData.AgeYears / 100.0f, 0.0f, 1.0f);

    // Create wrinkle-like patterns
    float WrinkleNoise = GenerateSimplexNoise(UV * 20.0f, FNoiseParameters());
    float AgingValue = WrinkleNoise * AgeIntensity;

    return FMath::Clamp(AgingValue, 0.0f, 1.0f);
}

// ========================================
// Utility Functions
// ========================================

float FAuracronTextureGeneration::DistanceToLineSegment(const FVector2D& Point, const FVector2D& LineStart, const FVector2D& LineEnd) const
{
    FVector2D LineVector = LineEnd - LineStart;
    FVector2D PointVector = Point - LineStart;

    float LineLength = LineVector.Size();
    if (LineLength < SMALL_NUMBER)
    {
        return FVector2D::Distance(Point, LineStart);
    }

    float ProjectionLength = FVector2D::DotProduct(PointVector, LineVector) / LineLength;
    ProjectionLength = FMath::Clamp(ProjectionLength, 0.0f, LineLength);

    FVector2D ClosestPoint = LineStart + (LineVector / LineLength) * ProjectionLength;
    return FVector2D::Distance(Point, ClosestPoint);
}

// ========================================
// Texture Utility Functions
// ========================================

UTexture2D* FAuracronTextureGeneration::CreateTransientTexture(const FIntPoint& Resolution, EPixelFormat PixelFormat, const FString& TextureName) const
{
    UTexture2D* NewTexture = UTexture2D::CreateTransient(Resolution.X, Resolution.Y, PixelFormat, *TextureName);
    if (NewTexture)
    {
        NewTexture->SRGB = (PixelFormat == PF_B8G8R8A8);
        NewTexture->UpdateResource();
    }
    return NewTexture;
}

bool FAuracronTextureGeneration::FillTextureData(UTexture2D* Texture, const TArray<FColor>& ColorData) const
{
    if (!Texture || ColorData.Num() == 0)
    {
        return false;
    }

    FTexture2DMipMap& Mip = Texture->GetPlatformData()->Mips[0];
    void* Data = Mip.BulkData.Lock(LOCK_READ_WRITE);

    if (Data)
    {
        FMemory::Memcpy(Data, ColorData.GetData(), ColorData.Num() * sizeof(FColor));
        Mip.BulkData.Unlock();
        Texture->UpdateResource();
        return true;
    }

    return false;
}
