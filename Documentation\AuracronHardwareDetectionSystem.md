# Sistema de Detecção de Hardware Auracron para UE 5.6

## Visão Geral

O Sistema de Detecção de Hardware Auracron é uma solução robusta e production-ready para detectar automaticamente o hardware do jogador e aplicar configurações de qualidade gráfica otimizadas no Unreal Engine 5.6.

## Características Principais

- **Detecção Automática de Hardware**: Detecta GPU, CPU, memória RAM e recursos avançados
- **Configurações Inteligentes**: Calcula automaticamente as melhores configurações baseadas no hardware
- **Perfis Personalizados**: Suporte a perfis específicos para diferentes GPUs
- **APIs Modernas UE 5.6**: Usa apenas APIs compatíveis com UE 5.6
- **Blueprint Ready**: Totalmente exposto para Blueprints
- **Production Ready**: Código robusto com tratamento de erros e logging detalhado

## Componentes do Sistema

### 1. UAuracronSimpleHardwareDetection
Classe principal que realiza a detecção de hardware e cálculo de configurações.

### 2. UAuracronHardwareDetectionComponent
Componente que pode ser adicionado a qualquer Actor para facilitar o uso.

### 3. AAuracronHardwareOptimizedGameMode
GameMode de exemplo que demonstra o uso completo do sistema.

## Como Usar

### Método 1: Usando o Componente (Recomendado)

```cpp
// No seu GameMode ou PlayerController
UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Hardware")
TObjectPtr<UAuracronHardwareDetectionComponent> HardwareComponent;

// No construtor
HardwareComponent = CreateDefaultSubobject<UAuracronHardwareDetectionComponent>(TEXT("HardwareComponent"));

// Configurar o componente
HardwareComponent->bAutoDetectOnBeginPlay = true;
HardwareComponent->bAutoApplySettings = true;
HardwareComponent->bVerboseLogging = true;
```

### Método 2: Usando Diretamente a Classe

```cpp
// Criar instância
UAuracronSimpleHardwareDetection* HardwareDetection = NewObject<UAuracronSimpleHardwareDetection>();

// Detectar hardware
FSimpleHardwareInfo HardwareInfo = HardwareDetection->DetectHardware();

// Calcular configurações
FSimpleQualitySettings Settings = HardwareDetection->CalculateRecommendedSettings(HardwareInfo);

// Aplicar configurações
HardwareDetection->ApplyQualitySettings(Settings);
```

### Método 3: Usando o GameMode de Exemplo

1. Derive seu GameMode de `AAuracronHardwareOptimizedGameMode`
2. Configure as propriedades no editor
3. Implemente os eventos Blueprint se necessário

## Estruturas de Dados

### FSimpleHardwareInfo
```cpp
USTRUCT(BlueprintType)
struct FSimpleHardwareInfo
{
    FString GPUName;                // Nome da GPU
    int32 VideoMemoryMB;           // Memória de vídeo em MB
    float TotalRAMGB;              // RAM total em GB
    float AvailableRAMGB;          // RAM disponível em GB
    int32 CPUCores;                // Número de cores da CPU
    bool bSupportsRayTracing;      // Suporte a Ray Tracing
    bool bSupportsMeshShaders;     // Suporte a Mesh Shaders
};
```

### FSimpleQualitySettings
```cpp
USTRUCT(BlueprintType)
struct FSimpleQualitySettings
{
    int32 OverallQuality;          // Qualidade geral (0-4)
    int32 TextureQuality;          // Qualidade de texturas (0-4)
    int32 ShadowQuality;           // Qualidade de sombras (0-4)
    int32 PostProcessQuality;      // Qualidade de pós-processamento (0-4)
    int32 AntiAliasingQuality;     // Qualidade de anti-aliasing (0-4)
    int32 ViewDistanceQuality;     // Qualidade de distância de visão (0-4)
    int32 FoliageQuality;          // Qualidade de folhagem (0-4)
    int32 ShadingQuality;          // Qualidade de shading (0-4)
    float ResolutionScale;         // Escala de resolução (0.1-1.0)
    int32 TargetFPS;               // FPS alvo
    bool bEnableLumen;             // Habilitar Lumen
    bool bEnableNanite;            // Habilitar Nanite
    bool bEnableRayTracing;        // Habilitar Ray Tracing
};
```

## Configurações Avançadas

### Limitando Qualidade
```cpp
// No componente
HardwareComponent->MinimumQualityLevel = 1;  // Não vai abaixo de Low
HardwareComponent->MaximumQualityLevel = 3;  // Não vai acima de High
HardwareComponent->QualityMultiplier = 0.8f; // Reduz qualidade em 20%
```

### Perfis Personalizados
```cpp
// Criar perfil para GPU específica
FSimpleQualitySettings CustomSettings;
CustomSettings.OverallQuality = 3;
CustomSettings.bEnableRayTracing = true;

GameMode->CreateCustomProfileForGPU(TEXT("RTX 4080"), CustomSettings);
```

### Eventos Blueprint
O sistema expõe vários eventos que podem ser implementados em Blueprint:

- `OnHardwareDetectedBP`: Chamado quando hardware é detectado
- `OnQualitySettingsAppliedBP`: Chamado quando configurações são aplicadas
- `ShowSettingsUI`: Para mostrar UI personalizada de configurações

## Detecção de Hardware Suportada

### GPU
- Nome da GPU via RHI
- Memória de vídeo via RHI TextureMemoryStats
- Fallback inteligente baseado no nome da GPU
- Suporte a Ray Tracing via RHI
- Suporte a Mesh Shaders via RHI

### CPU
- Número de cores via FPlatformMisc::NumberOfCores()

### Memória
- RAM total e disponível via FPlatformMemory::GetStats()

## Algoritmo de Qualidade

O sistema usa um algoritmo de pontuação para determinar a qualidade:

1. **Pontuação baseada na VRAM**: 16GB+ = 4 pontos, 12GB+ = 3 pontos, etc.
2. **Pontuação baseada na RAM**: 32GB+ = 2 pontos, 16GB+ = 1 ponto
3. **Pontuação baseada na CPU**: 16+ cores = 2 pontos, 8+ cores = 1 ponto
4. **Recursos avançados**: Ray Tracing = 1 ponto, Mesh Shaders = 1 ponto

**Conversão para qualidade**:
- 8+ pontos = Epic (4)
- 6+ pontos = High (3)
- 4+ pontos = Medium (2)
- 2+ pontos = Low (1)
- <2 pontos = Very Low (0)

## Logging e Debug

O sistema fornece logging detalhado quando `bVerboseLogging = true`:

```
=== HARDWARE DETECTION RESULTS ===
GPU: NVIDIA GeForce RTX 4080
Video Memory: 16384 MB
Total RAM: 32.0 GB
Available RAM: 24.5 GB
CPU Cores: 16
Ray Tracing Support: Yes
Mesh Shader Support: Yes
===================================

=== APPLIED QUALITY SETTINGS ===
Overall Quality: 3
Texture Quality: 4
Shadow Quality: 3
...
=================================
```

## Integração com GameUserSettings

O sistema se integra perfeitamente com o UGameUserSettings do UE 5.6:

- Salva configurações automaticamente
- Respeita configurações existentes do usuário
- Permite override manual das configurações

## Considerações de Performance

- Detecção é executada apenas uma vez no início
- Baixo overhead de memória
- Não afeta performance durante o jogo
- Configurações são aplicadas de forma otimizada

## Compatibilidade

- **UE 5.6**: Totalmente compatível
- **Plataformas**: Windows (testado), outras plataformas via APIs cross-platform do UE
- **RHI**: DirectX 12, Vulkan, outros RHIs suportados pelo UE 5.6

## Exemplo de Uso Completo

```cpp
// No seu GameMode
void AMyGameMode::BeginPlay()
{
    Super::BeginPlay();
    
    // Criar e configurar componente
    HardwareComponent = CreateDefaultSubobject<UAuracronHardwareDetectionComponent>(TEXT("Hardware"));
    HardwareComponent->bAutoDetectOnBeginPlay = true;
    HardwareComponent->bVerboseLogging = true;
    HardwareComponent->QualityMultiplier = 0.9f; // Ser um pouco conservador
    
    // Conectar eventos
    HardwareComponent->OnHardwareDetected.AddDynamic(this, &AMyGameMode::OnHardwareDetected);
}

void AMyGameMode::OnHardwareDetected(const FSimpleHardwareInfo& HardwareInfo)
{
    UE_LOG(LogTemp, Log, TEXT("Detected %s with %dMB VRAM"), *HardwareInfo.GPUName, HardwareInfo.VideoMemoryMB);
    
    // Lógica personalizada baseada no hardware detectado
    if (HardwareInfo.VideoMemoryMB < 6144) // Menos de 6GB
    {
        // Desabilitar recursos pesados
        DisableHeavyFeatures();
    }
}
```

Este sistema fornece uma base sólida e extensível para otimização automática de qualidade gráfica baseada no hardware do jogador.
