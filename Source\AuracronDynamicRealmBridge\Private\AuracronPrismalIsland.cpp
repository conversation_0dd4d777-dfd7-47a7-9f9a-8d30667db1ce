/**
 * AuracronPrismalIsland.cpp
 * 
 * Implementation of strategic islands within the Prismal Flow system.
 * Four types: Nexus (Control), Santu<PERSON><PERSON> (Safe Zone), Arsenal (Upgrades), <PERSON>s (High Risk).
 * 
 * Uses UE 5.6 modern APIs for island management, player interaction,
 * and dynamic benefit systems.
 */

#include "AuracronPrismalIsland.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronPrismalFlow.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Components/BoxComponent.h"
#include "Components/AudioComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "GameplayTagContainer.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Sound/SoundBase.h"
#include "Engine/Engine.h"
#include "Sound/SoundBase.h"
#include "Blueprint/UserWidget.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "MetasoundSource.h"
#include "Engine/Engine.h"

AAuracronPrismalIsland::AAuracronPrismalIsland()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.2f; // 5 FPS for island updates

    // Create root scene component
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;

    // Create island mesh component using UE 5.6 mesh system
    IslandMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("IslandMesh"));
    IslandMesh->SetupAttachment(RootComponent);
    IslandMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    IslandMesh->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
    IslandMesh->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Block);
    IslandMesh->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);

    // Create activation trigger using UE 5.6 collision system
    ActivationTrigger = CreateDefaultSubobject<USphereComponent>(TEXT("ActivationTrigger"));
    ActivationTrigger->SetupAttachment(RootComponent);
    ActivationTrigger->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    ActivationTrigger->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
    ActivationTrigger->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
    ActivationTrigger->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);
    ActivationTrigger->SetSphereRadius(500.0f);

    // Create benefit area using UE 5.6 area system
    BenefitArea = CreateDefaultSubobject<UBoxComponent>(TEXT("BenefitArea"));
    BenefitArea->SetupAttachment(RootComponent);
    BenefitArea->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    BenefitArea->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
    BenefitArea->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
    BenefitArea->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);
    BenefitArea->SetBoxExtent(FVector(400.0f, 400.0f, 200.0f));

    // Create audio component using UE 5.6 audio system
    IslandAudio = CreateDefaultSubobject<UAudioComponent>(TEXT("IslandAudio"));
    IslandAudio->SetupAttachment(RootComponent);
    IslandAudio->SetAutoActivate(false);

    // Initialize island effects array
    IslandEffects.Reserve(4); // Reserve space for multiple effects

    // Initialize default values
    IslandType = EPrismalIslandType::Nexus;
    IslandRadius = 400.0f;
    bAutoActivate = false;
    bIsActive = false;
    bIsOnCooldown = false;
    ActivationTime = 0.0f;
    CooldownStartTime = 0.0f;
    ControllingTeam = 0;
    bIsInitialized = false;
    LastUpdateTime = 0.0f;
    LastPlayerCheckTime = 0.0f;
    LastPlayerCount = 0;
    FlowSegmentIndex = 0;
    FlowInfluence = 0.0f;
    FlowDirection = FVector::ForwardVector;
    EnergyBonus = 0.0f;

    // Set default activation requirements
    ActivationRequirements.MinPlayersRequired = 1;
    ActivationRequirements.MaxPlayersSupported = 5;
    ActivationRequirements.ActivationDuration = 30.0f;
    ActivationRequirements.CooldownDuration = 60.0f;
    ActivationRequirements.TeamRequirement = 0;
    ActivationRequirements.RequiredPhase = ERealmEvolutionPhase::Despertar;

    // Set default benefits
    IslandBenefits.HealthRegenRate = 10.0f;
    IslandBenefits.EnergyRegenRate = 15.0f;
    IslandBenefits.ShieldStrength = 100.0f;
    IslandBenefits.DamageMultiplier = 1.0f;
    IslandBenefits.SpeedMultiplier = 1.0f;
    IslandBenefits.VisionRange = 1000.0f;

    // Bind collision events
    ActivationTrigger->OnComponentBeginOverlap.AddDynamic(this, &AAuracronPrismalIsland::OnActivationTriggerBeginOverlap);
    ActivationTrigger->OnComponentEndOverlap.AddDynamic(this, &AAuracronPrismalIsland::OnActivationTriggerEndOverlap);
    BenefitArea->OnComponentBeginOverlap.AddDynamic(this, &AAuracronPrismalIsland::OnBenefitAreaBeginOverlap);
    BenefitArea->OnComponentEndOverlap.AddDynamic(this, &AAuracronPrismalIsland::OnBenefitAreaEndOverlap);
}

void AAuracronPrismalIsland::BeginPlay()
{
    Super::BeginPlay();

    // Initialize island using UE 5.6 initialization patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Prismal Island BeginPlay - Type: %s"), *UEnum::GetValueAsString(IslandType));

    if (GetWorld())
    {
        // Cache subsystem reference
        CachedRealmSubsystem = GetWorld()->GetSubsystem<UAuracronDynamicRealmSubsystem>();
        
        // Delay initialization to ensure all systems are ready
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            InitializeIsland();
        });
    }
}

void AAuracronPrismalIsland::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Clean up island using UE 5.6 cleanup patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Prismal Island EndPlay - Cleaning up..."));

    // Clear all timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Remove effects from all players
    for (APawn* Player : PlayersOnIsland)
    {
        if (Player)
        {
            RemoveIslandBenefits(Player);
        }
    }

    for (APawn* Player : PlayersInBenefitArea)
    {
        if (Player)
        {
            RemoveIslandBenefits(Player);
        }
    }

    // Clean up arrays
    PlayersOnIsland.Empty();
    PlayersInBenefitArea.Empty();
    PlayerBenefitEffects.Empty();

    // Stop all effects
    StopIslandEffects();

    bIsInitialized = false;

    Super::EndPlay(EndPlayReason);
}

void AAuracronPrismalIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    if (!bIsInitialized)
    {
        return;
    }

    // Update island state using UE 5.6 tick optimization
    LastUpdateTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Update activation state
    UpdateActivationState(DeltaTime);

    // Update player benefits
    UpdatePlayerBenefits(DeltaTime);

    // Update island appearance
    UpdateIslandAppearance();

    // Update team control
    UpdateTeamControl();

    // Performance optimization
    if (LastUpdateTime - LastPlayerCheckTime > 1.0f) // Check every second
    {
        OptimizeIslandRendering(GetNearestPlayerLocation());
        LastPlayerCheckTime = LastUpdateTime;
    }
}

// === Island Management Implementation ===

void AAuracronPrismalIsland::InitializeIsland()
{
    if (bIsInitialized)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing %s island..."), *UEnum::GetValueAsString(IslandType));

    // Initialize based on island type using UE 5.6 type-specific initialization
    switch (IslandType)
    {
        case EPrismalIslandType::Nexus:
            InitializeNexusIsland();
            break;
        case EPrismalIslandType::Santuario:
            InitializeSantuarioIsland();
            break;
        case EPrismalIslandType::Arsenal:
            InitializeArsenalIsland();
            break;
        case EPrismalIslandType::Caos:
            InitializeCaosIsland();
            break;
        default:
            break;
    }

    // Setup island effects
    SetupIslandEffects();

    // Configure island audio
    ConfigureIslandAudio();

    // Create dynamic material
    CreateDynamicMaterial();

    // Start benefit update timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            BenefitUpdateTimer,
            [this]()
            {
                UpdatePlayerBenefits(0.2f);
            },
            0.2f, // Update every 0.2 seconds
            true  // Looping
        );
    }

    bIsInitialized = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %s island initialized successfully"), *UEnum::GetValueAsString(IslandType));
}

void AAuracronPrismalIsland::ActivateIsland()
{
    if (bIsActive || bIsOnCooldown || !CheckActivationRequirements())
    {
        return;
    }

    // Activate island using UE 5.6 activation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Activating %s island"), *UEnum::GetValueAsString(IslandType));

    bIsActive = true;
    ActivationTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Process activation
    ProcessActivation();

    // Play activation effects
    PlayIslandEffects();

    // Start activation timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            ActivationTimer,
            [this]()
            {
                DeactivateIsland();
            },
            ActivationRequirements.ActivationDuration,
            false // Single execution
        );
    }

    // Apply benefits to players on island
    for (APawn* Player : PlayersOnIsland)
    {
        if (Player)
        {
            ApplyIslandBenefits(Player);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %s island activated for %.1f seconds"), 
        *UEnum::GetValueAsString(IslandType), ActivationRequirements.ActivationDuration);
}

void AAuracronPrismalIsland::DeactivateIsland()
{
    if (!bIsActive)
    {
        return;
    }

    // Deactivate island using UE 5.6 deactivation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Deactivating %s island"), *UEnum::GetValueAsString(IslandType));

    bIsActive = false;
    bIsOnCooldown = true;
    CooldownStartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Process deactivation
    ProcessDeactivation();

    // Stop island effects
    StopIslandEffects();

    // Remove benefits from all players
    for (APawn* Player : PlayersOnIsland)
    {
        if (Player)
        {
            RemoveIslandBenefits(Player);
        }
    }

    for (APawn* Player : PlayersInBenefitArea)
    {
        if (Player)
        {
            RemoveIslandBenefits(Player);
        }
    }

    // Start cooldown timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            CooldownTimer,
            [this]()
            {
                bIsOnCooldown = false;
                UE_LOG(LogTemp, Log, TEXT("AURACRON: %s island cooldown completed"), *UEnum::GetValueAsString(IslandType));
            },
            ActivationRequirements.CooldownDuration,
            false // Single execution
        );
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %s island deactivated, cooldown: %.1f seconds"), 
        *UEnum::GetValueAsString(IslandType), ActivationRequirements.CooldownDuration);
}

// SetIslandType implementation removed - using inline version from header

// SetIslandRadius implementation removed - using inline version from header

// Getter implementations removed - using inline versions from header

// More getter implementations removed - using inline versions from header

// SetControllingTeam implementation removed - using inline version from header

// GetPlayersOnIsland implementation removed - using inline version from header

TArray<APawn*> AAuracronPrismalIsland::GetPlayersInBenefitArea() const
{
    return PlayersInBenefitArea;
}

void AAuracronPrismalIsland::SetFlowSegmentIndex(int32 SegmentIndex)
{
    FlowSegmentIndex = SegmentIndex;
}

void AAuracronPrismalIsland::SetFlowInfluence(float Influence)
{
    FlowInfluence = FMath::Clamp(Influence, 0.0f, 2.0f);
    
    // Update island effects based on flow influence
    UpdateEffectIntensity(FlowInfluence);
}

void AAuracronPrismalIsland::SetFlowDirection(const FVector& Direction)
{
    FlowDirection = Direction.GetSafeNormal();
}

void AAuracronPrismalIsland::AddEnergyBonus(float Bonus)
{
    EnergyBonus += Bonus;
    EnergyBonus = FMath::Clamp(EnergyBonus, 0.0f, 1000.0f);
}

void AAuracronPrismalIsland::PlayRepositioningEffect()
{
    // Play repositioning effect using UE 5.6 effect system
    static const FSoftObjectPath RepositionVFXPath(TEXT("/Game/VFX/Islands/NS_IslandReposition.NS_IslandReposition"));
    if (UNiagaraSystem* RepositionVFX = Cast<UNiagaraSystem>(RepositionVFXPath.TryLoad()))
    {
        UNiagaraFunctionLibrary::SpawnSystemAttached(
            RepositionVFX,
            RootComponent,
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );
    }
}

// === Collision Event Handlers ===

void AAuracronPrismalIsland::OnActivationTriggerBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    if (APawn* Player = Cast<APawn>(OtherActor))
    {
        ProcessPlayerEntry(Player);
    }
}

void AAuracronPrismalIsland::OnActivationTriggerEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    if (APawn* Player = Cast<APawn>(OtherActor))
    {
        ProcessPlayerExit(Player);
    }
}

void AAuracronPrismalIsland::OnBenefitAreaBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    if (APawn* Player = Cast<APawn>(OtherActor))
    {
        if (!PlayersInBenefitArea.Contains(Player))
        {
            PlayersInBenefitArea.Add(Player);
            
            if (bIsActive)
            {
                ApplyIslandBenefits(Player);
            }
        }
    }
}

void AAuracronPrismalIsland::OnBenefitAreaEndOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex)
{
    if (APawn* Player = Cast<APawn>(OtherActor))
    {
        PlayersInBenefitArea.Remove(Player);
        RemoveIslandBenefits(Player);
    }
}

// === Island Type Initialization ===

void AAuracronPrismalIsland::InitializeNexusIsland()
{
    // Initialize Nexus island using UE 5.6 control point system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Nexus control island"));

    // Set Nexus-specific properties
    ActivationRequirements.MinPlayersRequired = 2;
    ActivationRequirements.MaxPlayersSupported = 8;
    ActivationRequirements.ActivationDuration = 45.0f;
    ActivationRequirements.CooldownDuration = 90.0f;

    // Set Nexus benefits
    IslandBenefits.HealthRegenRate = 20.0f;
    IslandBenefits.EnergyRegenRate = 25.0f;
    IslandBenefits.ShieldStrength = 200.0f;
    IslandBenefits.DamageMultiplier = 1.3f;
    IslandBenefits.SpeedMultiplier = 1.2f;
    IslandBenefits.VisionRange = 2000.0f;

    // Load Nexus-specific assets
    static const FSoftObjectPath NexusMeshPath(TEXT("/Game/Environment/Islands/SM_NexusIsland.SM_NexusIsland"));
    if (UStaticMesh* NexusMesh = Cast<UStaticMesh>(NexusMeshPath.TryLoad()))
    {
        IslandMesh->SetStaticMesh(NexusMesh);
    }

    static const FSoftObjectPath NexusMaterialPath(TEXT("/Game/Materials/Islands/M_NexusIsland.M_NexusIsland"));
    IslandMaterial = Cast<UMaterialInterface>(NexusMaterialPath.TryLoad());

    static const FSoftObjectPath NexusEffectPath(TEXT("/Game/VFX/Islands/NS_NexusControl.NS_NexusControl"));
    IslandEffect = Cast<UNiagaraSystem>(NexusEffectPath.TryLoad());

    static const FSoftObjectPath NexusAudioPath(TEXT("/Game/Audio/Islands/MS_NexusAmbient.MS_NexusAmbient"));
    IslandAudioAsset = Cast<USoundBase>(NexusAudioPath.TryLoad());
}

void AAuracronPrismalIsland::InitializeSantuarioIsland()
{
    // Initialize Santuário island using UE 5.6 safe zone system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Santuário safe zone island"));

    // Set Santuário-specific properties
    ActivationRequirements.MinPlayersRequired = 1;
    ActivationRequirements.MaxPlayersSupported = 6;
    ActivationRequirements.ActivationDuration = 60.0f;
    ActivationRequirements.CooldownDuration = 30.0f;

    // Set Santuário benefits (healing focused)
    IslandBenefits.HealthRegenRate = 50.0f;
    IslandBenefits.EnergyRegenRate = 40.0f;
    IslandBenefits.ShieldStrength = 300.0f;
    IslandBenefits.DamageMultiplier = 0.5f; // Damage reduction
    IslandBenefits.SpeedMultiplier = 1.0f;
    IslandBenefits.VisionRange = 1500.0f;

    // Load Santuário-specific assets
    static const FSoftObjectPath SantuarioMeshPath(TEXT("/Game/Environment/Islands/SM_SantuarioIsland.SM_SantuarioIsland"));
    if (UStaticMesh* SantuarioMesh = Cast<UStaticMesh>(SantuarioMeshPath.TryLoad()))
    {
        IslandMesh->SetStaticMesh(SantuarioMesh);
    }

    static const FSoftObjectPath SantuarioMaterialPath(TEXT("/Game/Materials/Islands/M_SantuarioIsland.M_SantuarioIsland"));
    IslandMaterial = Cast<UMaterialInterface>(SantuarioMaterialPath.TryLoad());

    static const FSoftObjectPath SantuarioEffectPath(TEXT("/Game/VFX/Islands/NS_SantuarioHealing.NS_SantuarioHealing"));
    IslandEffect = Cast<UNiagaraSystem>(SantuarioEffectPath.TryLoad());

    static const FSoftObjectPath SantuarioAudioPath(TEXT("/Game/Audio/Islands/MS_SantuarioAmbient.MS_SantuarioAmbient"));
    IslandAudioAsset = Cast<USoundBase>(SantuarioAudioPath.TryLoad());
}

void AAuracronPrismalIsland::InitializeArsenalIsland()
{
    // Initialize Arsenal island using UE 5.6 upgrade system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Arsenal upgrade island"));

    // Set Arsenal-specific properties
    ActivationRequirements.MinPlayersRequired = 1;
    ActivationRequirements.MaxPlayersSupported = 4;
    ActivationRequirements.ActivationDuration = 20.0f;
    ActivationRequirements.CooldownDuration = 120.0f;

    // Set Arsenal benefits (upgrade focused)
    IslandBenefits.HealthRegenRate = 15.0f;
    IslandBenefits.EnergyRegenRate = 20.0f;
    IslandBenefits.ShieldStrength = 150.0f;
    IslandBenefits.DamageMultiplier = 2.0f; // Damage boost
    IslandBenefits.SpeedMultiplier = 1.4f;
    IslandBenefits.VisionRange = 1800.0f;

    // Load Arsenal-specific assets
    static const FSoftObjectPath ArsenalMeshPath(TEXT("/Game/Environment/Islands/SM_ArsenalIsland.SM_ArsenalIsland"));
    if (UStaticMesh* ArsenalMesh = Cast<UStaticMesh>(ArsenalMeshPath.TryLoad()))
    {
        IslandMesh->SetStaticMesh(ArsenalMesh);
    }

    static const FSoftObjectPath ArsenalMaterialPath(TEXT("/Game/Materials/Islands/M_ArsenalIsland.M_ArsenalIsland"));
    IslandMaterial = Cast<UMaterialInterface>(ArsenalMaterialPath.TryLoad());

    static const FSoftObjectPath ArsenalEffectPath(TEXT("/Game/VFX/Islands/NS_ArsenalUpgrade.NS_ArsenalUpgrade"));
    IslandEffect = Cast<UNiagaraSystem>(ArsenalEffectPath.TryLoad());

    static const FSoftObjectPath ArsenalAudioPath(TEXT("/Game/Audio/Islands/MS_ArsenalAmbient.MS_ArsenalAmbient"));
    IslandAudioAsset = Cast<USoundBase>(ArsenalAudioPath.TryLoad());
}

void AAuracronPrismalIsland::InitializeCaosIsland()
{
    // Initialize Caos island using UE 5.6 chaos system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Caos high-risk island"));

    // Set Caos-specific properties
    ActivationRequirements.MinPlayersRequired = 3;
    ActivationRequirements.MaxPlayersSupported = 6;
    ActivationRequirements.ActivationDuration = 15.0f;
    ActivationRequirements.CooldownDuration = 180.0f;

    // Set Caos benefits (high risk, high reward)
    IslandBenefits.HealthRegenRate = 5.0f; // Low healing
    IslandBenefits.EnergyRegenRate = 60.0f; // High energy
    IslandBenefits.ShieldStrength = 50.0f; // Low protection
    IslandBenefits.DamageMultiplier = 3.0f; // Very high damage
    IslandBenefits.SpeedMultiplier = 1.8f; // High speed
    IslandBenefits.VisionRange = 2500.0f; // Extended vision

    // Load Caos-specific assets
    static const FSoftObjectPath CaosMeshPath(TEXT("/Game/Environment/Islands/SM_CaosIsland.SM_CaosIsland"));
    if (UStaticMesh* CaosMesh = Cast<UStaticMesh>(CaosMeshPath.TryLoad()))
    {
        IslandMesh->SetStaticMesh(CaosMesh);
    }

    static const FSoftObjectPath CaosMaterialPath(TEXT("/Game/Materials/Islands/M_CaosIsland.M_CaosIsland"));
    IslandMaterial = Cast<UMaterialInterface>(CaosMaterialPath.TryLoad());

    static const FSoftObjectPath CaosEffectPath(TEXT("/Game/VFX/Islands/NS_CaosChaos.NS_CaosChaos"));
    IslandEffect = Cast<UNiagaraSystem>(CaosEffectPath.TryLoad());

    static const FSoftObjectPath CaosAudioPath(TEXT("/Game/Audio/Islands/MS_CaosAmbient.MS_CaosAmbient"));
    IslandAudioAsset = Cast<USoundBase>(CaosAudioPath.TryLoad());
}

void AAuracronPrismalIsland::ReconfigureForType()
{
    // Reconfigure island for new type using UE 5.6 reconfiguration system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Reconfiguring island for type %s"), *UEnum::GetValueAsString(IslandType));

    // Stop current effects
    StopIslandEffects();

    // Reinitialize based on new type
    switch (IslandType)
    {
        case EPrismalIslandType::Nexus:
            InitializeNexusIsland();
            break;
        case EPrismalIslandType::Santuario:
            InitializeSantuarioIsland();
            break;
        case EPrismalIslandType::Arsenal:
            InitializeArsenalIsland();
            break;
        case EPrismalIslandType::Caos:
            InitializeCaosIsland();
            break;
        default:
            break;
    }

    // Restart effects if island was active
    if (bIsActive)
    {
        PlayIslandEffects();
    }
}

// === Player Interaction Implementation ===

void AAuracronPrismalIsland::ProcessPlayerEntry(APawn* Player)
{
    if (!Player || PlayersOnIsland.Contains(Player))
    {
        return;
    }

    // Process player entry using UE 5.6 entry system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s entered %s island"),
        *Player->GetName(), *UEnum::GetValueAsString(IslandType));

    PlayersOnIsland.Add(Player);

    // Check if island should activate
    if (!bIsActive && !bIsOnCooldown && CheckActivationRequirements())
    {
        ActivateIsland();
    }

    // Show interaction prompt
    ShowIslandInteractionPrompt(Player);

    // Play entry effect
    PlayPlayerEntryEffect(Player);
}

void AAuracronPrismalIsland::ProcessPlayerExit(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Process player exit using UE 5.6 exit system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player %s exited %s island"),
        *Player->GetName(), *UEnum::GetValueAsString(IslandType));

    PlayersOnIsland.Remove(Player);

    // Remove benefits if player is not in benefit area
    if (!PlayersInBenefitArea.Contains(Player))
    {
        RemoveIslandBenefits(Player);
    }

    // Hide interaction prompt
    HideIslandInteractionPrompt(Player);

    // Check if island should deactivate
    if (bIsActive && PlayersOnIsland.Num() < ActivationRequirements.MinPlayersRequired)
    {
        DeactivateIsland();
    }
}

bool AAuracronPrismalIsland::CheckActivationRequirements() const
{
    // Check activation requirements using UE 5.6 requirement system

    // Check player count
    if (PlayersOnIsland.Num() < ActivationRequirements.MinPlayersRequired)
    {
        return false;
    }

    if (PlayersOnIsland.Num() > ActivationRequirements.MaxPlayersSupported)
    {
        return false;
    }

    // Check team requirement
    if (ActivationRequirements.TeamRequirement != 0)
    {
        bool bHasRequiredTeam = false;
        for (APawn* Player : PlayersOnIsland)
        {
            if (Player && GetPlayerTeamID(Player) == ActivationRequirements.TeamRequirement)
            {
                bHasRequiredTeam = true;
                break;
            }
        }

        if (!bHasRequiredTeam)
        {
            return false;
        }
    }

    // Check realm phase requirement
    if (CachedRealmSubsystem)
    {
        ERealmEvolutionPhase CurrentPhase = CachedRealmSubsystem->GetCurrentEvolutionPhase();
        if (static_cast<int32>(CurrentPhase) < static_cast<int32>(ActivationRequirements.RequiredPhase))
        {
            return false;
        }
    }

    return true;
}

void AAuracronPrismalIsland::ProcessActivation()
{
    // Process island activation using UE 5.6 activation processing
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Processing %s island activation"), *UEnum::GetValueAsString(IslandType));

    // Type-specific activation processing
    switch (IslandType)
    {
        case EPrismalIslandType::Nexus:
            ProcessNexusActivation();
            break;
        case EPrismalIslandType::Santuario:
            ProcessSantuarioActivation();
            break;
        case EPrismalIslandType::Arsenal:
            ProcessArsenalActivation();
            break;
        case EPrismalIslandType::Caos:
            ProcessCaosActivation();
            break;
        default:
            break;
    }

    // Notify realm subsystem
    if (CachedRealmSubsystem)
    {
        CachedRealmSubsystem->OnPrismalIslandActivated.Broadcast(IslandType, GetActorLocation());
    }
}

void AAuracronPrismalIsland::ProcessDeactivation()
{
    // Process island deactivation using UE 5.6 deactivation processing
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Processing %s island deactivation"), *UEnum::GetValueAsString(IslandType));

    // Type-specific deactivation processing
    switch (IslandType)
    {
        case EPrismalIslandType::Nexus:
            ProcessNexusDeactivation();
            break;
        case EPrismalIslandType::Santuario:
            ProcessSantuarioDeactivation();
            break;
        case EPrismalIslandType::Arsenal:
            ProcessArsenalDeactivation();
            break;
        case EPrismalIslandType::Caos:
            ProcessCaosDeactivation();
            break;
        default:
            break;
    }
}

void AAuracronPrismalIsland::ProcessNexusActivation()
{
    // Process Nexus activation - grants team control over flow segment
    if (CachedRealmSubsystem)
    {
        // Find flow actor
        if (AAuracronPrismalFlow* FlowActor = CachedRealmSubsystem->GetPrismalFlowActor())
        {
            // Determine controlling team
            int32 DominantTeam = GetDominantTeamOnIsland();

            if (DominantTeam != 0)
            {
                FlowActor->SetSegmentControl(FlowSegmentIndex, DominantTeam);
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Nexus granted flow segment %d control to team %d"),
                    FlowSegmentIndex, DominantTeam);
            }
        }
    }
}

void AAuracronPrismalIsland::ProcessSantuarioActivation()
{
    // Process Santuário activation - creates safe zone effects
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Santuário safe zone activated"));

    // Apply area-wide protection
    for (APawn* Player : PlayersInBenefitArea)
    {
        if (Player)
        {
            ApplySantuarioProtection(Player);
        }
    }
}

void AAuracronPrismalIsland::ProcessArsenalActivation()
{
    // Process Arsenal activation - provides upgrades to players
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Arsenal upgrade station activated"));

    // Apply upgrades to players
    for (APawn* Player : PlayersOnIsland)
    {
        if (Player)
        {
            ApplyArsenalUpgrades(Player);
        }
    }
}

void AAuracronPrismalIsland::ProcessCaosActivation()
{
    // Process Caos activation - high risk, high reward effects
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Caos high-risk zone activated"));

    // Apply chaos effects to all players in area
    for (APawn* Player : PlayersInBenefitArea)
    {
        if (Player)
        {
            ApplyCaosEffects(Player);
        }
    }

    // Spawn chaos events
    SpawnChaosEvents();
}

void AAuracronPrismalIsland::ProcessNexusDeactivation()
{
    // Process Nexus deactivation - removes team control
    if (CachedRealmSubsystem)
    {
        if (AAuracronPrismalFlow* FlowActor = CachedRealmSubsystem->GetPrismalFlowActor())
        {
            FlowActor->SetSegmentControl(FlowSegmentIndex, 0); // Return to neutral
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Nexus released flow segment %d control"), FlowSegmentIndex);
        }
    }
}

void AAuracronPrismalIsland::ProcessSantuarioDeactivation()
{
    // Process Santuário deactivation - removes protection effects
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Santuário safe zone deactivated"));

    // Remove protection from all players
    for (APawn* Player : PlayersInBenefitArea)
    {
        if (Player)
        {
            RemoveSantuarioProtection(Player);
        }
    }
}

void AAuracronPrismalIsland::ProcessArsenalDeactivation()
{
    // Process Arsenal deactivation - upgrades remain but no new ones
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Arsenal upgrade station deactivated"));
    // Upgrades are permanent, so no removal needed
}

void AAuracronPrismalIsland::ProcessCaosDeactivation()
{
    // Process Caos deactivation - removes chaos effects
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Caos high-risk zone deactivated"));

    // Remove chaos effects from all players
    for (APawn* Player : PlayersInBenefitArea)
    {
        if (Player)
        {
            RemoveCaosEffects(Player);
        }
    }
}

// === Benefit Application Implementation ===

void AAuracronPrismalIsland::ApplyIslandBenefits(APawn* Player)
{
    if (!Player || !bIsActive)
    {
        return;
    }

    // Apply island benefits using UE 5.6 benefit system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        // Get island-specific benefit effect
        FString BenefitEffectPath = GetBenefitEffectPath();

        FSoftClassPath BenefitEffectSoftPath(BenefitEffectPath);
        if (TSubclassOf<UGameplayEffect> BenefitEffectClass = BenefitEffectSoftPath.TryLoadClass<UGameplayEffect>())
        {
            if (UGameplayEffect* BenefitEffectInstance = BenefitEffectClass.GetDefaultObject())
            {
                FGameplayEffectSpec BenefitSpec(BenefitEffectInstance, EffectContext, 1.0f);

            // Set benefit parameters
            BenefitSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Island.Health.Regen")), IslandBenefits.HealthRegenRate);
            BenefitSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Island.Energy.Regen")), IslandBenefits.EnergyRegenRate);
            BenefitSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Island.Shield.Strength")), IslandBenefits.ShieldStrength);
            BenefitSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Island.Damage.Multiplier")), IslandBenefits.DamageMultiplier);
            BenefitSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Island.Speed.Multiplier")), IslandBenefits.SpeedMultiplier);
            BenefitSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Island.Vision.Range")), IslandBenefits.VisionRange);

            // Add flow influence bonus
            BenefitSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Island.Flow.Bonus")), FlowInfluence);
            BenefitSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Island.Energy.Bonus")), EnergyBonus);

            BenefitSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle BenefitHandle = ASC->ApplyGameplayEffectSpecToSelf(BenefitSpec);
            PlayerBenefitEffects.Add(Player, BenefitHandle);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Applied %s island benefits to %s"),
                *UEnum::GetValueAsString(IslandType), *Player->GetName());
            }
        }
    }
}

void AAuracronPrismalIsland::RemoveIslandBenefits(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Remove island benefits using UE 5.6 cleanup system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        if (FActiveGameplayEffectHandle* BenefitHandle = PlayerBenefitEffects.Find(Player))
        {
            ASC->RemoveActiveGameplayEffect(*BenefitHandle);
            PlayerBenefitEffects.Remove(Player);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Removed %s island benefits from %s"),
                *UEnum::GetValueAsString(IslandType), *Player->GetName());
        }
    }
}

void AAuracronPrismalIsland::ApplySantuarioProtection(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply Santuário protection using UE 5.6 protection system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        static const FSoftClassPath ProtectionPath(TEXT("/Game/GameplayEffects/Islands/GE_SantuarioProtection.GE_SantuarioProtection_C"));
        if (TSubclassOf<UGameplayEffect> ProtectionEffectClass = ProtectionPath.TryLoadClass<UGameplayEffect>())
        {
            if (UGameplayEffect* ProtectionEffect = ProtectionEffectClass.GetDefaultObject())
            {
                FGameplayEffectSpec ProtectionSpec(ProtectionEffect, EffectContext, 1.0f);

            ProtectionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Protection.Physical")), 0.8f);
            ProtectionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Protection.Magical")), 0.7f);
            ProtectionSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Protection.Environmental")), 0.9f);

            ProtectionSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle ProtectionHandle = ASC->ApplyGameplayEffectSpecToSelf(ProtectionSpec);
            PlayerProtectionEffects.Add(Player, ProtectionHandle);
            }
        }
    }
}

void AAuracronPrismalIsland::RemoveSantuarioProtection(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Remove Santuário protection using UE 5.6 cleanup system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        if (FActiveGameplayEffectHandle* ProtectionHandle = PlayerProtectionEffects.Find(Player))
        {
            ASC->RemoveActiveGameplayEffect(*ProtectionHandle);
            PlayerProtectionEffects.Remove(Player);
        }
    }
}

void AAuracronPrismalIsland::ApplyArsenalUpgrades(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply Arsenal upgrades using UE 5.6 upgrade system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        static const FSoftClassPath UpgradePath(TEXT("/Game/GameplayEffects/Islands/GE_ArsenalUpgrade.GE_ArsenalUpgrade_C"));
        if (TSubclassOf<UGameplayEffect> UpgradeEffectClass = UpgradePath.TryLoadClass<UGameplayEffect>())
        {
            if (UGameplayEffect* UpgradeEffect = UpgradeEffectClass.GetDefaultObject())
            {
                FGameplayEffectSpec UpgradeSpec(UpgradeEffect, EffectContext, 1.0f);

            UpgradeSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Upgrade.Damage.Bonus")), 1.5f);
            UpgradeSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Upgrade.Speed.Bonus")), 1.3f);
            UpgradeSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Upgrade.Ability.Power")), 1.4f);

            UpgradeSpec.SetDuration(300.0f, false); // 5 minute upgrade

            ASC->ApplyGameplayEffectSpecToSelf(UpgradeSpec);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Applied Arsenal upgrades to %s"), *Player->GetName());
            }
        }
    }
}

void AAuracronPrismalIsland::ApplyCaosEffects(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Apply Caos effects using UE 5.6 chaos system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
        EffectContext.AddSourceObject(this);

        static const FSoftClassPath ChaosPath(TEXT("/Game/GameplayEffects/Islands/GE_CaosRisk.GE_CaosRisk_C"));
        if (TSubclassOf<UGameplayEffect> ChaosEffectClass = ChaosPath.TryLoadClass<UGameplayEffect>())
        {
            if (UGameplayEffect* ChaosEffect = ChaosEffectClass.GetDefaultObject())
            {
                FGameplayEffectSpec ChaosSpec(ChaosEffect, EffectContext, 1.0f);

            // High risk, high reward
            ChaosSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Chaos.Damage.Vulnerability")), 2.0f);
            ChaosSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Chaos.Power.Amplifier")), 3.0f);
            ChaosSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Chaos.Speed.Boost")), 1.8f);
            ChaosSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Chaos.Energy.Drain")), 0.5f);

            ChaosSpec.SetDuration(UGameplayEffect::INFINITE_DURATION, false);

            FActiveGameplayEffectHandle ChaosHandle = ASC->ApplyGameplayEffectSpecToSelf(ChaosSpec);
            PlayerChaosEffects.Add(Player, ChaosHandle);
            }
        }
    }
}

void AAuracronPrismalIsland::RemoveCaosEffects(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Remove Caos effects using UE 5.6 cleanup system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        if (FActiveGameplayEffectHandle* ChaosHandle = PlayerChaosEffects.Find(Player))
        {
            ASC->RemoveActiveGameplayEffect(*ChaosHandle);
            PlayerChaosEffects.Remove(Player);
        }
    }
}

void AAuracronPrismalIsland::SpawnChaosEvents()
{
    if (!GetWorld())
    {
        return;
    }

    // Spawn chaos events using UE 5.6 event spawning
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Spawning chaos events around Caos island"));

    // Spawn random chaos effects around the island
    int32 EventCount = FMath::RandRange(2, 5);

    for (int32 i = 0; i < EventCount; i++)
    {
        // Random location around island
        FVector EventLocation = GetActorLocation() + FVector(
            FMath::RandRange(-IslandRadius * 2.0f, IslandRadius * 2.0f),
            FMath::RandRange(-IslandRadius * 2.0f, IslandRadius * 2.0f),
            FMath::RandRange(-100.0f, 200.0f)
        );

        // Spawn chaos VFX
        static const FSoftObjectPath ChaosEventPath(TEXT("/Game/VFX/Islands/NS_ChaosEvent.NS_ChaosEvent"));
        if (UNiagaraSystem* ChaosEventVFX = Cast<UNiagaraSystem>(ChaosEventPath.TryLoad()))
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                ChaosEventVFX,
                EventLocation,
                FRotator::ZeroRotator,
                FVector::OneVector,
                true, // Auto-destroy
                true, // Auto-activate
                ENCPoolMethod::None
            );
        }
    }
}

// === Visual and Audio Effects Implementation ===

void AAuracronPrismalIsland::SetupIslandEffects()
{
    if (!IslandEffect)
    {
        return;
    }

    // Setup island effects using UE 5.6 VFX system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up %s island effects"), *UEnum::GetValueAsString(IslandType));

    // Create Niagara components for each effect layer
    for (int32 i = 0; i < 3; i++) // Base, Activation, Team effects
    {
        FString ComponentName = FString::Printf(TEXT("IslandEffect_%d"), i);
        UNiagaraComponent* EffectComponent = CreateDefaultSubobject<UNiagaraComponent>(*ComponentName);
        EffectComponent->SetupAttachment(RootComponent);
        EffectComponent->SetAsset(IslandEffect);
        EffectComponent->SetAutoActivate(false);

        IslandEffects.Add(EffectComponent);
    }

    // Configure base effect
    if (IslandEffects.IsValidIndex(0))
    {
        UNiagaraComponent* BaseEffect = IslandEffects[0];
        BaseEffect->SetVariableFloat(FName("IslandRadius"), IslandRadius);
        BaseEffect->SetVariableInt(FName("IslandType"), static_cast<int32>(IslandType));
        BaseEffect->SetVariableFloat(FName("BaseIntensity"), 0.8f);
        BaseEffect->Activate();
    }
}

void AAuracronPrismalIsland::ConfigureIslandAudio()
{
    if (!IslandAudioAsset || !IslandAudio)
    {
        return;
    }

    // Configure island audio using UE 5.6 audio system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Configuring %s island audio"), *UEnum::GetValueAsString(IslandType));

    IslandAudio->SetSound(IslandAudioAsset);

    // Set audio parameters based on island type
    switch (IslandType)
    {
        case EPrismalIslandType::Nexus:
            IslandAudio->SetFloatParameter(TEXT("IslandType"), 1.0f);
            IslandAudio->SetVolumeMultiplier(1.0f);
            break;
        case EPrismalIslandType::Santuario:
            IslandAudio->SetFloatParameter(TEXT("IslandType"), 2.0f);
            IslandAudio->SetVolumeMultiplier(0.7f);
            break;
        case EPrismalIslandType::Arsenal:
            IslandAudio->SetFloatParameter(TEXT("IslandType"), 3.0f);
            IslandAudio->SetVolumeMultiplier(0.9f);
            break;
        case EPrismalIslandType::Caos:
            IslandAudio->SetFloatParameter(TEXT("IslandType"), 4.0f);
            IslandAudio->SetVolumeMultiplier(1.2f);
            break;
        default:
            break;
    }

    IslandAudio->SetFloatParameter(TEXT("IslandRadius"), IslandRadius / 1000.0f);
    IslandAudio->Play();
}

void AAuracronPrismalIsland::CreateDynamicMaterial()
{
    if (!IslandMaterial || !IslandMesh)
    {
        return;
    }

    // Create dynamic material using UE 5.6 material system
    UMaterialInstanceDynamic* DynamicMaterial = IslandMesh->CreateDynamicMaterialInstance(0, IslandMaterial);
    if (DynamicMaterial)
    {
        // Set initial material parameters
        DynamicMaterial->SetScalarParameterValue(TEXT("IslandRadius"), IslandRadius);
        DynamicMaterial->SetScalarParameterValue(TEXT("IslandType"), static_cast<float>(IslandType));
        DynamicMaterial->SetScalarParameterValue(TEXT("ActivationState"), bIsActive ? 1.0f : 0.0f);
        DynamicMaterial->SetScalarParameterValue(TEXT("TeamControl"), static_cast<float>(ControllingTeam));
        DynamicMaterial->SetVectorParameterValue(TEXT("TeamColor"), GetTeamColor());

        DynamicIslandMaterial = DynamicMaterial;
    }
}

void AAuracronPrismalIsland::PlayIslandEffects()
{
    // Play island effects using UE 5.6 effect activation
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Playing %s island effects"), *UEnum::GetValueAsString(IslandType));

    // Activate activation effect
    if (IslandEffects.IsValidIndex(1))
    {
        UNiagaraComponent* ActivationEffectComponent = IslandEffects[1];
        ActivationEffectComponent->SetVariableFloat(FName("ActivationIntensity"), 2.0f);
        ActivationEffectComponent->SetVariableLinearColor(FName("ActivationColor"), GetIslandTypeColor());
        ActivationEffectComponent->Activate();
    }

    // Play activation audio burst
    static const FSoftObjectPath ActivationAudioPath(TEXT("/Game/Audio/Islands/MS_IslandActivation.MS_IslandActivation"));
    if (USoundBase* ActivationAudio = Cast<USoundBase>(ActivationAudioPath.TryLoad()))
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            ActivationAudio,
            GetActorLocation()
        );
    }
}

void AAuracronPrismalIsland::StopIslandEffects()
{
    // Stop island effects using UE 5.6 effect deactivation
    for (UNiagaraComponent* Effect : IslandEffects)
    {
        if (Effect && Effect->IsActive())
        {
            Effect->Deactivate();
        }
    }
}

void AAuracronPrismalIsland::PlayPlayerEntryEffect(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Play player entry effect using UE 5.6 player effect system
    static const FSoftObjectPath EntryVFXPath(TEXT("/Game/VFX/Islands/NS_PlayerIslandEntry.NS_PlayerIslandEntry"));
    if (UNiagaraSystem* EntryVFX = Cast<UNiagaraSystem>(EntryVFXPath.TryLoad()))
    {
        UNiagaraComponent* EntryComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            EntryVFX,
            Player->GetRootComponent(),
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );

        if (EntryComponent)
        {
            EntryComponent->SetVariableLinearColor(FName("IslandColor"), GetIslandTypeColor());
            EntryComponent->SetVariableFloat(FName("IslandType"), static_cast<float>(IslandType));
        }
    }
}

// === Update Methods Implementation ===

void AAuracronPrismalIsland::UpdateActivationState(float DeltaTime)
{
    // Update activation state using UE 5.6 state management

    // Check cooldown status
    if (bIsOnCooldown && GetWorld())
    {
        float TimeSinceCooldownStart = GetWorld()->GetTimeSeconds() - CooldownStartTime;
        if (TimeSinceCooldownStart >= ActivationRequirements.CooldownDuration)
        {
            bIsOnCooldown = false;
            UE_LOG(LogTemp, Log, TEXT("AURACRON: %s island cooldown completed"), *UEnum::GetValueAsString(IslandType));
        }
    }

    // Auto-activation check
    if (bAutoActivate && !bIsActive && !bIsOnCooldown && CheckActivationRequirements())
    {
        ActivateIsland();
    }
}

void AAuracronPrismalIsland::UpdatePlayerBenefits(float DeltaTime)
{
    if (!bIsActive)
    {
        return;
    }

    // Update player benefits using UE 5.6 benefit update system
    for (APawn* Player : PlayersInBenefitArea)
    {
        if (Player)
        {
            // Apply continuous benefits
            ApplyContinuousBenefits(Player, DeltaTime);
        }
    }
}

void AAuracronPrismalIsland::UpdateIslandAppearance()
{
    // Update island appearance using UE 5.6 appearance system
    if (DynamicIslandMaterial)
    {
        float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

        // Update material parameters
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("GameTime"), CurrentTime);
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("ActivationState"), bIsActive ? 1.0f : 0.0f);
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("PlayerCount"), static_cast<float>(PlayersOnIsland.Num()));
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("FlowInfluence"), FlowInfluence);
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("EnergyBonus"), EnergyBonus / 100.0f);

        // Update team control visualization
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("TeamControl"), static_cast<float>(ControllingTeam));
        DynamicIslandMaterial->SetVectorParameterValue(TEXT("TeamColor"), GetTeamColor());
    }

    // Update VFX parameters
    UpdateEffectIntensity(FlowInfluence);
}

void AAuracronPrismalIsland::UpdateTeamControl()
{
    // Update team control based on player presence using UE 5.6 control system
    if (PlayersOnIsland.IsEmpty())
    {
        return;
    }

    // Count players by team
    TMap<int32, int32> TeamCounts;
    for (APawn* Player : PlayersOnIsland)
    {
        if (Player)
        {
            int32 PlayerTeam = GetPlayerTeamID(Player);
            TeamCounts.FindOrAdd(PlayerTeam)++;
        }
    }

    // Determine dominant team
    int32 DominantTeam = 0;
    int32 MaxCount = 0;

    for (const auto& TeamPair : TeamCounts)
    {
        if (TeamPair.Value > MaxCount)
        {
            MaxCount = TeamPair.Value;
            DominantTeam = TeamPair.Key;
        }
    }

    // Apply team influence if significant
    if (MaxCount >= ActivationRequirements.MinPlayersRequired && DominantTeam != 0)
    {
        ApplyTeamInfluence(DominantTeam, 0.2f); // 20% influence per update
    }
}

void AAuracronPrismalIsland::ApplyTeamInfluence(int32 TeamID, float InfluenceStrength)
{
    // Apply team influence using UE 5.6 influence system
    if (TeamID == ControllingTeam)
    {
        return; // Already controlled by this team
    }

    // Calculate influence accumulation
    float* CurrentInfluence = TeamInfluenceMap.Find(TeamID);
    if (!CurrentInfluence)
    {
        TeamInfluenceMap.Add(TeamID, 0.0f);
        CurrentInfluence = &TeamInfluenceMap[TeamID];
    }

    *CurrentInfluence += InfluenceStrength;

    // Check if team gains control
    if (*CurrentInfluence >= 1.0f)
    {
        SetControllingTeam(TeamID);
        TeamInfluenceMap.Empty(); // Reset all influence
    }
}

// === Utility Methods Implementation ===

int32 AAuracronPrismalIsland::GetDominantTeamOnIsland() const
{
    // Get dominant team on island
    TMap<int32, int32> TeamCounts;

    for (APawn* Player : PlayersOnIsland)
    {
        if (Player)
        {
            int32 PlayerTeam = GetPlayerTeamID(Player);
            TeamCounts.FindOrAdd(PlayerTeam)++;
        }
    }

    int32 DominantTeam = 0;
    int32 MaxCount = 0;

    for (const auto& TeamPair : TeamCounts)
    {
        if (TeamPair.Value > MaxCount)
        {
            MaxCount = TeamPair.Value;
            DominantTeam = TeamPair.Key;
        }
    }

    return DominantTeam;
}

int32 AAuracronPrismalIsland::GetPlayerTeamID(APawn* Player) const
{
    if (!Player)
    {
        return 0;
    }

    // Get player team ID using UE 5.6 team system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        FGameplayTagContainer PlayerTags;
        ASC->GetOwnedGameplayTags(PlayerTags);

        if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("Player.Team.1"))))
        {
            return 1;
        }
        else if (PlayerTags.HasTag(FGameplayTag::RequestGameplayTag(TEXT("Player.Team.2"))))
        {
            return 2;
        }
    }

    return 0; // Neutral
}

FString AAuracronPrismalIsland::GetBenefitEffectPath() const
{
    // Get benefit effect path based on island type
    switch (IslandType)
    {
        case EPrismalIslandType::Nexus:
            return TEXT("/Game/GameplayEffects/Islands/GE_NexusBenefits.GE_NexusBenefits_C");
        case EPrismalIslandType::Santuario:
            return TEXT("/Game/GameplayEffects/Islands/GE_SantuarioBenefits.GE_SantuarioBenefits_C");
        case EPrismalIslandType::Arsenal:
            return TEXT("/Game/GameplayEffects/Islands/GE_ArsenalBenefits.GE_ArsenalBenefits_C");
        case EPrismalIslandType::Caos:
            return TEXT("/Game/GameplayEffects/Islands/GE_CaosBenefits.GE_CaosBenefits_C");
        default:
            return TEXT("/Game/GameplayEffects/Islands/GE_DefaultIslandBenefits.GE_DefaultIslandBenefits_C");
    }
}

FLinearColor AAuracronPrismalIsland::GetIslandTypeColor() const
{
    // Get color based on island type
    switch (IslandType)
    {
        case EPrismalIslandType::Nexus:
            return FLinearColor(1.0f, 0.84f, 0.0f, 1.0f); // Gold
        case EPrismalIslandType::Santuario:
            return FLinearColor::Green;
        case EPrismalIslandType::Arsenal:
            return FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Orange
        case EPrismalIslandType::Caos:
            return FLinearColor(0.5f, 0.0f, 0.5f, 1.0f); // Purple
        default:
            return FLinearColor::White;
    }
}

FLinearColor AAuracronPrismalIsland::GetTeamColor() const
{
    // Get team color
    switch (ControllingTeam)
    {
        case 1: return FLinearColor::Blue;
        case 2: return FLinearColor::Red;
        default: return FLinearColor::White;
    }
}

void AAuracronPrismalIsland::UpdateEffectIntensity(float Intensity)
{
    // Update effect intensity using UE 5.6 intensity system
    for (UNiagaraComponent* Effect : IslandEffects)
    {
        if (Effect && Effect->IsActive())
        {
            Effect->SetVariableFloat(FName("EffectIntensity"), Intensity);
            Effect->SetVariableFloat(FName("FlowInfluence"), FlowInfluence);
        }
    }
}

void AAuracronPrismalIsland::ApplyTeamControlEffects(int32 TeamID)
{
    // Apply team control effects using UE 5.6 team effect system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying team %d control effects to %s island"),
        TeamID, *UEnum::GetValueAsString(IslandType));

    // Update material team color
    if (DynamicIslandMaterial)
    {
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("TeamControl"), static_cast<float>(TeamID));
        DynamicIslandMaterial->SetVectorParameterValue(TEXT("TeamColor"), GetTeamColor());
    }

    // Activate team effect
    if (IslandEffects.IsValidIndex(2))
    {
        UNiagaraComponent* TeamEffect = IslandEffects[2];
        TeamEffect->SetVariableLinearColor(FName("TeamColor"), GetTeamColor());
        TeamEffect->SetVariableInt(FName("TeamID"), TeamID);
        TeamEffect->SetVariableFloat(FName("ControlStrength"), 1.0f);
        TeamEffect->Activate();
    }

    // Play team control audio
    if (IslandAudio)
    {
        IslandAudio->SetFloatParameter(TEXT("TeamControl"), static_cast<float>(TeamID));
    }
}

void AAuracronPrismalIsland::ApplyContinuousBenefits(APawn* Player, float DeltaTime)
{
    if (!Player)
    {
        return;
    }

    // Apply continuous benefits using UE 5.6 continuous effect system
    if (UAbilitySystemComponent* ASC = Player->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Apply health regeneration
        if (IslandBenefits.HealthRegenRate > 0.0f)
        {
            float HealthRegen = IslandBenefits.HealthRegenRate * DeltaTime;

            // Apply as instant healing effect
            FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            static const FSoftClassPath HealingPath(TEXT("/Game/GameplayEffects/Islands/GE_IslandHealing.GE_IslandHealing_C"));
            if (TSubclassOf<UGameplayEffect> HealingEffectClass = HealingPath.TryLoadClass<UGameplayEffect>())
            {
                if (UGameplayEffect* HealingEffect = HealingEffectClass.GetDefaultObject())
                {
                    FGameplayEffectSpec HealingSpec(HealingEffect, EffectContext, 1.0f);
                HealingSpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Healing.Amount")), HealthRegen);
                HealingSpec.SetDuration(UGameplayEffect::INSTANT_APPLICATION, false);

                ASC->ApplyGameplayEffectSpecToSelf(HealingSpec);
                }
            }
        }

        // Apply energy regeneration
        if (IslandBenefits.EnergyRegenRate > 0.0f)
        {
            float EnergyRegen = (IslandBenefits.EnergyRegenRate + EnergyBonus) * DeltaTime;

            FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
            EffectContext.AddSourceObject(this);

            static const FSoftClassPath EnergyPath(TEXT("/Game/GameplayEffects/Islands/GE_IslandEnergyRegen.GE_IslandEnergyRegen_C"));
            if (TSubclassOf<UGameplayEffect> EnergyEffectClass = EnergyPath.TryLoadClass<UGameplayEffect>())
            {
                if (UGameplayEffect* EnergyEffect = EnergyEffectClass.GetDefaultObject())
                {
                    FGameplayEffectSpec EnergySpec(EnergyEffect, EffectContext, 1.0f);
                EnergySpec.SetSetByCallerMagnitude(FGameplayTag::RequestGameplayTag(TEXT("Energy.Amount")), EnergyRegen);
                EnergySpec.SetDuration(UGameplayEffect::INSTANT_APPLICATION, false);

                ASC->ApplyGameplayEffectSpecToSelf(EnergySpec);
                }
            }
        }
    }
}

void AAuracronPrismalIsland::OptimizeIslandRendering(const FVector& ViewerLocation)
{
    if (!GetWorld())
    {
        return;
    }

    // Optimize island rendering using UE 5.6 optimization system
    float DistanceToViewer = FVector::Dist(GetActorLocation(), ViewerLocation);

    // Calculate LOD level
    int32 LODLevel = 0;
    if (DistanceToViewer > 8000.0f)
    {
        LODLevel = 3; // Lowest detail
    }
    else if (DistanceToViewer > 4000.0f)
    {
        LODLevel = 2; // Medium detail
    }
    else if (DistanceToViewer > 2000.0f)
    {
        LODLevel = 1; // High detail
    }
    else
    {
        LODLevel = 0; // Maximum detail
    }

    // Apply LOD to mesh
    if (IslandMesh)
    {
        IslandMesh->SetForcedLodModel(LODLevel + 1);
    }

    // Optimize effects based on distance
    for (UNiagaraComponent* Effect : IslandEffects)
    {
        if (Effect)
        {
            float EffectQuality = FMath::Clamp(1.0f - (DistanceToViewer / 10000.0f), 0.2f, 1.0f);
            Effect->SetVariableFloat(FName("QualityLevel"), EffectQuality);
        }
    }

    // Optimize audio
    if (IslandAudio)
    {
        float AudioVolume = FMath::Clamp(1.0f - (DistanceToViewer / 5000.0f), 0.0f, 1.0f);
        IslandAudio->SetVolumeMultiplier(AudioVolume);
    }
}

FVector AAuracronPrismalIsland::GetNearestPlayerLocation() const
{
    // Get nearest player location for optimization
    if (PlayersOnIsland.IsEmpty())
    {
        // Find nearest player in world
        if (GetWorld())
        {
            float NearestDistance = FLT_MAX;
            FVector NearestLocation = GetActorLocation();

            for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
            {
                APlayerController* PC = Iterator->Get();
                if (PC && PC->GetPawn())
                {
                    float Distance = FVector::Dist(PC->GetPawn()->GetActorLocation(), GetActorLocation());
                    if (Distance < NearestDistance)
                    {
                        NearestDistance = Distance;
                        NearestLocation = PC->GetPawn()->GetActorLocation();
                    }
                }
            }

            return NearestLocation;
        }

        return GetActorLocation();
    }

    // Return location of first player on island
    return PlayersOnIsland[0]->GetActorLocation();
}

void AAuracronPrismalIsland::ShowIslandInteractionPrompt(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Show interaction prompt using UE 5.6 UI system
    if (APlayerController* PC = Cast<APlayerController>(Player->GetController()))
    {
        // Create interaction widget
        static const FSoftClassPath InteractionWidgetPath(TEXT("/Game/UI/Islands/WBP_IslandInteraction.WBP_IslandInteraction_C"));
        if (TSubclassOf<UUserWidget> InteractionWidgetClass = InteractionWidgetPath.TryLoadClass<UUserWidget>())
        {
            if (UUserWidget* InteractionWidget = CreateWidget<UUserWidget>(GetWorld(), InteractionWidgetClass))
            {
                // Configure widget for island type
                if (UFunction* SetIslandTypeFunc = InteractionWidget->FindFunction(TEXT("SetIslandType")))
                {
                    struct { EPrismalIslandType Type; } Params;
                    Params.Type = IslandType;
                    InteractionWidget->ProcessEvent(SetIslandTypeFunc, &Params);
                }

                InteractionWidget->AddToViewport();
                PlayerInteractionWidgets.Add(Player, InteractionWidget);
            }
        }
    }
}

void AAuracronPrismalIsland::HideIslandInteractionPrompt(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // Hide interaction prompt using UE 5.6 UI cleanup
    if (TObjectPtr<UUserWidget>* Widget = PlayerInteractionWidgets.Find(Player))
    {
        if (*Widget && IsValid(*Widget))
        {
            (*Widget)->RemoveFromParent();
        }
        PlayerInteractionWidgets.Remove(Player);
    }
}

void AAuracronPrismalIsland::UpdateFlowMaterials()
{
    // Update flow materials based on island state
    if (DynamicIslandMaterial)
    {
        float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

        // Update flow-related parameters
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("FlowInfluence"), FlowInfluence);
        DynamicIslandMaterial->SetVectorParameterValue(TEXT("FlowDirection"), FlowDirection);
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("FlowTime"), CurrentTime);

        // Update energy parameters
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("EnergyBonus"), EnergyBonus / 100.0f);
        DynamicIslandMaterial->SetScalarParameterValue(TEXT("EnergyPulse"), FMath::Sin(CurrentTime * 2.0f) * 0.5f + 0.5f);
    }
}

void AAuracronPrismalIsland::UpdateFlowEffects()
{
    // Update flow effects based on current state
    for (UNiagaraComponent* Effect : IslandEffects)
    {
        if (Effect && Effect->IsActive())
        {
            Effect->SetVariableFloat(FName("FlowInfluence"), FlowInfluence);
            Effect->SetVariableVec3(FName("FlowDirection"), FlowDirection);
            Effect->SetVariableFloat(FName("EnergyBonus"), EnergyBonus);
        }
    }
}

// === Final Getter Methods ===

FAuracronIslandActivationRequirements AAuracronPrismalIsland::GetActivationRequirements() const
{
    return ActivationRequirements;
}

FAuracronIslandBenefits AAuracronPrismalIsland::GetIslandBenefits() const
{
    return IslandBenefits;
}

void AAuracronPrismalIsland::SetActivationRequirements(const FAuracronIslandActivationRequirements& NewRequirements)
{
    ActivationRequirements = NewRequirements;
}

void AAuracronPrismalIsland::SetIslandBenefits(const FAuracronIslandBenefits& NewBenefits)
{
    IslandBenefits = NewBenefits;

    // Update existing player benefits if island is active
    if (bIsActive)
    {
        for (APawn* Player : PlayersInBenefitArea)
        {
            if (Player)
            {
                RemoveIslandBenefits(Player);
                ApplyIslandBenefits(Player);
            }
        }
    }
}

void AAuracronPrismalIsland::SetAutoActivate(bool bNewAutoActivate)
{
    bAutoActivate = bNewAutoActivate;
}

// === Missing UFUNCTION Implementations for UE 5.6 ===

bool AAuracronPrismalIsland::CanActivate() const
{
    // Implementation for CanActivate using UE 5.6 activation validation
    if (bIsActive || bIsOnCooldown)
    {
        return false;
    }

    return CheckActivationRequirements();
}

void AAuracronPrismalIsland::UpdateIslandState(float DeltaTime)
{
    // Implementation for UpdateIslandState using UE 5.6 state management
    if (!bIsInitialized)
    {
        return;
    }

    // Update activation state
    UpdateActivationState(DeltaTime);

    // Update player benefits
    UpdatePlayerBenefits(DeltaTime);

    // Update island appearance
    UpdateIslandAppearance();

    // Update team control
    UpdateTeamControl();

    // Update flow effects
    UpdateFlowEffects();

    // Update flow materials
    UpdateFlowMaterials();
}

void AAuracronPrismalIsland::AddPlayerToIsland(APawn* Player)
{
    // Implementation for AddPlayerToIsland using UE 5.6 player management
    if (!Player || PlayersOnIsland.Contains(Player))
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Adding player %s to %s island"),
        *Player->GetName(), *UEnum::GetValueAsString(IslandType));

    PlayersOnIsland.Add(Player);

    // Check if island should activate
    if (!bIsActive && !bIsOnCooldown && CheckActivationRequirements())
    {
        ActivateIsland();
    }

    // Show interaction prompt
    ShowIslandInteractionPrompt(Player);

    // Play entry effect
    PlayPlayerEntryEffect(Player);

    // Apply benefits if island is active and player is in benefit area
    if (bIsActive && PlayersInBenefitArea.Contains(Player))
    {
        ApplyIslandBenefits(Player);
    }
}

void AAuracronPrismalIsland::RemovePlayerFromIsland(APawn* Player)
{
    // Implementation for RemovePlayerFromIsland using UE 5.6 player management
    if (!Player)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removing player %s from %s island"),
        *Player->GetName(), *UEnum::GetValueAsString(IslandType));

    PlayersOnIsland.Remove(Player);

    // Remove from benefit area if present
    PlayersInBenefitArea.Remove(Player);

    // Remove benefits
    RemoveIslandBenefits(Player);

    // Hide interaction prompt
    HideIslandInteractionPrompt(Player);

    // Check if island should deactivate
    if (bIsActive && PlayersOnIsland.Num() < ActivationRequirements.MinPlayersRequired)
    {
        DeactivateIsland();
    }
}

bool AAuracronPrismalIsland::IsIslandOnCooldown() const
{
    // Implementation for IsIslandOnCooldown using UE 5.6 cooldown system
    return bIsOnCooldown;
}

void AAuracronPrismalIsland::ApplyBenefitsToPlayer(APawn* Player)
{
    // Implementation for ApplyBenefitsToPlayer using UE 5.6 benefit system
    if (!Player)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying benefits to player %s on %s island"),
        *Player->GetName(), *UEnum::GetValueAsString(IslandType));

    // Use existing implementation
    ApplyIslandBenefits(Player);
}

void AAuracronPrismalIsland::RemoveBenefitsFromPlayer(APawn* Player)
{
    // Implementation for RemoveBenefitsFromPlayer using UE 5.6 benefit cleanup
    if (!Player)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removing benefits from player %s on %s island"),
        *Player->GetName(), *UEnum::GetValueAsString(IslandType));

    // Use existing implementation
    RemoveIslandBenefits(Player);
}

void AAuracronPrismalIsland::UpdateIslandVisuals()
{
    // Implementation for UpdateIslandVisuals using UE 5.6 visual system
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Updating visuals for %s island"),
        *UEnum::GetValueAsString(IslandType));

    // Update island appearance
    UpdateIslandAppearance();

    // Update flow materials
    UpdateFlowMaterials();

    // Update flow effects
    UpdateFlowEffects();

    // Update effect intensity based on current state
    float IntensityMultiplier = 1.0f;
    if (bIsActive)
    {
        IntensityMultiplier = 2.0f;
    }
    else if (bIsOnCooldown)
    {
        IntensityMultiplier = 0.3f;
    }

    UpdateEffectIntensity(FlowInfluence * IntensityMultiplier);
}

void AAuracronPrismalIsland::PlayActivationEffects()
{
    // Implementation for PlayActivationEffects using UE 5.6 effect system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Playing activation effects for %s island"),
        *UEnum::GetValueAsString(IslandType));

    // Use existing implementation
    PlayIslandEffects();

    // Additional activation-specific effects
    if (IslandEffects.IsValidIndex(1))
    {
        UNiagaraComponent* ActivationEffectComponent = IslandEffects[1];
        if (ActivationEffectComponent)
        {
            ActivationEffectComponent->SetVariableFloat(FName("ActivationBurst"), 3.0f);
            ActivationEffectComponent->SetVariableLinearColor(FName("BurstColor"), GetIslandTypeColor());
            ActivationEffectComponent->Activate();
        }
    }

    // Play activation sound
    static const FSoftObjectPath ActivationSoundPath(TEXT("/Game/Audio/Islands/MS_IslandActivationBurst.MS_IslandActivationBurst"));
    if (USoundBase* ActivationSoundAsset = Cast<USoundBase>(ActivationSoundPath.TryLoad()))
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            ActivationSoundAsset,
            GetActorLocation(),
            FRotator::ZeroRotator,
            1.0f, // Volume
            1.0f, // Pitch
            0.0f, // Start time
            nullptr, // Attenuation
            nullptr, // Concurrency
            this // Owner
        );
    }
}

void AAuracronPrismalIsland::PlayDeactivationEffects()
{
    // Implementation for PlayDeactivationEffects using UE 5.6 effect system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Playing deactivation effects for %s island"),
        *UEnum::GetValueAsString(IslandType));

    // Play deactivation VFX
    static const FSoftObjectPath DeactivationVFXPath(TEXT("/Game/VFX/Islands/NS_IslandDeactivation.NS_IslandDeactivation"));
    if (UNiagaraSystem* DeactivationVFX = Cast<UNiagaraSystem>(DeactivationVFXPath.TryLoad()))
    {
        UNiagaraComponent* DeactivationComponent = UNiagaraFunctionLibrary::SpawnSystemAttached(
            DeactivationVFX,
            RootComponent,
            NAME_None,
            FVector::ZeroVector,
            FRotator::ZeroRotator,
            EAttachLocation::KeepRelativeOffset,
            true // Auto-destroy
        );

        if (DeactivationComponent)
        {
            DeactivationComponent->SetVariableLinearColor(FName("IslandColor"), GetIslandTypeColor());
            DeactivationComponent->SetVariableFloat(FName("IslandRadius"), IslandRadius);
            DeactivationComponent->SetVariableFloat(FName("DeactivationIntensity"), 1.5f);
        }
    }

    // Play deactivation sound
    static const FSoftObjectPath DeactivationSoundPath(TEXT("/Game/Audio/Islands/MS_IslandDeactivation.MS_IslandDeactivation"));
    if (USoundBase* DeactivationSoundAsset = Cast<USoundBase>(DeactivationSoundPath.TryLoad()))
    {
        UGameplayStatics::PlaySoundAtLocation(
            GetWorld(),
            DeactivationSoundAsset,
            GetActorLocation(),
            FRotator::ZeroRotator,
            0.8f, // Volume
            1.0f, // Pitch
            0.0f, // Start time
            nullptr, // Attenuation
            nullptr, // Concurrency
            this // Owner
        );
    }

    // Stop island effects
    StopIslandEffects();
}
