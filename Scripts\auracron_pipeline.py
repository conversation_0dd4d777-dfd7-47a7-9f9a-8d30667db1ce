#!/usr/bin/env python3
"""
Auracron Game Creation Pipeline
Master script for building the complete Auracron game using all bridges
"""

import os
import sys
import json
import subprocess
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import concurrent.futures
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auracron_pipeline.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('AuracronPipeline')

class BuildConfiguration(Enum):
    """Build configurations for different deployment targets"""
    DEVELOPMENT = "Development"
    SHIPPING = "Shipping"
    DEBUG = "Debug"
    TEST = "Test"

class Platform(Enum):
    """Target platforms for builds"""
    WIN64 = "Win64"
    LINUX = "Linux"
    MAC = "Mac"
    ANDROID = "Android"
    IOS = "IOS"

@dataclass
class BridgeInfo:
    """Information about each Auracron bridge module"""
    name: str
    path: str
    dependencies: List[str]
    priority: int  # Lower number = higher priority
    category: str
    description: str
    requires_special_handling: bool = False

class AuracronPipelineManager:
    """Main pipeline manager for Auracron game creation"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.source_dir = self.project_root / "Source"
        self.scripts_dir = self.project_root / "Scripts"
        self.builds_dir = self.project_root / "Builds"
        self.logs_dir = self.project_root / "Logs"
        
        # Ensure directories exist
        self.builds_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # Initialize bridge registry
        self.bridges = self._discover_bridges()
        self.build_order = self._calculate_build_order()
        
        logger.info(f"Initialized Auracron Pipeline with {len(self.bridges)} bridges")
    
    def _discover_bridges(self) -> Dict[str, BridgeInfo]:
        """Automatically discover all bridge modules"""
        bridges = {}
        
        # Define bridge configurations with dependencies and priorities
        bridge_configs = {
            "Auracron": BridgeInfo("Auracron", "Auracron", [], 1, "Core", "Main game module"),
            "AuracronLumenBridge": BridgeInfo("AuracronLumenBridge", "AuracronLumenBridge", ["Auracron"], 2, "Rendering", "Lumen global illumination integration"),
            "AuracronNaniteBridge": BridgeInfo("AuracronNaniteBridge", "AuracronNaniteBridge", ["Auracron"], 2, "Rendering", "Nanite virtualized geometry"),
            "AuracronVFXBridge": BridgeInfo("AuracronVFXBridge", "AuracronVFXBridge", ["Auracron", "AuracronLumenBridge"], 3, "Rendering", "Visual effects system"),
            "AuracronWorldPartitionBridge": BridgeInfo("AuracronWorldPartitionBridge", "AuracronWorldPartitionBridge", ["Auracron"], 2, "World", "World partition and streaming"),
            "AuracronPCGBridge": BridgeInfo("AuracronPCGBridge", "AuracronPCGBridge", ["Auracron", "AuracronWorldPartitionBridge"], 3, "World", "Procedural content generation"),
            "AuracronFoliageBridge": BridgeInfo("AuracronFoliageBridge", "AuracronFoliageBridge", ["Auracron", "AuracronPCGBridge"], 4, "World", "Advanced foliage system"),
            "AuracronCombatBridge": BridgeInfo("AuracronCombatBridge", "AuracronCombatBridge", ["Auracron"], 2, "Gameplay", "Combat system with GAS integration"),
            "AuracronChampionsBridge": BridgeInfo("AuracronChampionsBridge", "AuracronChampionsBridge", ["Auracron", "AuracronCombatBridge"], 3, "Gameplay", "Champion system and abilities"),
            "AuracronProgressionBridge": BridgeInfo("AuracronProgressionBridge", "AuracronProgressionBridge", ["Auracron", "AuracronCombatBridge"], 3, "Gameplay", "Player progression system"),
            "AuracronHarmonyEngineBridge": BridgeInfo("AuracronHarmonyEngineBridge", "AuracronHarmonyEngineBridge", ["Auracron"], 2, "Social", "Anti-toxicity AI and community healing"),
            "AuracronVoiceBridge": BridgeInfo("AuracronVoiceBridge", "AuracronVoiceBridge", ["Auracron", "AuracronHarmonyEngineBridge"], 4, "Social", "Voice chat integration"),
            "AuracronNetworkingBridge": BridgeInfo("AuracronNetworkingBridge", "AuracronNetworkingBridge", ["Auracron"], 1, "Technical", "Advanced networking"),
            "AuracronAntiCheatBridge": BridgeInfo("AuracronAntiCheatBridge", "AuracronAntiCheatBridge", ["Auracron", "AuracronNetworkingBridge"], 2, "Technical", "Anti-cheat system"),
            "AuracronAnalyticsBridge": BridgeInfo("AuracronAnalyticsBridge", "AuracronAnalyticsBridge", ["Auracron"], 3, "Technical", "Analytics and telemetry"),
            "AuracronMetaHumanBridge": BridgeInfo("AuracronMetaHumanBridge", "AuracronMetaHumanBridge", ["Auracron"], 3, "Content", "MetaHuman integration"),
            "AuracronLoreBridge": BridgeInfo("AuracronLoreBridge", "AuracronLoreBridge", ["Auracron"], 4, "Content", "Lore and narrative system"),
            "AuracronTutorialBridge": BridgeInfo("AuracronTutorialBridge", "AuracronTutorialBridge", ["Auracron", "AuracronUIBridge"], 5, "Content", "Tutorial system"),
            "AuracronMonetizationBridge": BridgeInfo("AuracronMonetizationBridge", "AuracronMonetizationBridge", ["Auracron"], 4, "Business", "Monetization system"),
            "AuracronEOSBridge": BridgeInfo("AuracronEOSBridge", "AuracronEOSBridge", ["Auracron", "AuracronNetworkingBridge"], 3, "Business", "Epic Online Services"),
            "AuracronUIBridge": BridgeInfo("AuracronUIBridge", "AuracronUIBridge", ["Auracron"], 2, "Platform", "UI system"),
            "AuracronAudioBridge": BridgeInfo("AuracronAudioBridge", "AuracronAudioBridge", ["Auracron"], 2, "Platform", "Audio system"),
            "AuracronPhysicsBridge": BridgeInfo("AuracronPhysicsBridge", "AuracronPhysicsBridge", ["Auracron"], 2, "Platform", "Physics system"),
            "AuracronAbismoUmbrioBridge": BridgeInfo("AuracronAbismoUmbrioBridge", "AuracronAbismoUmbrioBridge", ["Auracron", "AuracronWorldPartitionBridge"], 4, "Special", "Abismo Umbrio realm"),
            "AuracronSigilosBridge": BridgeInfo("AuracronSigilosBridge", "AuracronSigilosBridge", ["Auracron"], 4, "Special", "Sigilos system"),
            "AuracronRealmsBridge": BridgeInfo("AuracronRealmsBridge", "AuracronRealmsBridge", ["Auracron", "AuracronWorldPartitionBridge"], 3, "Special", "Multi-realm system"),
            "AuracronVerticalTransitionsBridge": BridgeInfo("AuracronVerticalTransitionsBridge", "AuracronVerticalTransitionsBridge", ["Auracron", "AuracronRealmsBridge"], 5, "Special", "Vertical realm transitions"),
            "AuracronAdaptiveCreaturesBridge": BridgeInfo("AuracronAdaptiveCreaturesBridge", "AuracronAdaptiveCreaturesBridge", ["Auracron", "AuracronCombatBridge"], 4, "Special", "Adaptive AI creatures"),
            "AuracronQABridge": BridgeInfo("AuracronQABridge", "AuracronQABridge", ["Auracron"], 6, "Special", "Quality assurance automation")
        }
        
        # Verify bridges exist
        for name, info in bridge_configs.items():
            bridge_path = self.source_dir / info.path
            if bridge_path.exists():
                bridges[name] = info
                logger.info(f"Discovered bridge: {name} at {bridge_path}")
            else:
                logger.warning(f"Bridge {name} not found at {bridge_path}")
        
        return bridges
    
    def _calculate_build_order(self) -> List[str]:
        """Calculate optimal build order based on dependencies"""
        order = []
        built = set()
        
        def can_build(bridge_name: str) -> bool:
            bridge = self.bridges[bridge_name]
            return all(dep in built for dep in bridge.dependencies)
        
        # Build in dependency order
        while len(built) < len(self.bridges):
            progress_made = False
            
            # Sort by priority within each iteration
            available_bridges = [
                name for name in self.bridges.keys() 
                if name not in built and can_build(name)
            ]
            
            available_bridges.sort(key=lambda x: self.bridges[x].priority)
            
            for bridge_name in available_bridges:
                order.append(bridge_name)
                built.add(bridge_name)
                progress_made = True
            
            if not progress_made:
                remaining = set(self.bridges.keys()) - built
                logger.error(f"Circular dependency detected in bridges: {remaining}")
                break
        
        logger.info(f"Calculated build order: {' -> '.join(order)}")
        return order
    
    def build_game(self, config: BuildConfiguration, platform: Platform, 
                   clean: bool = False, parallel: bool = True) -> bool:
        """Build the complete Auracron game"""
        logger.info(f"Starting Auracron build - Config: {config.value}, Platform: {platform.value}")
        
        try:
            # Step 1: Clean if requested
            if clean:
                self._clean_build()
            
            # Step 2: Validate environment
            if not self._validate_build_environment():
                return False
            
            # Step 3: Generate project files
            if not self._generate_project_files():
                return False
            
            # Step 4: Build bridges in order
            if not self._build_bridges(config, platform, parallel):
                return False
            
            # Step 5: Build main game
            if not self._build_main_game(config, platform):
                return False
            
            # Step 6: Package game
            if not self._package_game(config, platform):
                return False
            
            # Step 7: Run tests
            if config != BuildConfiguration.SHIPPING:
                self._run_tests()
            
            logger.info("Auracron build completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Build failed: {str(e)}")
            return False
    
    def _clean_build(self):
        """Clean previous build artifacts"""
        logger.info("Cleaning previous build artifacts...")
        
        # Clean directories
        clean_dirs = ["Binaries", "Intermediate", "Build"]
        for dir_name in clean_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                import shutil
                shutil.rmtree(dir_path)
                logger.info(f"Cleaned directory: {dir_path}")
    
    def _validate_build_environment(self) -> bool:
        """Validate that build environment is properly configured"""
        logger.info("Validating build environment...")
        
        # Check for Unreal Engine
        ue_path = self._find_unreal_engine()
        if not ue_path:
            logger.error("Unreal Engine not found!")
            return False
        
        # Check for Visual Studio (Windows)
        if sys.platform == "win32":
            if not self._check_visual_studio():
                logger.error("Visual Studio not found!")
                return False
        
        # Check project file
        uproject_file = self.project_root / "Auracron.uproject"
        if not uproject_file.exists():
            logger.error(f"Project file not found: {uproject_file}")
            return False
        
        logger.info("Build environment validation passed")
        return True
    
    def _generate_project_files(self) -> bool:
        """Generate Visual Studio project files"""
        logger.info("Generating project files...")
        
        ue_path = self._find_unreal_engine()
        ubt_path = ue_path / "Engine" / "Binaries" / "DotNET" / "UnrealBuildTool" / "UnrealBuildTool.exe"
        
        cmd = [
            str(ubt_path),
            "-projectfiles",
            "-project=" + str(self.project_root / "Auracron.uproject"),
            "-game",
            "-rocket",
            "-progress"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            logger.error(f"Project file generation failed: {result.stderr}")
            return False
        
        logger.info("Project files generated successfully")
        return True
    
    def _build_bridges(self, config: BuildConfiguration, platform: Platform, parallel: bool) -> bool:
        """Build all bridges in dependency order"""
        logger.info(f"Building {len(self.bridges)} bridges...")
        
        if parallel and len(self.build_order) > 1:
            return self._build_bridges_parallel(config, platform)
        else:
            return self._build_bridges_sequential(config, platform)
    
    def _build_bridges_sequential(self, config: BuildConfiguration, platform: Platform) -> bool:
        """Build bridges one by one in dependency order"""
        for bridge_name in self.build_order:
            if not self._build_single_bridge(bridge_name, config, platform):
                logger.error(f"Failed to build bridge: {bridge_name}")
                return False
            logger.info(f"Successfully built bridge: {bridge_name}")
        
        return True
    
    def _build_bridges_parallel(self, config: BuildConfiguration, platform: Platform) -> bool:
        """Build bridges in parallel where dependencies allow"""
        logger.info("Building bridges in parallel...")
        
        built = set()
        failed = set()
        
        # Group bridges by dependency level
        levels = self._group_bridges_by_level()
        
        for level, bridge_names in levels.items():
            logger.info(f"Building level {level} bridges: {bridge_names}")
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                futures = {
                    executor.submit(self._build_single_bridge, name, config, platform): name 
                    for name in bridge_names
                }
                
                for future in concurrent.futures.as_completed(futures):
                    bridge_name = futures[future]
                    try:
                        success = future.result()
                        if success:
                            built.add(bridge_name)
                            logger.info(f"Successfully built bridge: {bridge_name}")
                        else:
                            failed.add(bridge_name)
                            logger.error(f"Failed to build bridge: {bridge_name}")
                    except Exception as e:
                        failed.add(bridge_name)
                        logger.error(f"Exception building bridge {bridge_name}: {str(e)}")
            
            # Check if any bridges failed in this level
            if failed:
                logger.error(f"Failed bridges in level {level}: {failed}")
                return False
        
        logger.info(f"All bridges built successfully: {built}")
        return True
    
    def _build_single_bridge(self, bridge_name: str, config: BuildConfiguration, platform: Platform) -> bool:
        """Build a single bridge module"""
        logger.info(f"Building bridge: {bridge_name}")
        
        ue_path = self._find_unreal_engine()
        ubt_path = ue_path / "Engine" / "Binaries" / "DotNET" / "UnrealBuildTool" / "UnrealBuildTool.exe"
        
        cmd = [
            str(ubt_path),
            bridge_name,
            platform.value,
            config.value,
            "-project=" + str(self.project_root / "Auracron.uproject"),
            "-progress",
            "-NoHotReloadFromIDE"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(self.project_root))
        
        if result.returncode != 0:
            logger.error(f"Bridge {bridge_name} build failed: {result.stderr}")
            return False
        
        return True
    
    def _build_main_game(self, config: BuildConfiguration, platform: Platform) -> bool:
        """Build the main Auracron game"""
        logger.info("Building main Auracron game...")
        
        ue_path = self._find_unreal_engine()
        ubt_path = ue_path / "Engine" / "Binaries" / "DotNET" / "UnrealBuildTool" / "UnrealBuildTool.exe"
        
        cmd = [
            str(ubt_path),
            "Auracron",
            platform.value,
            config.value,
            "-project=" + str(self.project_root / "Auracron.uproject"),
            "-progress"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=str(self.project_root))
        
        if result.returncode != 0:
            logger.error(f"Main game build failed: {result.stderr}")
            return False
        
        logger.info("Main game built successfully")
        return True
    
    def _find_unreal_engine(self) -> Optional[Path]:
        """Find Unreal Engine installation"""
        # Common UE installation paths
        possible_paths = [
            Path("C:/Program Files/Epic Games/UE_5.6"),
            Path("C:/Program Files/Epic Games/UE_5.5"),
            Path("C:/UnrealEngine"),
            Path(os.environ.get("UE5_ROOT", "")),
        ]
        
        for path in possible_paths:
            if path.exists() and (path / "Engine").exists():
                logger.info(f"Found Unreal Engine at: {path}")
                return path
        
        return None

def main():
    """Main entry point for Auracron pipeline"""
    parser = argparse.ArgumentParser(description="Auracron Game Creation Pipeline")
    parser.add_argument("--config", type=str, choices=[c.value for c in BuildConfiguration], 
                       default=BuildConfiguration.DEVELOPMENT.value, help="Build configuration")
    parser.add_argument("--platform", type=str, choices=[p.value for p in Platform], 
                       default=Platform.WIN64.value, help="Target platform")
    parser.add_argument("--clean", action="store_true", help="Clean before building")
    parser.add_argument("--parallel", action="store_true", default=True, help="Enable parallel building")
    parser.add_argument("--project-root", type=str, default=".", help="Project root directory")
    
    args = parser.parse_args()
    
    # Initialize pipeline
    pipeline = AuracronPipelineManager(args.project_root)
    
    # Build game
    config = BuildConfiguration(args.config)
    platform = Platform(args.platform)
    
    success = pipeline.build_game(config, platform, args.clean, args.parallel)
    
    if success:
        logger.info("🎉 Auracron build completed successfully!")
        sys.exit(0)
    else:
        logger.error("❌ Auracron build failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
