// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Progressão Bridge Implementation

#include "AuracronProgressionBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Interfaces/OnlineAchievementsInterface.h"
#include "Interfaces/OnlineLeaderboardInterface.h"
#include "Interfaces/OnlineStatsInterface.h"
#include "Interfaces/OnlineUserCloudInterface.h"
#include "Json.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Engine/ActorChannel.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Kismet/GameplayStatics.h"
#include "DrawDebugHelpers.h"

UAuracronProgressionBridge::UAuracronProgressionBridge(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 5.0f; // 0.2 FPS para progressão (não precisa ser frequente)
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão de progressão de conta
    CurrentAccountProgression.AccountLevel = 1;
    CurrentAccountProgression.CurrentExperience = 0;
    CurrentAccountProgression.TotalExperience = 0;
    CurrentAccountProgression.ExperienceToNextLevel = 1000;
    CurrentAccountProgression.CurrentGold = 0;
    CurrentAccountProgression.PremiumCurrency = 0;
    CurrentAccountProgression.BlueEssence = 0;
    CurrentAccountProgression.OrangeEssence = 0;
    CurrentAccountProgression.MatchesPlayed = 0;
    CurrentAccountProgression.MatchesWon = 0;
    CurrentAccountProgression.WinRate = 0.0f;
    CurrentAccountProgression.TotalPlayTimeMinutes = 0;
    CurrentAccountProgression.AccountCreationDate = FDateTime::Now();
    CurrentAccountProgression.LastOnlineDate = FDateTime::Now();
    CurrentAccountProgression.CurrentWinStreak = 0;
    CurrentAccountProgression.BestWinStreak = 0;
    CurrentAccountProgression.HonorLevel = 2;
    CurrentAccountProgression.HonorPoints = 0;
    CurrentAccountProgression.bHasActiveBattlePass = false;
    CurrentAccountProgression.BattlePassLevel = 0;
    CurrentAccountProgression.BattlePassXP = 0;
}

void UAuracronProgressionBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Progressão"));

    // Inicializar sistema
    bSystemInitialized = InitializeProgressionSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers para sincronização automática
        GetWorld()->GetTimerManager().SetTimer(
            AutoSyncTimer,
            [this]()
            {
                SyncWithFirebase();
            },
            300.0f, // A cada 5 minutos
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            MilestoneCheckTimer,
            [this]()
            {
                CheckAutomaticMilestones();
            },
            10.0f, // A cada 10 segundos
            true
        );
        
        // Carregar progressão da nuvem
        LoadProgressionFromCloud();
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Progressão inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Progressão"));
    }
}

void UAuracronProgressionBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(AutoSyncTimer);
        GetWorld()->GetTimerManager().ClearTimer(MilestoneCheckTimer);
    }
    
    // Salvar progressão antes de sair
    if (bSystemInitialized)
    {
        SaveProgressionToCloud();
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronProgressionBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronProgressionBridge, CurrentAccountProgression);
    DOREPLIFETIME(UAuracronProgressionBridge, ChampionMasteries);
    DOREPLIFETIME(UAuracronProgressionBridge, RealmMasteries);
    DOREPLIFETIME(UAuracronProgressionBridge, PendingRewards);
    DOREPLIFETIME(UAuracronProgressionBridge, AvailableMilestones);
    DOREPLIFETIME(UAuracronProgressionBridge, CompletedMilestones);
    DOREPLIFETIME(UAuracronProgressionBridge, DiscoveredSecrets);
}

void UAuracronProgressionBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar progressão automática
    ProcessAutomaticProgression(DeltaTime);
    
    // Verificar marcos automaticamente
    CheckAutomaticMilestones();
}

// === Account Progression ===

bool UAuracronProgressionBridge::GainAccountExperience(int32 ExperienceAmount)
{
    if (!bSystemInitialized || ExperienceAmount <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou experiência inválida"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    CurrentAccountProgression.CurrentExperience += ExperienceAmount;
    CurrentAccountProgression.TotalExperience += ExperienceAmount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Experiência ganha: %d (Total: %lld)"), ExperienceAmount, CurrentAccountProgression.TotalExperience);

    // Verificar se pode subir de nível
    while (CurrentAccountProgression.CurrentExperience >= CurrentAccountProgression.ExperienceToNextLevel && 
           CurrentAccountProgression.AccountLevel < 500)
    {
        LevelUpAccount();
    }

    return true;
}

bool UAuracronProgressionBridge::LevelUpAccount()
{
    if (CurrentAccountProgression.AccountLevel >= 500)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Conta já está no nível máximo"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    int32 OldLevel = CurrentAccountProgression.AccountLevel;
    
    // Subir nível
    CurrentAccountProgression.AccountLevel++;
    CurrentAccountProgression.CurrentExperience -= CurrentAccountProgression.ExperienceToNextLevel;
    
    // Calcular nova experiência necessária (fórmula exponencial)
    CurrentAccountProgression.ExperienceToNextLevel = CalculateExperienceForLevel(CurrentAccountProgression.AccountLevel + 1);

    // Conceder recompensas de nível
    FAuracronReward LevelReward;
    LevelReward.RewardID = FString::Printf(TEXT("AccountLevel_%d"), CurrentAccountProgression.AccountLevel);
    LevelReward.RewardName = FText::FromString(FString::Printf(TEXT("Level %d Reward"), CurrentAccountProgression.AccountLevel));
    LevelReward.RewardType = EAuracronRewardType::Gold;
    LevelReward.Quantity = CurrentAccountProgression.AccountLevel * 100; // 100 gold por nível
    LevelReward.RewardRarity = EAuracronRewardRarity::Common;

    GrantReward(LevelReward);

    // Conceder Blue Essence
    CurrentAccountProgression.BlueEssence += CurrentAccountProgression.AccountLevel * 50;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Conta subiu para nível %d"), CurrentAccountProgression.AccountLevel);

    // Broadcast evento
    OnAccountLevelUp.Broadcast(OldLevel, CurrentAccountProgression.AccountLevel);

    return true;
}

bool UAuracronProgressionBridge::AddGold(int32 GoldAmount)
{
    if (!bSystemInitialized || GoldAmount <= 0)
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    CurrentAccountProgression.CurrentGold += GoldAmount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Gold adicionado: %d (Total: %d)"), GoldAmount, CurrentAccountProgression.CurrentGold);

    return true;
}

bool UAuracronProgressionBridge::RemoveGold(int32 GoldAmount)
{
    if (!bSystemInitialized || GoldAmount <= 0)
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    if (CurrentAccountProgression.CurrentGold < GoldAmount)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Gold insuficiente: %d < %d"), CurrentAccountProgression.CurrentGold, GoldAmount);
        return false;
    }

    CurrentAccountProgression.CurrentGold -= GoldAmount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Gold removido: %d (Restante: %d)"), GoldAmount, CurrentAccountProgression.CurrentGold);

    return true;
}

bool UAuracronProgressionBridge::AddPremiumCurrency(int32 Amount)
{
    if (!bSystemInitialized || Amount <= 0)
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    CurrentAccountProgression.PremiumCurrency += Amount;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Moeda premium adicionada: %d (Total: %d)"), Amount, CurrentAccountProgression.PremiumCurrency);

    return true;
}

int64 UAuracronProgressionBridge::CalculateExperienceForLevel(int32 Level) const
{
    if (Level <= 1)
    {
        return 1000;
    }

    // Fórmula exponencial para experiência: Base * (Level^1.5) * Multiplier
    float BaseXP = 1000.0f;
    float LevelMultiplier = FMath::Pow(Level, 1.5f);
    float ScalingFactor = 1.2f;

    return FMath::RoundToInt(BaseXP * LevelMultiplier * ScalingFactor);
}

// === Champion Mastery ===

bool UAuracronProgressionBridge::GainChampionMasteryPoints(const FString& ChampionID, int32 Points)
{
    if (!bSystemInitialized || ChampionID.IsEmpty() || Points <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros inválidos para maestria de campeão"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Buscar maestria existente
    FAuracronChampionMasteryEntry* MasteryEntry = nullptr;
    for (FAuracronChampionMasteryEntry& Entry : ChampionMasteries)
    {
        if (Entry.ChampionID == ChampionID)
        {
            MasteryEntry = &Entry;
            break;
        }
    }

    if (!MasteryEntry)
    {
        // Criar nova maestria
        FAuracronChampionMastery NewMastery;
        NewMastery.ChampionID = ChampionID;
        NewMastery.MasteryLevel = 0;
        NewMastery.MasteryPoints = 0;
        NewMastery.PointsToNextLevel = 1800; // Pontos necessários para nível 1
        NewMastery.MatchesPlayed = 0;
        NewMastery.MatchesWon = 0;
        NewMastery.ChampionWinRate = 0.0f;
        NewMastery.AverageKDA = 0.0f;
        NewMastery.BestKDA = 0.0f;
        NewMastery.TotalPlayTime = 0;
        NewMastery.LastPlayed = FDateTime::Now();
        NewMastery.bHasMasteryToken = false;
        NewMastery.MasteryTokens = 0;

        FAuracronChampionMasteryEntry NewEntry(ChampionID, NewMastery);
        ChampionMasteries.Add(NewEntry);
        MasteryEntry = &ChampionMasteries.Last();
    }

    FAuracronChampionMastery* Mastery = &MasteryEntry->Mastery;

    Mastery->MasteryPoints += Points;
    Mastery->LastPlayed = FDateTime::Now();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Pontos de maestria ganhos para %s: %d (Total: %d)"), *ChampionID, Points, Mastery->MasteryPoints);

    // Verificar se pode subir de nível
    while (Mastery->MasteryPoints >= Mastery->PointsToNextLevel && Mastery->MasteryLevel < 10)
    {
        LevelUpChampionMastery(ChampionID);
    }

    return true;
}

FAuracronChampionMastery UAuracronProgressionBridge::GetChampionMastery(const FString& ChampionID) const
{
    // Buscar maestria existente
    for (const FAuracronChampionMasteryEntry& Entry : ChampionMasteries)
    {
        if (Entry.ChampionID == ChampionID)
        {
            return Entry.Mastery;
        }
    }

    // Retornar maestria vazia se não encontrada
    FAuracronChampionMastery EmptyMastery;
    EmptyMastery.ChampionID = ChampionID;
    return EmptyMastery;
}

TArray<FAuracronChampionMastery> UAuracronProgressionBridge::GetAllChampionMasteries() const
{
    TArray<FAuracronChampionMastery> AllMasteries;

    for (const FAuracronChampionMasteryEntry& Entry : ChampionMasteries)
    {
        AllMasteries.Add(Entry.Mastery);
    }

    // Ordenar por nível de maestria (decrescente)
    AllMasteries.Sort([](const FAuracronChampionMastery& A, const FAuracronChampionMastery& B)
    {
        if (A.MasteryLevel != B.MasteryLevel)
        {
            return A.MasteryLevel > B.MasteryLevel;
        }
        return A.MasteryPoints > B.MasteryPoints;
    });

    return AllMasteries;
}

bool UAuracronProgressionBridge::LevelUpChampionMastery(const FString& ChampionID)
{
    // Buscar maestria existente
    FAuracronChampionMasteryEntry* MasteryEntry = nullptr;
    for (FAuracronChampionMasteryEntry& Entry : ChampionMasteries)
    {
        if (Entry.ChampionID == ChampionID)
        {
            MasteryEntry = &Entry;
            break;
        }
    }

    if (!MasteryEntry || MasteryEntry->Mastery.MasteryLevel >= 10)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Maestria não encontrada ou já no nível máximo: %s"), *ChampionID);
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    FAuracronChampionMastery* Mastery = &MasteryEntry->Mastery;
    int32 OldLevel = Mastery->MasteryLevel;

    // Subir nível
    Mastery->MasteryLevel++;
    Mastery->MasteryPoints -= Mastery->PointsToNextLevel;

    // Calcular pontos necessários para próximo nível
    int32 BasePoints = 1800;
    float LevelMultiplier = FMath::Pow(Mastery->MasteryLevel + 1, 1.3f);
    Mastery->PointsToNextLevel = FMath::RoundToInt(BasePoints * LevelMultiplier);

    // Conceder recompensas de maestria
    FAuracronReward MasteryReward;
    MasteryReward.RewardID = FString::Printf(TEXT("ChampionMastery_%s_%d"), *ChampionID, Mastery->MasteryLevel);
    MasteryReward.RewardName = FText::FromString(FString::Printf(TEXT("Mastery %d Reward"), Mastery->MasteryLevel));
    MasteryReward.RewardType = EAuracronRewardType::Essence;
    MasteryReward.Quantity = Mastery->MasteryLevel * 500; // 500 BE por nível
    MasteryReward.RewardRarity = EAuracronRewardRarity::Uncommon;

    GrantReward(MasteryReward);

    // Conceder token de maestria em níveis específicos
    if (Mastery->MasteryLevel == 5 || Mastery->MasteryLevel == 7 || Mastery->MasteryLevel == 10)
    {
        Mastery->MasteryTokens++;
        Mastery->bHasMasteryToken = true;

        FAuracronReward TokenReward;
        TokenReward.RewardID = FString::Printf(TEXT("MasteryToken_%s_%d"), *ChampionID, Mastery->MasteryLevel);
        TokenReward.RewardName = FText::FromString(TEXT("Mastery Token"));
        TokenReward.RewardType = EAuracronRewardType::Essence;
        TokenReward.Quantity = 1;
        TokenReward.RewardRarity = EAuracronRewardRarity::Epic;

        GrantReward(TokenReward);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Maestria de %s subiu para nível %d"), *ChampionID, Mastery->MasteryLevel);

    // Broadcast evento
    OnChampionMasteryLevelUp.Broadcast(ChampionID, OldLevel, Mastery->MasteryLevel);

    return true;
}

// === Realm Mastery ===

bool UAuracronProgressionBridge::GainRealmMasteryPoints(int32 RealmType, int32 Points)
{
    if (!bSystemInitialized || RealmType < 0 || RealmType > 2 || Points <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros inválidos para maestria de realm"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Buscar maestria existente
    FAuracronRealmMasteryEntry* MasteryEntry = nullptr;
    for (FAuracronRealmMasteryEntry& Entry : RealmMasteries)
    {
        if (Entry.RealmID == RealmType)
        {
            MasteryEntry = &Entry;
            break;
        }
    }

    if (!MasteryEntry)
    {
        // Criar nova maestria
        FAuracronRealmMastery NewMastery;
        NewMastery.RealmType = RealmType;
        NewMastery.MasteryLevel = 0;
        NewMastery.MasteryPoints = 0;
        NewMastery.TimeSpentInRealm = 0;
        NewMastery.ObjectivesCompleted = 0;
        NewMastery.KillsInRealm = 0;
        NewMastery.DeathsInRealm = 0;
        NewMastery.AssistsInRealm = 0;
        NewMastery.ExplorationPercentage = 0.0f;

        FAuracronRealmMasteryEntry NewEntry;
        NewEntry.RealmID = RealmType;
        NewEntry.Mastery = NewMastery;
        RealmMasteries.Add(NewEntry);
        MasteryEntry = &RealmMasteries.Last();
    }

    FAuracronRealmMastery* Mastery = &MasteryEntry->Mastery;

    Mastery->MasteryPoints += Points;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Pontos de maestria de realm %d ganhos: %d (Total: %d)"), RealmType, Points, Mastery->MasteryPoints);

    // Verificar se pode subir de nível (lógica similar à maestria de campeão)
    int32 PointsNeeded = (Mastery->MasteryLevel + 1) * 2000; // 2000 pontos por nível
    if (Mastery->MasteryPoints >= PointsNeeded && Mastery->MasteryLevel < 10)
    {
        Mastery->MasteryLevel++;
        Mastery->MasteryPoints -= PointsNeeded;

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Maestria de realm %d subiu para nível %d"), RealmType, Mastery->MasteryLevel);
    }

    return true;
}

// === Missing Function Implementations ===

FAuracronRealmMastery UAuracronProgressionBridge::GetRealmMastery(int32 RealmType) const
{
    // Buscar maestria existente
    for (const FAuracronRealmMasteryEntry& Entry : RealmMasteries)
    {
        if (Entry.RealmID == RealmType)
        {
            return Entry.Mastery;
        }
    }

    // Retornar maestria vazia se não encontrada
    FAuracronRealmMastery EmptyMastery;
    EmptyMastery.RealmType = RealmType;
    EmptyMastery.MasteryLevel = 0;
    EmptyMastery.MasteryPoints = 0;
    EmptyMastery.TimeSpentInRealm = 0;
    EmptyMastery.ObjectivesCompleted = 0;
    EmptyMastery.KillsInRealm = 0;
    EmptyMastery.DeathsInRealm = 0;
    EmptyMastery.AssistsInRealm = 0;
    EmptyMastery.ExplorationPercentage = 0.0f;
    return EmptyMastery;
}

bool UAuracronProgressionBridge::RegisterTimeInRealm(int32 RealmType, int32 TimeMinutes)
{
    if (!bSystemInitialized || RealmType < 0 || RealmType > 2 || TimeMinutes <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros inválidos para tempo no realm"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Buscar maestria existente
    FAuracronRealmMasteryEntry* MasteryEntry = nullptr;
    for (FAuracronRealmMasteryEntry& Entry : RealmMasteries)
    {
        if (Entry.RealmID == RealmType)
        {
            MasteryEntry = &Entry;
            break;
        }
    }

    if (!MasteryEntry)
    {
        // Criar nova maestria se não existir
        FAuracronRealmMastery NewMastery;
        NewMastery.RealmType = RealmType;
        NewMastery.MasteryLevel = 0;
        NewMastery.MasteryPoints = 0;
        NewMastery.TimeSpentInRealm = 0;
        NewMastery.ObjectivesCompleted = 0;
        NewMastery.KillsInRealm = 0;
        NewMastery.DeathsInRealm = 0;
        NewMastery.AssistsInRealm = 0;
        NewMastery.ExplorationPercentage = 0.0f;

        FAuracronRealmMasteryEntry NewEntry;
        NewEntry.RealmID = RealmType;
        NewEntry.Mastery = NewMastery;
        RealmMasteries.Add(NewEntry);
        MasteryEntry = &RealmMasteries.Last();
    }

    // Registrar tempo
    MasteryEntry->Mastery.TimeSpentInRealm += TimeMinutes;

    // Ganhar pontos de maestria baseado no tempo (1 ponto por minuto)
    GainRealmMasteryPoints(RealmType, TimeMinutes);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Tempo registrado no realm %d: %d minutos (Total: %d)"),
           RealmType, TimeMinutes, MasteryEntry->Mastery.TimeSpentInRealm);

    return true;
}

bool UAuracronProgressionBridge::DiscoverRealmSecret(int32 RealmType, const FString& SecretID)
{
    if (!bSystemInitialized || RealmType < 0 || RealmType > 2 || SecretID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros inválidos para descoberta de segredo"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Verificar se o segredo já foi descoberto
    for (const FString& DiscoveredSecret : DiscoveredSecrets)
    {
        if (DiscoveredSecret == SecretID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Segredo já descoberto: %s"), *SecretID);
            return false;
        }
    }

    // Adicionar segredo à lista de descobertos
    DiscoveredSecrets.Add(SecretID);

    // Ganhar pontos de maestria de realm
    GainRealmMasteryPoints(RealmType, 500); // 500 pontos por segredo descoberto

    // Criar recompensa por descoberta
    FAuracronReward SecretReward;
    SecretReward.RewardID = FString::Printf(TEXT("Secret_%s"), *SecretID);
    SecretReward.RewardName = FText::FromString(TEXT("Secret Discovery Reward"));
    SecretReward.RewardType = EAuracronRewardType::Essence;
    SecretReward.Quantity = 1000; // 1000 BE por segredo
    SecretReward.RewardRarity = EAuracronRewardRarity::Rare;

    GrantReward(SecretReward);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Segredo descoberto no realm %d: %s"), RealmType, *SecretID);

    return true;
}

bool UAuracronProgressionBridge::CompleteMilestone(const FString& MilestoneID)
{
    if (!bSystemInitialized || MilestoneID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: ID de marco inválido"));
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Verificar se o marco já foi completado
    for (const FAuracronProgressionMilestone& CompletedMilestone : CompletedMilestones)
    {
        if (CompletedMilestone.MilestoneID == MilestoneID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Marco já completado: %s"), *MilestoneID);
            return false;
        }
    }

    // Buscar marco disponível
    FAuracronProgressionMilestone* TargetMilestone = nullptr;
    for (FAuracronProgressionMilestone& Milestone : AvailableMilestones)
    {
        if (Milestone.MilestoneID == MilestoneID)
        {
            TargetMilestone = &Milestone;
            break;
        }
    }

    if (!TargetMilestone)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Marco não encontrado: %s"), *MilestoneID);
        return false;
    }

    // Marcar como completado
    TargetMilestone->bCompleted = true;
    TargetMilestone->CompletionDate = FDateTime::Now();
    CompletedMilestones.Add(*TargetMilestone);

    // Conceder recompensas do marco
    for (const FAuracronReward& Reward : TargetMilestone->MilestoneRewards)
    {
        GrantReward(Reward);
    }

    // Ganhar experiência de conta (valor fixo baseado na prioridade)
    int32 ExperienceReward = TargetMilestone->DisplayPriority * 500;
    GainAccountExperience(ExperienceReward);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Marco completado: %s"), *MilestoneID);

    // Broadcast evento
    OnMilestoneCompleted.Broadcast(MilestoneID, TargetMilestone->MilestoneRewards);

    return true;
}

TArray<FAuracronProgressionMilestone> UAuracronProgressionBridge::GetAvailableMilestones() const
{
    TArray<FAuracronProgressionMilestone> Available;

    for (const FAuracronProgressionMilestone& Milestone : AvailableMilestones)
    {
        if (!Milestone.bCompleted)
        {
            Available.Add(Milestone);
        }
    }

    return Available;
}

TArray<FAuracronProgressionMilestone> UAuracronProgressionBridge::GetCompletedMilestones() const
{
    return CompletedMilestones;
}

bool UAuracronProgressionBridge::CheckMilestoneProgress(const FString& MilestoneID, int32 CurrentProgress)
{
    if (!bSystemInitialized || MilestoneID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Buscar marco
    FAuracronProgressionMilestone* TargetMilestone = nullptr;
    for (FAuracronProgressionMilestone& Milestone : AvailableMilestones)
    {
        if (Milestone.MilestoneID == MilestoneID)
        {
            TargetMilestone = &Milestone;
            break;
        }
    }

    if (!TargetMilestone || TargetMilestone->bCompleted)
    {
        return false;
    }

    // Atualizar progresso
    TargetMilestone->CurrentValue = CurrentProgress;

    // Verificar se foi completado
    if (CurrentProgress >= TargetMilestone->RequiredValue)
    {
        return CompleteMilestone(MilestoneID);
    }

    return true;
}

bool UAuracronProgressionBridge::GrantReward(const FAuracronReward& Reward)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Adicionar à lista de recompensas pendentes
    PendingRewards.Add(Reward);

    // Aplicar recompensa imediatamente baseado no tipo
    switch (Reward.RewardType)
    {
        case EAuracronRewardType::Gold:
            AddGold(Reward.Quantity);
            break;
        case EAuracronRewardType::PremiumCurrency:
            AddPremiumCurrency(Reward.Quantity);
            break;
        case EAuracronRewardType::Essence:
            CurrentAccountProgression.BlueEssence += Reward.Quantity;
            break;
        case EAuracronRewardType::Experience:
            GainAccountExperience(Reward.Quantity);
            break;
        default:
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Recompensa concedida: %s (Tipo: %d, Quantidade: %d)"),
           *Reward.RewardName.ToString(), (int32)Reward.RewardType, Reward.Quantity);

    // Broadcast evento
    OnRewardGranted.Broadcast(Reward);

    return true;
}

TArray<FAuracronReward> UAuracronProgressionBridge::GetPendingRewards() const
{
    return PendingRewards;
}

bool UAuracronProgressionBridge::ClaimReward(const FString& RewardID)
{
    if (!bSystemInitialized || RewardID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    // Buscar recompensa pendente
    for (int32 i = 0; i < PendingRewards.Num(); i++)
    {
        if (PendingRewards[i].RewardID == RewardID)
        {
            FAuracronReward ClaimedReward = PendingRewards[i];
            PendingRewards.RemoveAt(i);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Recompensa reivindicada: %s"), *RewardID);

            // Broadcast evento
            OnRewardClaimed.Broadcast(ClaimedReward);

            return true;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Recompensa não encontrada: %s"), *RewardID);
    return false;
}

bool UAuracronProgressionBridge::ClaimAllRewards()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ProgressionMutex);

    int32 ClaimedCount = PendingRewards.Num();

    // Broadcast para cada recompensa
    for (const FAuracronReward& Reward : PendingRewards)
    {
        OnRewardClaimed.Broadcast(Reward);
    }

    // Limpar todas as recompensas pendentes
    PendingRewards.Empty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d recompensas reivindicadas"), ClaimedCount);

    return ClaimedCount > 0;
}

bool UAuracronProgressionBridge::SaveProgressionToCloud()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado para salvar na nuvem"));
        return false;
    }

    // Simular salvamento na nuvem (implementação real dependeria do serviço específico)
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Salvando progressão na nuvem..."));

    // Criar JSON com dados de progressão
    TSharedPtr<FJsonObject> ProgressionJson = MakeShareable(new FJsonObject);

    // Dados da conta
    TSharedPtr<FJsonObject> AccountJson = MakeShareable(new FJsonObject);
    AccountJson->SetNumberField(TEXT("AccountLevel"), CurrentAccountProgression.AccountLevel);
    AccountJson->SetNumberField(TEXT("TotalExperience"), CurrentAccountProgression.TotalExperience);
    AccountJson->SetNumberField(TEXT("CurrentGold"), CurrentAccountProgression.CurrentGold);
    AccountJson->SetNumberField(TEXT("PremiumCurrency"), CurrentAccountProgression.PremiumCurrency);
    AccountJson->SetNumberField(TEXT("BlueEssence"), CurrentAccountProgression.BlueEssence);
    ProgressionJson->SetObjectField(TEXT("Account"), AccountJson);

    // Maestrias de campeão
    TArray<TSharedPtr<FJsonValue>> ChampionMasteriesJson;
    for (const FAuracronChampionMasteryEntry& Entry : ChampionMasteries)
    {
        TSharedPtr<FJsonObject> MasteryJson = MakeShareable(new FJsonObject);
        MasteryJson->SetStringField(TEXT("ChampionID"), Entry.ChampionID);
        MasteryJson->SetNumberField(TEXT("MasteryLevel"), Entry.Mastery.MasteryLevel);
        MasteryJson->SetNumberField(TEXT("MasteryPoints"), Entry.Mastery.MasteryPoints);
        ChampionMasteriesJson.Add(MakeShareable(new FJsonValueObject(MasteryJson)));
    }
    ProgressionJson->SetArrayField(TEXT("ChampionMasteries"), ChampionMasteriesJson);

    // Simular sucesso
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Progressão salva na nuvem com sucesso"));
    return true;
}

bool UAuracronProgressionBridge::LoadProgressionFromCloud()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado para carregar da nuvem"));
        return false;
    }

    // Simular carregamento da nuvem
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Carregando progressão da nuvem..."));

    // Simular sucesso
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Progressão carregada da nuvem com sucesso"));
    return true;
}

bool UAuracronProgressionBridge::SyncWithFirebase()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Simular sincronização com Firebase
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sincronizando com Firebase..."));

    // Salvar dados locais na nuvem
    SaveProgressionToCloud();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sincronização com Firebase concluída"));
    return true;
}

bool UAuracronProgressionBridge::BackupProgression()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Simular backup
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Criando backup da progressão..."));

    // Criar backup local
    SaveProgressionToCloud();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Backup da progressão criado com sucesso"));
    return true;
}

void UAuracronProgressionBridge::OnRep_PendingRewards()
{
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Recompensas pendentes atualizadas: %d"), PendingRewards.Num());
}

bool UAuracronProgressionBridge::InitializeProgressionSystem()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Inicializando sistema de progressão..."));

    // Inicializar marcos padrão
    InitializeDefaultMilestones();

    // Configurar sistema
    bSystemInitialized = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de progressão inicializado com sucesso"));
    return true;
}

void UAuracronProgressionBridge::ProcessAutomaticProgression(float DeltaTime)
{
    if (!bSystemInitialized)
    {
        return;
    }

    // Processar progressão automática baseada no tempo
    // Por exemplo, regeneração de recursos, verificação de marcos temporais, etc.

    // Atualizar tempo total de jogo
    CurrentAccountProgression.TotalPlayTimeMinutes += FMath::RoundToInt(DeltaTime / 60.0f);
}

void UAuracronProgressionBridge::CheckAutomaticMilestones()
{
    if (!bSystemInitialized)
    {
        return;
    }

    // Verificar marcos automáticos baseados em estatísticas
    for (FAuracronProgressionMilestone& Milestone : AvailableMilestones)
    {
        if (Milestone.bCompleted)
        {
            continue;
        }

        // Verificar diferentes tipos de marcos automáticos
        if (Milestone.MilestoneID.Contains(TEXT("PlayTime")))
        {
            CheckMilestoneProgress(Milestone.MilestoneID, CurrentAccountProgression.TotalPlayTimeMinutes);
        }
        else if (Milestone.MilestoneID.Contains(TEXT("Level")))
        {
            CheckMilestoneProgress(Milestone.MilestoneID, CurrentAccountProgression.AccountLevel);
        }
        else if (Milestone.MilestoneID.Contains(TEXT("Gold")))
        {
            CheckMilestoneProgress(Milestone.MilestoneID, CurrentAccountProgression.CurrentGold);
        }
    }
}

void UAuracronProgressionBridge::InitializeDefaultMilestones()
{
    // Limpar marcos existentes
    AvailableMilestones.Empty();

    // Marco de nível
    FAuracronProgressionMilestone LevelMilestone;
    LevelMilestone.MilestoneID = TEXT("Level_10");
    LevelMilestone.MilestoneName = FText::FromString(TEXT("Reach Level 10"));
    LevelMilestone.MilestoneDescription = FText::FromString(TEXT("Reach account level 10"));
    LevelMilestone.ProgressionType = EAuracronProgressionType::AccountLevel;
    LevelMilestone.RequiredValue = 10;
    LevelMilestone.CurrentValue = 0;
    LevelMilestone.DisplayPriority = 8;
    LevelMilestone.bCompleted = false;

    // Adicionar recompensa
    FAuracronReward LevelReward;
    LevelReward.RewardID = TEXT("Level10_Reward");
    LevelReward.RewardName = FText::FromString(TEXT("Level 10 Achievement"));
    LevelReward.RewardType = EAuracronRewardType::Gold;
    LevelReward.Quantity = 2000;
    LevelReward.RewardRarity = EAuracronRewardRarity::Uncommon;
    LevelMilestone.MilestoneRewards.Add(LevelReward);

    AvailableMilestones.Add(LevelMilestone);

    // Marco de tempo de jogo
    FAuracronProgressionMilestone PlayTimeMilestone;
    PlayTimeMilestone.MilestoneID = TEXT("PlayTime_60");
    PlayTimeMilestone.MilestoneName = FText::FromString(TEXT("Play for 1 Hour"));
    PlayTimeMilestone.MilestoneDescription = FText::FromString(TEXT("Play for a total of 60 minutes"));
    PlayTimeMilestone.ProgressionType = EAuracronProgressionType::PlayTime;
    PlayTimeMilestone.RequiredValue = 60;
    PlayTimeMilestone.CurrentValue = 0;
    PlayTimeMilestone.DisplayPriority = 5;
    PlayTimeMilestone.bCompleted = false;

    FAuracronReward PlayTimeReward;
    PlayTimeReward.RewardID = TEXT("PlayTime60_Reward");
    PlayTimeReward.RewardName = FText::FromString(TEXT("First Hour Achievement"));
    PlayTimeReward.RewardType = EAuracronRewardType::Essence;
    PlayTimeReward.Quantity = 500;
    PlayTimeReward.RewardRarity = EAuracronRewardRarity::Common;
    PlayTimeMilestone.MilestoneRewards.Add(PlayTimeReward);

    AvailableMilestones.Add(PlayTimeMilestone);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: %d marcos padrão inicializados"), AvailableMilestones.Num());
}
