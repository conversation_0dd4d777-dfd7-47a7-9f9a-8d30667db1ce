#!/usr/bin/env python3
"""
Auracron Setup Script
Initial setup and configuration for the Auracron game development environment
"""

import os
import sys
import json
import logging
import subprocess
import shutil
from pathlib import Path
from typing import Dict, List, Optional
import requests
import zipfile

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('AuracronSetup')

class AuracronSetup:
    """Setup manager for Auracron development environment"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.scripts_dir = self.project_root / "Scripts"
        self.requirements_file = self.scripts_dir / "requirements.txt"
        
        logger.info(f"Initializing Auracron setup in: {self.project_root}")
    
    def setup_complete_environment(self) -> bool:
        """Setup complete development environment"""
        logger.info("🚀 Setting up complete Auracron development environment")
        
        try:
            # Step 1: Validate system requirements
            if not self._validate_system_requirements():
                return False
            
            # Step 2: Setup Python environment
            if not self._setup_python_environment():
                return False
            
            # Step 3: Install dependencies
            if not self._install_dependencies():
                return False
            
            # Step 4: Setup Unreal Engine integration
            if not self._setup_unreal_engine_integration():
                return False
            
            # Step 5: Initialize bridge directories
            if not self._initialize_bridge_directories():
                return False
            
            # Step 6: Setup Harmony Engine
            if not self._setup_harmony_engine():
                return False
            
            # Step 7: Create development scripts
            if not self._create_development_scripts():
                return False
            
            # Step 8: Setup version control hooks
            if not self._setup_version_control():
                return False
            
            # Step 9: Generate initial configuration
            if not self._generate_initial_configuration():
                return False
            
            logger.info("✅ Auracron development environment setup completed successfully!")
            self._print_next_steps()
            
            return True
            
        except Exception as e:
            logger.error(f"Setup failed: {str(e)}")
            return False
    
    def _validate_system_requirements(self) -> bool:
        """Validate system requirements"""
        logger.info("Validating system requirements...")
        
        requirements = {
            "python": {"min_version": "3.8", "command": "python --version"},
            "git": {"min_version": "2.0", "command": "git --version"},
            "visual_studio": {"required": True, "platform": "windows"},
            "unreal_engine": {"min_version": "5.5", "required": True}
        }
        
        # Check Python
        try:
            result = subprocess.run(["python", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("Python not found or not accessible")
                return False
            logger.info(f"✅ Python: {result.stdout.strip()}")
        except:
            logger.error("Python not found")
            return False
        
        # Check Git
        try:
            result = subprocess.run(["git", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("Git not found")
                return False
            logger.info(f"✅ Git: {result.stdout.strip()}")
        except:
            logger.error("Git not found")
            return False
        
        # Check Unreal Engine
        ue_path = self._find_unreal_engine()
        if not ue_path:
            logger.error("Unreal Engine 5.5+ not found")
            logger.error("Please install Unreal Engine 5.5 or later from Epic Games Launcher")
            return False
        
        logger.info(f"✅ Unreal Engine: {ue_path}")
        
        logger.info("✅ System requirements validated")
        return True
    
    def _setup_python_environment(self) -> bool:
        """Setup Python virtual environment"""
        logger.info("Setting up Python environment...")
        
        venv_dir = self.project_root / "venv"
        
        # Create virtual environment
        if not venv_dir.exists():
            result = subprocess.run([sys.executable, "-m", "venv", str(venv_dir)])
            if result.returncode != 0:
                logger.error("Failed to create virtual environment")
                return False
        
        # Activate virtual environment and install pip
        if sys.platform == "win32":
            pip_path = venv_dir / "Scripts" / "pip.exe"
            python_path = venv_dir / "Scripts" / "python.exe"
        else:
            pip_path = venv_dir / "bin" / "pip"
            python_path = venv_dir / "bin" / "python"
        
        # Upgrade pip
        result = subprocess.run([str(python_path), "-m", "pip", "install", "--upgrade", "pip"])
        if result.returncode != 0:
            logger.error("Failed to upgrade pip")
            return False
        
        logger.info("✅ Python environment setup completed")
        return True
    
    def _install_dependencies(self) -> bool:
        """Install Python dependencies"""
        logger.info("Installing Python dependencies...")
        
        # Create requirements.txt if it doesn't exist
        if not self.requirements_file.exists():
            self._create_requirements_file()
        
        # Install dependencies
        venv_dir = self.project_root / "venv"
        if sys.platform == "win32":
            pip_path = venv_dir / "Scripts" / "pip.exe"
        else:
            pip_path = venv_dir / "bin" / "pip"
        
        result = subprocess.run([str(pip_path), "install", "-r", str(self.requirements_file)])
        if result.returncode != 0:
            logger.error("Failed to install dependencies")
            return False
        
        logger.info("✅ Dependencies installed successfully")
        return True
    
    def _create_requirements_file(self):
        """Create requirements.txt with necessary dependencies"""
        requirements = [
            "# Auracron Development Dependencies",
            "",
            "# Core utilities",
            "pyyaml>=6.0",
            "requests>=2.28.0",
            "click>=8.0.0",
            "colorama>=0.4.4",
            "",
            "# Build and deployment",
            "boto3>=1.26.0",  # For AWS deployment
            "azure-storage-blob>=12.14.0",  # For Azure deployment
            "",
            "# Testing and quality assurance", 
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-xdist>=3.0.0",
            "",
            "# Machine Learning (for Harmony Engine)",
            "numpy>=1.21.0",
            "scikit-learn>=1.1.0",
            "tensorflow>=2.10.0",  # For ML model training
            "torch>=1.12.0",  # Alternative ML framework
            "",
            "# Data processing",
            "pandas>=1.5.0",
            "matplotlib>=3.5.0",
            "",
            "# Development tools",
            "black>=22.0.0",  # Code formatting
            "flake8>=5.0.0",  # Linting
            "mypy>=0.991",  # Type checking
            "",
            "# Documentation",
            "sphinx>=5.0.0",
            "sphinx-rtd-theme>=1.0.0"
        ]
        
        with open(self.requirements_file, 'w') as f:
            f.write('\n'.join(requirements))
        
        logger.info(f"Created requirements.txt with {len([r for r in requirements if not r.startswith('#') and r.strip()])} packages")
    
    def _setup_harmony_engine(self) -> bool:
        """Setup Harmony Engine specific components"""
        logger.info("Setting up Harmony Engine...")
        
        # Create Harmony Engine directories
        harmony_dirs = [
            "Content/HarmonyEngine/MLModels",
            "Content/HarmonyEngine/TrainingData", 
            "Content/HarmonyEngine/UI",
            "Content/HarmonyEngine/Audio",
            "Content/HarmonyEngine/VFX",
            "Config/HarmonyEngine",
            "Saved/HarmonyEngine/Logs",
            "Saved/HarmonyEngine/Analytics"
        ]
        
        for dir_path in harmony_dirs:
            full_path = self.project_root / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {dir_path}")
        
        # Create initial ML model configuration
        ml_config = {
            "models": {
                "behavioral_prediction": {
                    "type": "classification",
                    "features": ["toxicity_score", "positivity_score", "session_duration", "action_frequency"],
                    "target": "behavior_category",
                    "accuracy_threshold": 0.75
                },
                "emotional_prediction": {
                    "type": "regression",
                    "features": ["emotional_history", "behavior_patterns", "social_interactions"],
                    "target": "emotional_state",
                    "accuracy_threshold": 0.70
                },
                "intervention_effectiveness": {
                    "type": "classification",
                    "features": ["intervention_type", "player_profile", "context"],
                    "target": "intervention_success",
                    "accuracy_threshold": 0.80
                }
            },
            "training": {
                "batch_size": 32,
                "learning_rate": 0.001,
                "epochs": 100,
                "validation_split": 0.2
            }
        }
        
        ml_config_path = self.project_root / "Config" / "HarmonyEngine" / "ml_config.json"
        with open(ml_config_path, 'w') as f:
            json.dump(ml_config, f, indent=2)
        
        # Create sample training data structure
        sample_data = {
            "behavioral_data": [
                {
                    "player_id": "sample_player_1",
                    "toxicity_score": 0.2,
                    "positivity_score": 0.8,
                    "session_duration": 3600,
                    "action_frequency": 45,
                    "behavior_category": "positive"
                }
            ],
            "emotional_data": [
                {
                    "player_id": "sample_player_1", 
                    "emotional_state": "happy",
                    "context": "successful_match",
                    "timestamp": "2024-01-01T12:00:00Z"
                }
            ]
        }
        
        training_data_path = self.project_root / "Content" / "HarmonyEngine" / "TrainingData" / "sample_data.json"
        with open(training_data_path, 'w') as f:
            json.dump(sample_data, f, indent=2)
        
        logger.info("✅ Harmony Engine setup completed")
        return True
    
    def _create_development_scripts(self) -> bool:
        """Create convenient development scripts"""
        logger.info("Creating development scripts...")
        
        # Create batch files for Windows
        if sys.platform == "win32":
            self._create_windows_scripts()
        
        # Create shell scripts for Unix-like systems
        else:
            self._create_unix_scripts()
        
        logger.info("✅ Development scripts created")
        return True
    
    def _create_windows_scripts(self):
        """Create Windows batch scripts"""
        scripts = {
            "build_auracron.bat": [
                "@echo off",
                "echo Building Auracron with all bridges...",
                "python Scripts/auracron_master.py --config Development --platform Win64 --enable-harmony",
                "pause"
            ],
            "build_harmony_only.bat": [
                "@echo off", 
                "echo Building Harmony Engine only...",
                "python Scripts/auracron_master.py --harmony-only --config Development",
                "pause"
            ],
            "run_tests.bat": [
                "@echo off",
                "echo Running Auracron tests...",
                "python Scripts/test_automation.py --harmony-only",
                "pause"
            ],
            "validate_setup.bat": [
                "@echo off",
                "echo Validating Auracron setup...",
                "python Scripts/auracron_master.py --validate-only",
                "pause"
            ]
        }
        
        for script_name, commands in scripts.items():
            script_path = self.project_root / script_name
            with open(script_path, 'w') as f:
                f.write('\n'.join(commands))
            logger.info(f"Created script: {script_name}")
    
    def _print_next_steps(self):
        """Print next steps for the developer"""
        print("\n" + "="*60)
        print("🎉 AURACRON SETUP COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("\n📋 NEXT STEPS:")
        print("\n1. 🏗️ BUILD THE GAME:")
        print("   python Scripts/auracron_master.py --config Development --platform Win64")
        print("\n2. 🤝 TEST HARMONY ENGINE:")
        print("   python Scripts/test_automation.py --harmony-only")
        print("\n3. 🎮 RUN FULL BUILD:")
        print("   python Scripts/auracron_master.py --enable-harmony --parallel")
        print("\n4. 🚀 DEPLOY TO STAGING:")
        print("   python Scripts/deployment_pipeline.py --target staging --platform Windows")
        
        print("\n🤝 HARMONY ENGINE FEATURES:")
        print("   ✅ Emotional Intelligence AI")
        print("   ✅ Predictive Intervention System") 
        print("   ✅ Community Healing Manager")
        print("   ✅ Real-Time Toxicity Detection")
        print("   ✅ Rewards & Progression System")
        print("   ✅ Machine Learning Models")
        
        print("\n🏗️ AVAILABLE BRIDGES:")
        bridges = [
            "Core Game (Auracron)",
            "Harmony Engine (Anti-Toxicity AI)", 
            "Combat System (GAS Integration)",
            "Champions System",
            "Rendering (Lumen + Nanite + VFX)",
            "World (Partition + PCG + Foliage)",
            "Networking (Advanced Multiplayer)",
            "Anti-Cheat System",
            "MetaHuman Integration",
            "UI System",
            "Audio System",
            "Physics System",
            "Special Realms (Abismo Umbrio, Sigilos)",
            "Analytics & Telemetry",
            "Monetization & EOS"
        ]
        
        for i, bridge in enumerate(bridges, 1):
            print(f"   {i:2d}. {bridge}")
        
        print("\n📁 PROJECT STRUCTURE:")
        print("   📂 Auracron/")
        print("   ├── 📂 Source/           (All bridge modules)")
        print("   ├── 📂 Content/          (Game assets)")
        print("   ├── 📂 Scripts/          (Automation scripts)")
        print("   ├── 📂 Config/           (Configuration files)")
        print("   └── 📂 Builds/           (Build outputs)")
        
        print("\n🔧 QUICK COMMANDS:")
        if sys.platform == "win32":
            print("   build_auracron.bat       - Build complete game")
            print("   build_harmony_only.bat   - Build Harmony Engine only")
            print("   run_tests.bat           - Run automated tests")
            print("   validate_setup.bat      - Validate setup")
        else:
            print("   ./build_auracron.sh      - Build complete game")
            print("   ./build_harmony_only.sh  - Build Harmony Engine only")
            print("   ./run_tests.sh          - Run automated tests")
            print("   ./validate_setup.sh     - Validate setup")
        
        print("\n" + "="*60)
        print("Ready to create the next-generation gaming experience! 🎮✨")
        print("="*60 + "\n")

def main():
    """Main entry point for Auracron setup"""
    parser = argparse.ArgumentParser(description="Auracron Development Environment Setup")
    parser.add_argument("--project-root", type=str, default=".", help="Project root directory")
    parser.add_argument("--skip-validation", action="store_true", help="Skip system validation")
    parser.add_argument("--harmony-only", action="store_true", help="Setup only Harmony Engine")
    parser.add_argument("--minimal", action="store_true", help="Minimal setup (core only)")
    
    args = parser.parse_args()
    
    # Initialize setup
    setup = AuracronSetup(args.project_root)
    
    if args.harmony_only:
        # Setup only Harmony Engine
        logger.info("🤝 Setting up Harmony Engine only...")
        success = setup._setup_harmony_engine()
        print("✅ Harmony Engine setup completed" if success else "❌ Harmony Engine setup failed")
        return
    
    if args.minimal:
        # Minimal setup
        logger.info("⚡ Running minimal setup...")
        success = (setup._validate_system_requirements() and 
                  setup._setup_python_environment() and
                  setup._install_dependencies())
        print("✅ Minimal setup completed" if success else "❌ Minimal setup failed")
        return
    
    # Full setup
    success = setup.setup_complete_environment()
    
    if not success:
        print("❌ Setup failed! Check the logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
