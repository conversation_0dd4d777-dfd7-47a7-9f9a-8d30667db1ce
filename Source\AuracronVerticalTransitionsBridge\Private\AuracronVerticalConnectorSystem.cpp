/**
 * AuracronVerticalConnectorSystem.cpp
 * 
 * Implementação do sistema de conectores verticais usando UE 5.6 APIs modernas
 */

#include "AuracronVerticalConnectorSystem.h"
#include "AuracronVerticalConnector.h"
#include "Engine/World.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/CapsuleComponent.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "Engine/Engine.h"

DEFINE_LOG_CATEGORY_STATIC(LogAuracronVerticalConnectors, Log, All);

// === USubsystem Interface ===

void UAuracronVerticalConnectorSystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Initializing Auracron Vertical Connector System"));
    
    bSystemInitialized = false;
    ActiveTransitionCount = 0;
    TotalTransitionsProcessed = 0;
    LastUpdateTime = 0.0f;
    
    // Cache realm subsystem reference
    if (UWorld* World = GetWorld())
    {
        // Initialize system state
        bSystemInitialized = false;

        // Initialize connector system
        InitializeConnectorSystem();
    }
    
    // Initialize system
    InitializeConnectorSystem();
}

void UAuracronVerticalConnectorSystem::Deinitialize()
{
    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Deinitializing Auracron Vertical Connector System"));
    
    ShutdownConnectorSystem();
    
    Super::Deinitialize();
}

bool UAuracronVerticalConnectorSystem::ShouldCreateSubsystem(UObject* Outer) const
{
    return true;
}

// === Core System Management ===

void UAuracronVerticalConnectorSystem::InitializeConnectorSystem()
{
    if (bSystemInitialized)
    {
        UE_LOG(LogAuracronVerticalConnectors, Warning, TEXT("Connector system already initialized"));
        return;
    }
    
    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Initializing connector system..."));
    
    // Initialize default configurations
    InitializeDefaultConfigurations();
    
    // Load movement curves
    LoadMovementCurves();
    
    // Clear existing data
    ActiveConnectors.Empty();
    ActiveTransitions.Empty();
    
    // Setup timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            SystemUpdateTimer,
            [this]()
            {
                if (bSystemInitialized)
                {
                    float DeltaTime = GetWorld()->GetDeltaSeconds();
                    ProcessActiveTransitions(DeltaTime);
                    UpdateConnectorStates(DeltaTime);
                    HandleAutoActivations(DeltaTime);
                }
            },
            0.016f, // ~60 FPS
            true
        );
    }
    
    bSystemInitialized = true;
    
    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Connector system initialized successfully"));
}

void UAuracronVerticalConnectorSystem::UpdateConnectorSystem(float DeltaTime)
{
    if (!bSystemInitialized)
    {
        return;
    }
    
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronVerticalConnectorSystem::UpdateConnectorSystem);
    
    LastUpdateTime += DeltaTime;
    
    // Update active transitions
    ProcessActiveTransitions(DeltaTime);
    
    // Update connector states
    UpdateConnectorStates(DeltaTime);
    
    // Handle auto activations
    HandleAutoActivations(DeltaTime);
}

void UAuracronVerticalConnectorSystem::ShutdownConnectorSystem()
{
    if (!bSystemInitialized)
    {
        return;
    }
    
    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Shutting down connector system..."));
    
    // Cancel all active transitions
    for (auto& TransitionPair : ActiveTransitions)
    {
        if (ACharacter* Character = TransitionPair.Key.Get())
        {
            CancelVerticalTransition(Character);
        }
    }
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(SystemUpdateTimer);
        World->GetTimerManager().ClearTimer(ConnectorUpdateTimer);
        World->GetTimerManager().ClearTimer(AutoActivationTimer);
    }
    
    // Clear data
    ActiveConnectors.Empty();
    ActiveTransitions.Empty();
    DefaultConfigurations.Empty();
    MovementCurves.Empty();
    
    bSystemInitialized = false;
    
    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Connector system shutdown complete"));
}

// === Connector Management ===

AAuracronVerticalConnectorActor* UAuracronVerticalConnectorSystem::CreateVerticalConnector(const FVerticalConnectorConfig& Config)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogAuracronVerticalConnectors, Error, TEXT("Cannot create connector: system not initialized"));
        return nullptr;
    }
    
    if (Config.ConnectorType == EVerticalConnectorType::None)
    {
        UE_LOG(LogAuracronVerticalConnectors, Warning, TEXT("Cannot create connector: invalid type"));
        return nullptr;
    }
    
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogAuracronVerticalConnectors, Error, TEXT("Cannot create connector: invalid world"));
        return nullptr;
    }
    
    // Spawn connector actor
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bNoFail = true;
    
    AAuracronVerticalConnectorActor* NewConnector = World->SpawnActor<AAuracronVerticalConnectorActor>(
        AAuracronVerticalConnectorActor::StaticClass(),
        Config.ConnectorLocation,
        FRotator::ZeroRotator,
        SpawnParams
    );
    
    if (!NewConnector)
    {
        UE_LOG(LogAuracronVerticalConnectors, Error, TEXT("Failed to spawn connector actor"));
        return nullptr;
    }
    
    // Configure connector
    NewConnector->SetConnectorConfiguration(Config);
    
    // Add to active connectors
    ActiveConnectors.Add(NewConnector);
    
    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Created vertical connector of type %d at %s"), 
           (int32)Config.ConnectorType, *Config.ConnectorLocation.ToString());
    
    return NewConnector;
}

bool UAuracronVerticalConnectorSystem::RemoveVerticalConnector(AAuracronVerticalConnectorActor* Connector)
{
    if (!bSystemInitialized || !IsValid(Connector))
    {
        return false;
    }
    
    // Cancel any active transitions using this connector
    TArray<ACharacter*> CharactersToCancel;
    for (const auto& TransitionPair : ActiveTransitions)
    {
        if (TransitionPair.Value.Connector == Connector)
        {
            CharactersToCancel.Add(TransitionPair.Key.Get());
        }
    }
    
    for (ACharacter* Character : CharactersToCancel)
    {
        CancelVerticalTransition(Character);
    }
    
    // Remove from active connectors
    ActiveConnectors.Remove(Connector);
    
    // Destroy actor
    Connector->Destroy();
    
    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Removed vertical connector"));
    
    return true;
}

TArray<AAuracronVerticalConnectorActor*> UAuracronVerticalConnectorSystem::GetConnectorsByType(EVerticalConnectorType ConnectorType) const
{
    TArray<AAuracronVerticalConnectorActor*> Result;
    
    for (AAuracronVerticalConnectorActor* Connector : ActiveConnectors)
    {
        if (IsValid(Connector) && Connector->GetConnectorType() == ConnectorType)
        {
            Result.Add(Connector);
        }
    }
    
    return Result;
}

TArray<AAuracronVerticalConnectorActor*> UAuracronVerticalConnectorSystem::GetConnectorsByLayer(ERealmLayer Layer) const
{
    TArray<AAuracronVerticalConnectorActor*> Result;
    
    for (AAuracronVerticalConnectorActor* Connector : ActiveConnectors)
    {
        if (IsValid(Connector))
        {
            const FVerticalConnectorConfig& Config = Connector->GetConnectorConfiguration();
            if (Config.SourceLayer == Layer || Config.DestinationLayer == Layer)
            {
                Result.Add(Connector);
            }
        }
    }
    
    return Result;
}

AAuracronVerticalConnectorActor* UAuracronVerticalConnectorSystem::GetNearestConnector(const FVector& Location, EVerticalConnectorType ConnectorType) const
{
    AAuracronVerticalConnectorActor* NearestConnector = nullptr;
    float NearestDistance = FLT_MAX;
    
    for (AAuracronVerticalConnectorActor* Connector : ActiveConnectors)
    {
        if (!IsValid(Connector))
        {
            continue;
        }
        
        // Check type filter
        if (ConnectorType != EVerticalConnectorType::None && Connector->GetConnectorType() != ConnectorType)
        {
            continue;
        }
        
        // Check if connector is active
        if (Connector->GetConnectorState() != EVerticalConnectorState::Active)
        {
            continue;
        }
        
        float Distance = FVector::Dist(Location, Connector->GetActorLocation());
        if (Distance < NearestDistance)
        {
            NearestDistance = Distance;
            NearestConnector = Connector;
        }
    }
    
    return NearestConnector;
}

// === Transition Management ===

bool UAuracronVerticalConnectorSystem::StartVerticalTransition(ACharacter* Character, AAuracronVerticalConnectorActor* Connector)
{
    if (!bSystemInitialized || !IsValid(Character) || !IsValid(Connector))
    {
        UE_LOG(LogAuracronVerticalConnectors, Warning, TEXT("Cannot start transition: invalid parameters"));
        return false;
    }
    
    // Validate transition request
    if (!ValidateTransitionRequest(Character, Connector))
    {
        return false;
    }
    
    // Check if character is already in transition
    if (ActiveTransitions.Contains(Character))
    {
        UE_LOG(LogAuracronVerticalConnectors, Warning, TEXT("Character already in transition"));
        return false;
    }
    
    // Get connector configuration
    const FVerticalConnectorConfig& Config = Connector->GetConnectorConfiguration();
    
    // Create transition data
    FVerticalTransitionData TransitionData;
    TransitionData.TransitioningCharacter = Character;
    TransitionData.Connector = Connector;
    TransitionData.StartPosition = Character->GetActorLocation();
    TransitionData.EndPosition = GetDestinationPosition(Connector, Config.DestinationLayer);
    TransitionData.TransitionProgress = 0.0f;
    TransitionData.StartTime = GetWorld()->GetTimeSeconds();
    TransitionData.Duration = Config.TransitionDuration;
    TransitionData.MovementCurve = MovementCurves.FindRef(Config.ConnectorType);
    
    // Add to active transitions
    ActiveTransitions.Add(Character, TransitionData);
    ActiveTransitionCount++;
    
    // Apply transition effects
    ApplyTransitionEffects(Character, Connector, true);
    
    // Disable character movement
    if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
    {
        MovementComp->SetMovementMode(MOVE_None);
    }
    
    // Set connector state
    Connector->SetConnectorState(EVerticalConnectorState::InUse);
    
    // Broadcast event
    OnTransitionStarted(Character, Connector);
    
    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Started vertical transition for %s using connector type %d"),
           *Character->GetName(), (int32)Config.ConnectorType);

    return true;
}

bool UAuracronVerticalConnectorSystem::CancelVerticalTransition(ACharacter* Character)
{
    if (!bSystemInitialized || !IsValid(Character))
    {
        return false;
    }

    FVerticalTransitionData* TransitionData = ActiveTransitions.Find(Character);
    if (!TransitionData)
    {
        return false;
    }

    // Restore character movement
    if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
    {
        MovementComp->SetMovementMode(MOVE_Walking);
    }

    // Apply transition effects (ending)
    if (IsValid(TransitionData->Connector))
    {
        ApplyTransitionEffects(Character, TransitionData->Connector.Get(), false);

        // Set connector state back to active
        TransitionData->Connector->SetConnectorState(EVerticalConnectorState::Active);

        // Broadcast event
        OnTransitionCancelled(Character, TransitionData->Connector.Get());
    }

    // Remove from active transitions
    ActiveTransitions.Remove(Character);
    ActiveTransitionCount--;

    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Cancelled vertical transition for %s"), *Character->GetName());

    return true;
}

float UAuracronVerticalConnectorSystem::GetTransitionProgress(ACharacter* Character) const
{
    if (const FVerticalTransitionData* TransitionData = ActiveTransitions.Find(Character))
    {
        return TransitionData->TransitionProgress;
    }

    return 0.0f;
}

bool UAuracronVerticalConnectorSystem::IsCharacterInTransition(ACharacter* Character) const
{
    return ActiveTransitions.Contains(Character);
}

// === Internal Implementation ===

void UAuracronVerticalConnectorSystem::InitializeDefaultConfigurations()
{
    // Portal de Ânima - permanentes nas bases
    FVerticalConnectorConfig PortalAnimaConfig;
    PortalAnimaConfig.ConnectorType = EVerticalConnectorType::PortalAnima;
    PortalAnimaConfig.ActivationRadius = 300.0f;
    PortalAnimaConfig.TransitionSpeed = 2000.0f;
    PortalAnimaConfig.TransitionDuration = 1.0f;
    PortalAnimaConfig.CooldownDuration = 0.0f; // No cooldown for permanent portals
    PortalAnimaConfig.EnergyCost = 0.0f; // Free to use
    PortalAnimaConfig.MaxSimultaneousUsers = 5;
    PortalAnimaConfig.bRequiresLineOfSight = false;
    PortalAnimaConfig.bUsableInCombat = false;
    PortalAnimaConfig.bAutoActivation = false;
    DefaultConfigurations.Add(EVerticalConnectorType::PortalAnima, PortalAnimaConfig);

    // Fenda Fluxo - temporárias, rasgam o solo
    FVerticalConnectorConfig FendaFluxoConfig;
    FendaFluxoConfig.ConnectorType = EVerticalConnectorType::FendaFluxo;
    FendaFluxoConfig.ActivationRadius = 200.0f;
    FendaFluxoConfig.TransitionSpeed = 1500.0f;
    FendaFluxoConfig.TransitionDuration = 1.5f;
    FendaFluxoConfig.CooldownDuration = 10.0f;
    FendaFluxoConfig.EnergyCost = 15.0f;
    FendaFluxoConfig.MaxSimultaneousUsers = 2;
    FendaFluxoConfig.bRequiresLineOfSight = true;
    FendaFluxoConfig.bUsableInCombat = true;
    FendaFluxoConfig.bAutoActivation = false;
    DefaultConfigurations.Add(EVerticalConnectorType::FendaFluxo, FendaFluxoConfig);

    // Cipó Astria - cordas vegetais escaláveis
    FVerticalConnectorConfig CipoAstriaConfig;
    CipoAstriaConfig.ConnectorType = EVerticalConnectorType::CipoAstria;
    CipoAstriaConfig.ActivationRadius = 150.0f;
    CipoAstriaConfig.TransitionSpeed = 800.0f;
    CipoAstriaConfig.TransitionDuration = 3.0f;
    CipoAstriaConfig.CooldownDuration = 5.0f;
    CipoAstriaConfig.EnergyCost = 10.0f;
    CipoAstriaConfig.MaxSimultaneousUsers = 3;
    CipoAstriaConfig.bRequiresLineOfSight = true;
    CipoAstriaConfig.bUsableInCombat = true;
    CipoAstriaConfig.bAutoActivation = false;
    DefaultConfigurations.Add(EVerticalConnectorType::CipoAstria, CipoAstriaConfig);

    // Elevador de Vórtice - colunas de vento
    FVerticalConnectorConfig ElevadorVorticeConfig;
    ElevadorVorticeConfig.ConnectorType = EVerticalConnectorType::ElevadorVortice;
    ElevadorVorticeConfig.ActivationRadius = 400.0f;
    ElevadorVorticeConfig.TransitionSpeed = 1200.0f;
    ElevadorVorticeConfig.TransitionDuration = 2.0f;
    ElevadorVorticeConfig.CooldownDuration = 8.0f;
    ElevadorVorticeConfig.EnergyCost = 20.0f;
    ElevadorVorticeConfig.MaxSimultaneousUsers = 4;
    ElevadorVorticeConfig.bRequiresLineOfSight = false;
    ElevadorVorticeConfig.bUsableInCombat = true;
    ElevadorVorticeConfig.bAutoActivation = false;
    DefaultConfigurations.Add(EVerticalConnectorType::ElevadorVortice, ElevadorVorticeConfig);

    // Respiradouro Geotermal - ativação periódica
    FVerticalConnectorConfig RespiradoroGeotermalConfig;
    RespiradoroGeotermalConfig.ConnectorType = EVerticalConnectorType::RespiradoroGeotermal;
    RespiradoroGeotermalConfig.ActivationRadius = 250.0f;
    RespiradoroGeotermalConfig.TransitionSpeed = 1800.0f;
    RespiradoroGeotermalConfig.TransitionDuration = 1.2f;
    RespiradoroGeotermalConfig.CooldownDuration = 15.0f;
    RespiradoroGeotermalConfig.EnergyCost = 5.0f;
    RespiradoroGeotermalConfig.MaxSimultaneousUsers = 2;
    RespiradoroGeotermalConfig.bRequiresLineOfSight = false;
    RespiradoroGeotermalConfig.bUsableInCombat = true;
    RespiradoroGeotermalConfig.bAutoActivation = true;
    RespiradoroGeotermalConfig.AutoActivationInterval = 45.0f;
    RespiradoroGeotermalConfig.AutoActivationDuration = 20.0f;
    DefaultConfigurations.Add(EVerticalConnectorType::RespiradoroGeotermal, RespiradoroGeotermalConfig);
}

void UAuracronVerticalConnectorSystem::LoadMovementCurves()
{
    // Load movement curves for each connector type
    static const FSoftObjectPath PortalAnimaCurvePath(TEXT("/Game/Curves/VerticalConnectors/Curve_PortalAnima_Movement.Curve_PortalAnima_Movement"));
    static const FSoftObjectPath FendaFluxoCurvePath(TEXT("/Game/Curves/VerticalConnectors/Curve_FendaFluxo_Movement.Curve_FendaFluxo_Movement"));
    static const FSoftObjectPath CipoAstriaCurvePath(TEXT("/Game/Curves/VerticalConnectors/Curve_CipoAstria_Movement.Curve_CipoAstria_Movement"));
    static const FSoftObjectPath ElevadorVorticeCurvePath(TEXT("/Game/Curves/VerticalConnectors/Curve_ElevadorVortice_Movement.Curve_ElevadorVortice_Movement"));
    static const FSoftObjectPath RespiradoroGeotermalCurvePath(TEXT("/Game/Curves/VerticalConnectors/Curve_RespiradoroGeotermal_Movement.Curve_RespiradoroGeotermal_Movement"));

    if (UCurveFloat* PortalAnimaCurve = Cast<UCurveFloat>(PortalAnimaCurvePath.TryLoad()))
    {
        MovementCurves.Add(EVerticalConnectorType::PortalAnima, PortalAnimaCurve);
    }

    if (UCurveFloat* FendaFluxoCurve = Cast<UCurveFloat>(FendaFluxoCurvePath.TryLoad()))
    {
        MovementCurves.Add(EVerticalConnectorType::FendaFluxo, FendaFluxoCurve);
    }

    if (UCurveFloat* CipoAstriaCurve = Cast<UCurveFloat>(CipoAstriaCurvePath.TryLoad()))
    {
        MovementCurves.Add(EVerticalConnectorType::CipoAstria, CipoAstriaCurve);
    }

    if (UCurveFloat* ElevadorVorticeCurve = Cast<UCurveFloat>(ElevadorVorticeCurvePath.TryLoad()))
    {
        MovementCurves.Add(EVerticalConnectorType::ElevadorVortice, ElevadorVorticeCurve);
    }

    if (UCurveFloat* RespiradoroGeotermalCurve = Cast<UCurveFloat>(RespiradoroGeotermalCurvePath.TryLoad()))
    {
        MovementCurves.Add(EVerticalConnectorType::RespiradoroGeotermal, RespiradoroGeotermalCurve);
    }
}

void UAuracronVerticalConnectorSystem::ProcessActiveTransitions(float DeltaTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronVerticalConnectorSystem::ProcessActiveTransitions);

    TArray<ACharacter*> CompletedTransitions;

    for (auto& TransitionPair : ActiveTransitions)
    {
        ACharacter* Character = TransitionPair.Key.Get();
        FVerticalTransitionData& TransitionData = TransitionPair.Value;

        if (!IsValid(Character))
        {
            CompletedTransitions.Add(Character);
            continue;
        }

        ProcessTransition(Character, TransitionData, DeltaTime);

        // Check if transition is complete
        if (TransitionData.TransitionProgress >= 1.0f)
        {
            CompleteTransition(Character, TransitionData);
            CompletedTransitions.Add(Character);
        }
    }

    // Remove completed transitions
    for (ACharacter* Character : CompletedTransitions)
    {
        ActiveTransitions.Remove(Character);
        ActiveTransitionCount--;
        TotalTransitionsProcessed++;
    }
}

void UAuracronVerticalConnectorSystem::UpdateConnectorStates(float DeltaTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronVerticalConnectorSystem::UpdateConnectorStates);

    for (AAuracronVerticalConnectorActor* Connector : ActiveConnectors)
    {
        if (!IsValid(Connector))
        {
            continue;
        }

        Connector->UpdateConnectorState(DeltaTime);
    }
}

void UAuracronVerticalConnectorSystem::HandleAutoActivations(float DeltaTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronVerticalConnectorSystem::HandleAutoActivations);

    for (AAuracronVerticalConnectorActor* Connector : ActiveConnectors)
    {
        if (!IsValid(Connector))
        {
            continue;
        }

        const FVerticalConnectorConfig& Config = Connector->GetConnectorConfiguration();
        if (Config.bAutoActivation && Config.ConnectorType == EVerticalConnectorType::RespiradoroGeotermal)
        {
            Connector->HandleAutoActivation(DeltaTime);
        }
    }
}

void UAuracronVerticalConnectorSystem::ProcessTransition(ACharacter* Character, FVerticalTransitionData& TransitionData, float DeltaTime)
{
    if (!IsValid(Character) || !IsValid(TransitionData.Connector))
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    float ElapsedTime = CurrentTime - TransitionData.StartTime;

    // Calculate progress
    TransitionData.TransitionProgress = FMath::Clamp(ElapsedTime / TransitionData.Duration, 0.0f, 1.0f);

    // Calculate new position
    FVector NewPosition = CalculateTransitionPosition(TransitionData, TransitionData.TransitionProgress);

    // Update character position
    Character->SetActorLocation(NewPosition, false, nullptr, ETeleportType::TeleportPhysics);

    // Update character rotation to face movement direction
    if (TransitionData.TransitionProgress > 0.0f && TransitionData.TransitionProgress < 1.0f)
    {
        FVector MovementDirection = (TransitionData.EndPosition - TransitionData.StartPosition).GetSafeNormal();
        if (!MovementDirection.IsZero())
        {
            FRotator NewRotation = MovementDirection.Rotation();
            Character->SetActorRotation(NewRotation);
        }
    }
}

void UAuracronVerticalConnectorSystem::CompleteTransition(ACharacter* Character, const FVerticalTransitionData& TransitionData)
{
    if (!IsValid(Character) || !IsValid(TransitionData.Connector))
    {
        return;
    }

    // Set final position
    Character->SetActorLocation(TransitionData.EndPosition, false, nullptr, ETeleportType::TeleportPhysics);

    // Restore character movement
    if (UCharacterMovementComponent* MovementComp = Character->GetCharacterMovement())
    {
        MovementComp->SetMovementMode(MOVE_Walking);
    }

    // Apply transition effects (ending)
    ApplyTransitionEffects(Character, TransitionData.Connector.Get(), false);

    // Set connector state
    TransitionData.Connector->SetConnectorState(EVerticalConnectorState::Cooldown);

    // Broadcast event
    OnTransitionCompleted(Character, TransitionData.Connector.Get());

    UE_LOG(LogAuracronVerticalConnectors, Log, TEXT("Completed vertical transition for %s"), *Character->GetName());
}

FVector UAuracronVerticalConnectorSystem::CalculateTransitionPosition(const FVerticalTransitionData& TransitionData, float Progress)
{
    FVector Position = FMath::Lerp(TransitionData.StartPosition, TransitionData.EndPosition, Progress);

    // Apply movement curve if available
    if (IsValid(TransitionData.MovementCurve.Get()))
    {
        float CurveValue = TransitionData.MovementCurve->GetFloatValue(Progress);

        // Apply curve to vertical movement for more natural transitions
        FVector Direction = TransitionData.EndPosition - TransitionData.StartPosition;
        FVector HorizontalDirection = FVector(Direction.X, Direction.Y, 0.0f);
        float VerticalDistance = Direction.Z;

        Position = TransitionData.StartPosition + (HorizontalDirection * Progress) + (FVector::UpVector * VerticalDistance * CurveValue);
    }

    return Position;
}

bool UAuracronVerticalConnectorSystem::ValidateTransitionRequest(ACharacter* Character, AAuracronVerticalConnectorActor* Connector)
{
    if (!IsValid(Character) || !IsValid(Connector))
    {
        return false;
    }

    // Check connector state
    if (Connector->GetConnectorState() != EVerticalConnectorState::Active)
    {
        UE_LOG(LogAuracronVerticalConnectors, Warning, TEXT("Connector is not active"));
        return false;
    }

    // Check distance
    const FVerticalConnectorConfig& Config = Connector->GetConnectorConfiguration();
    float Distance = FVector::Dist(Character->GetActorLocation(), Connector->GetActorLocation());
    if (Distance > Config.ActivationRadius)
    {
        UE_LOG(LogAuracronVerticalConnectors, Warning, TEXT("Character too far from connector"));
        return false;
    }

    // Check line of sight if required
    if (Config.bRequiresLineOfSight)
    {
        FHitResult HitResult;
        FVector Start = Character->GetActorLocation();
        FVector End = Connector->GetActorLocation();

        FCollisionQueryParams QueryParams;
        QueryParams.AddIgnoredActor(Character);
        QueryParams.AddIgnoredActor(Connector);

        if (GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, ECC_Visibility, QueryParams))
        {
            UE_LOG(LogAuracronVerticalConnectors, Warning, TEXT("Line of sight blocked"));
            return false;
        }
    }

    // Check combat state if required
    if (!Config.bUsableInCombat)
    {
        // TODO: Check if character is in combat
        // This would require integration with combat system
    }

    // Check energy cost
    if (Config.EnergyCost > 0.0f)
    {
        // TODO: Check if character has enough energy
        // This would require integration with character energy system
    }

    // Check maximum users
    int32 CurrentUsers = 0;
    for (const auto& TransitionPair : ActiveTransitions)
    {
        if (TransitionPair.Value.Connector == Connector)
        {
            CurrentUsers++;
        }
    }

    if (CurrentUsers >= Config.MaxSimultaneousUsers)
    {
        UE_LOG(LogAuracronVerticalConnectors, Warning, TEXT("Connector at maximum capacity"));
        return false;
    }

    return true;
}

FVector UAuracronVerticalConnectorSystem::GetDestinationPosition(AAuracronVerticalConnectorActor* Connector, ERealmLayer DestinationLayer)
{
    if (!IsValid(Connector))
    {
        return FVector::ZeroVector;
    }

    FVector ConnectorLocation = Connector->GetActorLocation();

    // Calculate destination based on layer
    switch (DestinationLayer)
    {
        case ERealmLayer::Terrestrial:
            return FVector(ConnectorLocation.X, ConnectorLocation.Y, 0.0f); // Ground level

        case ERealmLayer::Celestial:
            return FVector(ConnectorLocation.X, ConnectorLocation.Y, 3000.0f); // Sky level

        case ERealmLayer::Abyssal:
            return FVector(ConnectorLocation.X, ConnectorLocation.Y, -1500.0f); // Underground level

        default:
            return ConnectorLocation;
    }
}

void UAuracronVerticalConnectorSystem::ApplyTransitionEffects(ACharacter* Character, AAuracronVerticalConnectorActor* Connector, bool bStarting)
{
    if (!IsValid(Character) || !IsValid(Connector))
    {
        return;
    }

    // Apply connector-specific effects
    Connector->ApplyTransitionEffects(Character, bStarting);

    // Apply character effects
    if (bStarting)
    {
        // Make character invulnerable during transition
        if (UCapsuleComponent* CapsuleComp = Character->GetCapsuleComponent())
        {
            CapsuleComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);
        }
    }
    else
    {
        // Restore character collision
        if (UCapsuleComponent* CapsuleComp = Character->GetCapsuleComponent())
        {
            CapsuleComp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        }
    }
}

// Removed duplicate implementations - using existing ones above

