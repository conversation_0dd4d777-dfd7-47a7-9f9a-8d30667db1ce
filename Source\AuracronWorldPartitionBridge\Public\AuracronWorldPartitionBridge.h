// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - World Partition Bridge Header
// Production-ready header file for UE5.6 World Partition API bridge

#pragma once

#include "CoreMinimal.h"
#include "AuracronWorldPartitionBridgeAPI.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Modules/ModuleManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/Object.h"
#include "UObject/Class.h"
#include "UObject/WeakObjectPtr.h"
#include "UObject/SoftObjectPtr.h"
#include "Templates/SharedPointer.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "HAL/CriticalSection.h"
#include "Async/Future.h"
#include "Delegates/Delegate.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Math/Transform.h"
#include "Math/Box.h"
#include "Math/IntVector.h"
#include "Engine/EngineTypes.h"
#include "GameFramework/Actor.h"
#include "Components/ActorComponent.h"
#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Texture.h"
#include "Engine/Texture2D.h"
#include "Landscape.h"
#include "FoliageType.h"
#include "InstancedFoliageActor.h"
#include "NavigationSystem.h"
#include "AI/NavigationSystemBase.h"
#include "NavMesh/NavMeshBoundsVolume.h"
#include "Engine/LevelStreaming.h"
#include "Engine/LevelStreamingDynamic.h"
#include "Engine/WorldComposition.h"
#include "LevelInstance/LevelInstanceActor.h"
#include "LevelInstance/LevelInstanceSubsystem.h"

// World Partition Core Includes
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/WorldPartitionRuntimeHash.h"
#include "WorldPartition/WorldPartitionRuntimeSpatialHash.h"
#include "WorldPartition/WorldPartitionEditorHash.h"
#include "WorldPartition/WorldPartitionActorDesc.h"
#include "WorldPartition/WorldPartitionActorDescView.h"
#include "WorldPartition/WorldPartitionHandle.h"
#include "WorldPartition/WorldPartitionHelpers.h"
#include "WorldPartition/WorldPartitionLog.h"
#include "WorldPartition/WorldPartitionMiniMap.h"
#include "WorldPartition/WorldPartitionMiniMapHelper.h"
#include "WorldPartition/WorldPartitionReplay.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
// Note: WorldPartitionHLODRuntimeSubsystem.h may have been removed or moved in UE 5.6
// #include "WorldPartition/WorldPartitionHLODRuntimeSubsystem.h"

// World Partition Data Layers
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/DataLayerInstanceWithAsset.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"

// World Partition HLOD
#include "WorldPartition/HLOD/HLODLayer.h"
#include "WorldPartition/HLOD/HLODActor.h"
#include "WorldPartition/HLOD/HLODSubsystem.h"
#include "WorldPartition/HLOD/HLODBuilder.h"
#include "WorldPartition/HLOD/HLODStats.h"

// World Partition Editor (Editor Only)
#if WITH_EDITOR
// #include "WorldPartition/IWorldPartitionEditorModule.h" // Editor module not available in runtime
#include "WorldPartition/WorldPartitionEditorPerProjectUserSettings.h"
// Forward declaration for WorldPartitionEditorSettings compatibility
class UWorldPartitionEditorSettings;
#include "WorldPartition/SWorldPartitionEditorGrid.h"
#include "WorldPartition/WorldPartitionEditorSpatialHash.h"
#include "WorldPartition/WorldPartitionRuntimeSpatialHash.h"
#include "WorldPartition/WorldPartitionLevelStreamingDynamic.h"
#include "WorldPartition/LoaderAdapter/LoaderAdapterShape.h"
#include "WorldPartition/LoaderAdapter/LoaderAdapterList.h"
#include "WorldPartition/LoaderAdapter/LoaderAdapterActor.h"
#include "WorldPartition/ContentBundle/ContentBundleEditor.h"
#include "WorldPartition/ContentBundle/ContentBundleWorldSubsystem.h"
#endif

// Level Instance Support
#include "LevelInstance/LevelInstanceTypes.h"
#include "LevelInstance/LevelInstanceInterface.h"
#include "LevelInstance/LevelInstanceEditorInstanceActor.h"
#include "LevelInstance/LevelInstanceEditorPivotActor.h"

// Navigation System Integration
#include "NavMesh/RecastNavMesh.h"
#include "NavMesh/RecastNavMeshGenerator.h"
#include "NavigationData.h"
#include "NavigationOctree.h"

// Rendering and Materials
#include "RenderingThread.h"
#include "RenderResource.h"
#include "GlobalShader.h"
#include "ShaderParameters.h"
#include "RHICommandList.h"
#include "SceneView.h"
#include "SceneManagement.h"
#include "PrimitiveViewRelevance.h"
#include "PrimitiveSceneProxy.h"
#include "StaticMeshResources.h"
#include "Engine/MapBuildDataRegistry.h"
#include "Lightmass/LightmassImportanceVolume.h"

// Python Integration
#if WITH_PYTHON
#include "PyWrapperTypeRegistry.h"
#include "PyWrapperObject.h"
#include "PyWrapperStruct.h"
#include "PyWrapperEnum.h"
#include "PyWrapperDelegate.h"
#include "PyWrapperName.h"
#include "PyWrapperText.h"
#include "PyWrapperArray.h"
#include "PyWrapperFixedArray.h"
#include "PyWrapperSet.h"
#include "PyWrapperMap.h"
#include "PyWrapperMath.h"
#include "PyGenUtil.h"
#include "PyConversion.h"
#include "PyReferenceCollector.h"
#endif

// Async and Threading
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/Event.h"
#include "HAL/Runnable.h"
#include "HAL/RunnableThread.h"

// Logging and Profiling
#include "Logging/LogMacros.h"
#include "ProfilingDebugging/ScopedTimers.h"
#include "Stats/Stats.h"
#include "Stats/StatsMisc.h"

// Generated header must be last
#include "AuracronWorldPartitionBridge.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronWorldPartitionBridge, Log, All);

// Forward Declarations
class UWorld;
class UWorldPartition;
class UWorldPartitionSubsystem;
class UWorldPartitionStreamingSource;
class UWorldPartitionRuntimeHash;
class UWorldPartitionEditorHash;
class UWorldPartitionActorDesc;
class UWorldPartitionRuntimeCell;
class UDataLayerSubsystem;
class UDataLayerManager;
class UDataLayerAsset;
class UDataLayerInstance;
class UHLODLayer;
class UHLODActor;
// UHLODSubsystem forward declaration removed due to typedef conflict
class ALevelInstance;
class ULevelInstanceSubsystem;
class ANavigationData;
class UNavigationSystemV1;
class ARecastNavMesh;
class UStaticMesh;
class UMaterialInterface;
class UTexture2D;
class ALandscape;
class AInstancedFoliageActor;
class UFoliageType;

// Enums for World Partition operations
UENUM(BlueprintType)
enum class EWorldPartitionStreamingState : uint8
{
    Unloaded        UMETA(DisplayName = "Unloaded"),
    Loading         UMETA(DisplayName = "Loading"),
    Loaded          UMETA(DisplayName = "Loaded"),
    Activating      UMETA(DisplayName = "Activating"),
    Activated       UMETA(DisplayName = "Activated"),
    Unloading       UMETA(DisplayName = "Unloading")
};

UENUM(BlueprintType)
enum class EWorldPartitionDataLayer : uint8
{
    Runtime         UMETA(DisplayName = "Runtime"),
    Editor          UMETA(DisplayName = "Editor"),
    Both            UMETA(DisplayName = "Both")
};

UENUM(BlueprintType)
enum class EWorldPartitionHLODLevel : uint8
{
    HLOD0           UMETA(DisplayName = "HLOD Level 0"),
    HLOD1           UMETA(DisplayName = "HLOD Level 1"),
    HLOD2           UMETA(DisplayName = "HLOD Level 2"),
    HLOD3           UMETA(DisplayName = "HLOD Level 3"),
    HLOD4           UMETA(DisplayName = "HLOD Level 4"),
    HLOD5           UMETA(DisplayName = "HLOD Level 5")
};

UENUM(BlueprintType)
enum class EWorldPartitionCellState : uint8
{
    Unloaded        UMETA(DisplayName = "Unloaded"),
    Loading         UMETA(DisplayName = "Loading"),
    Loaded          UMETA(DisplayName = "Loaded"),
    Activated       UMETA(DisplayName = "Activated")
};

UENUM(BlueprintType)
enum class EWorldPartitionGridType : uint8
{
    Runtime2D       UMETA(DisplayName = "Runtime 2D Hash"),
    Runtime3D       UMETA(DisplayName = "Runtime 3D Hash"),
    EditorSpatial   UMETA(DisplayName = "Editor Spatial Hash"),
    Custom          UMETA(DisplayName = "Custom Hash")
};

UENUM(BlueprintType)
enum class EWorldPartitionStreamingSourceShape : uint8
{
    Sphere          UMETA(DisplayName = "Sphere"),
    Box             UMETA(DisplayName = "Box"),
    Capsule         UMETA(DisplayName = "Capsule"),
    Custom          UMETA(DisplayName = "Custom Shape")
};

UENUM(BlueprintType)
enum class EWorldPartitionStreamingSourceTargetState : uint8
{
    Loaded          UMETA(DisplayName = "Loaded"),
    Activated       UMETA(DisplayName = "Activated")
};

UENUM(BlueprintType)
enum class EWorldPartitionMinimapCaptureMode : uint8
{
    TopDown         UMETA(DisplayName = "Top Down"),
    Perspective     UMETA(DisplayName = "Perspective"),
    Orthographic    UMETA(DisplayName = "Orthographic")
};

UENUM(BlueprintType)
enum class EWorldPartitionActorFilter : uint8
{
    All             UMETA(DisplayName = "All Actors"),
    SpatiallyLoaded UMETA(DisplayName = "Spatially Loaded"),
    AlwaysLoaded    UMETA(DisplayName = "Always Loaded"),
    DataLayerOnly   UMETA(DisplayName = "Data Layer Only"),
    HLODRelevant    UMETA(DisplayName = "HLOD Relevant")
};

// Structs for World Partition data
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FWorldPartitionCellInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Cell Info")
    FString CellName;

    UPROPERTY(BlueprintReadOnly, Category = "Cell Info")
    FBox CellBounds;

    UPROPERTY(BlueprintReadOnly, Category = "Cell Info")
    EWorldPartitionCellState CellState;

    UPROPERTY(BlueprintReadOnly, Category = "Cell Info")
    int32 ActorCount;

    UPROPERTY(BlueprintReadOnly, Category = "Cell Info")
    float LoadingProgress;

    UPROPERTY(BlueprintReadOnly, Category = "Cell Info")
    TArray<FString> DataLayers;

    UPROPERTY(BlueprintReadOnly, Category = "Cell Info")
    bool bIsHLODCell;

    UPROPERTY(BlueprintReadOnly, Category = "Cell Info")
    int32 HLODLevel;

    FWorldPartitionCellInfo()
        : CellName(TEXT(""))
        , CellBounds(ForceInit)
        , CellState(EWorldPartitionCellState::Unloaded)
        , ActorCount(0)
        , LoadingProgress(0.0f)
        , bIsHLODCell(false)
        , HLODLevel(0)
    {
    }
};

USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FWorldPartitionStreamingSourceInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Streaming Source")
    FString SourceName;

    UPROPERTY(BlueprintReadOnly, Category = "Streaming Source")
    FVector Location;

    UPROPERTY(BlueprintReadOnly, Category = "Streaming Source")
    FVector Forward;

    UPROPERTY(BlueprintReadOnly, Category = "Streaming Source")
    float LoadingRange;

    UPROPERTY(BlueprintReadOnly, Category = "Streaming Source")
    EWorldPartitionStreamingSourceShape Shape;

    UPROPERTY(BlueprintReadOnly, Category = "Streaming Source")
    EWorldPartitionStreamingSourceTargetState TargetState;

    UPROPERTY(BlueprintReadOnly, Category = "Streaming Source")
    int32 Priority;

    UPROPERTY(BlueprintReadOnly, Category = "Streaming Source")
    bool bEnabled;

    UPROPERTY(BlueprintReadOnly, Category = "Streaming Source")
    FLinearColor DebugColor;

    FWorldPartitionStreamingSourceInfo()
        : SourceName(TEXT(""))
        , Location(FVector::ZeroVector)
        , Forward(FVector::ForwardVector)
        , LoadingRange(0.0f)
        , Shape(EWorldPartitionStreamingSourceShape::Sphere)
        , TargetState(EWorldPartitionStreamingSourceTargetState::Loaded)
        , Priority(0)
        , bEnabled(true)
        , DebugColor(FLinearColor::White)
    {
    }
};

USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FWorldPartitionDataLayerInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Data Layer")
    FString LayerName;

    UPROPERTY(BlueprintReadOnly, Category = "Data Layer")
    FString LayerLabel;

    UPROPERTY(BlueprintReadOnly, Category = "Data Layer")
    EWorldPartitionDataLayer LayerType;

    UPROPERTY(BlueprintReadOnly, Category = "Data Layer")
    bool bIsLoaded;

    UPROPERTY(BlueprintReadOnly, Category = "Data Layer")
    bool bIsVisible;

    UPROPERTY(BlueprintReadOnly, Category = "Data Layer")
    bool bIsLocked;

    UPROPERTY(BlueprintReadOnly, Category = "Data Layer")
    FLinearColor DebugColor;

    UPROPERTY(BlueprintReadOnly, Category = "Data Layer")
    TArray<FString> ChildLayers;

    UPROPERTY(BlueprintReadOnly, Category = "Data Layer")
    int32 ActorCount;

    FWorldPartitionDataLayerInfo()
        : LayerName(TEXT(""))
        , LayerLabel(TEXT(""))
        , LayerType(EWorldPartitionDataLayer::Runtime)
        , bIsLoaded(false)
        , bIsVisible(true)
        , bIsLocked(false)
        , DebugColor(FLinearColor::White)
        , ActorCount(0)
    {
    }
};

USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FWorldPartitionHLODInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "HLOD")
    FString HLODName;

    UPROPERTY(BlueprintReadOnly, Category = "HLOD")
    EWorldPartitionHLODLevel HLODLevel;

    UPROPERTY(BlueprintReadOnly, Category = "HLOD")
    FBox HLODBounds;

    UPROPERTY(BlueprintReadOnly, Category = "HLOD")
    int32 SourceActorCount;

    UPROPERTY(BlueprintReadOnly, Category = "HLOD")
    int32 TriangleCount;

    UPROPERTY(BlueprintReadOnly, Category = "HLOD")
    int32 VertexCount;

    UPROPERTY(BlueprintReadOnly, Category = "HLOD")
    float ScreenSize;

    UPROPERTY(BlueprintReadOnly, Category = "HLOD")
    bool bIsGenerated;

    UPROPERTY(BlueprintReadOnly, Category = "HLOD")
    TArray<FString> SourceActors;

    FWorldPartitionHLODInfo()
        : HLODName(TEXT(""))
        , HLODLevel(EWorldPartitionHLODLevel::HLOD0)
        , HLODBounds(ForceInit)
        , SourceActorCount(0)
        , TriangleCount(0)
        , VertexCount(0)
        , ScreenSize(0.0f)
        , bIsGenerated(false)
    {
    }
};

USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FWorldPartitionGridInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    FString GridName;

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    EWorldPartitionGridType GridType;

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    int32 CellSize;

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    float LoadingRange;

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    bool bBlockOnSlowStreaming;

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    int32 Priority;

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    FLinearColor DebugColor;

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    bool bPreviewGrids;

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    int32 TotalCells;

    UPROPERTY(BlueprintReadOnly, Category = "Grid")
    int32 LoadedCells;

    FWorldPartitionGridInfo()
        : GridName(TEXT(""))
        , GridType(EWorldPartitionGridType::Runtime2D)
        , CellSize(25600)
        , LoadingRange(25600.0f)
        , bBlockOnSlowStreaming(false)
        , Priority(0)
        , DebugColor(FLinearColor::White)
        , bPreviewGrids(false)
        , TotalCells(0)
        , LoadedCells(0)
    {
    }
};

/**
 * Enumeration for World Partition streaming policies
 */
UENUM(BlueprintType)
enum class EWorldPartitionStreamingPolicy : uint8
{
    Default         UMETA(DisplayName = "Default"),
    Distance        UMETA(DisplayName = "Distance Based"),
    Priority        UMETA(DisplayName = "Priority Based"),
    Hybrid          UMETA(DisplayName = "Hybrid"),
    Custom          UMETA(DisplayName = "Custom")
};

/**
 * Structure for World Partition memory statistics
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FWorldPartitionMemoryStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    int32 LoadedCellsMemoryMB = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    int32 StreamingMemoryMB = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    int32 HLODMemoryMB = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    int32 TotalMemoryMB = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    int32 AvailableMemoryMB = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Memory")
    float MemoryPressure = 0.0f;

    FWorldPartitionMemoryStats()
    {
        LoadedCellsMemoryMB = 0;
        StreamingMemoryMB = 0;
        HLODMemoryMB = 0;
        TotalMemoryMB = 0;
        AvailableMemoryMB = 0;
        MemoryPressure = 0.0f;
    }
};

/**
 * Structure for detailed World Partition statistics
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FWorldPartitionDetailedStats
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalCells = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 LoadedCells = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 StreamingCells = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 UnloadedCells = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageLoadTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageUnloadTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 ActiveStreamingSources = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float StreamingEfficiency = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    FWorldPartitionMemoryStats MemoryStats;

    FWorldPartitionDetailedStats()
    {
        TotalCells = 0;
        LoadedCells = 0;
        StreamingCells = 0;
        UnloadedCells = 0;
        AverageLoadTime = 0.0f;
        AverageUnloadTime = 0.0f;
        ActiveStreamingSources = 0;
        StreamingEfficiency = 0.0f;
    }
};

// Delegates for async operations
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWorldPartitionCellLoaded, const FString&, CellName, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWorldPartitionCellUnloaded, const FString&, CellName, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWorldPartitionStreamingCompleted, const FVector&, Location, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWorldPartitionDataLayerChanged, const FString&, LayerName, bool, bLoaded);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWorldPartitionHLODGenerated, const FString&, HLODName, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWorldPartitionMinimapGenerated, const FString&, MinimapPath, bool, bSuccess);

/**
 * Main API class for World Partition Bridge
 * Exposes UE5.6 World Partition functionality to Python
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONWORLDPARTITIONBRIDGE_API UAuracronWorldPartitionBridgeAPI : public UObject
{
    GENERATED_BODY()

public:
    UAuracronWorldPartitionBridgeAPI();
    virtual ~UAuracronWorldPartitionBridgeAPI();

    // Initialization and cleanup
    UFUNCTION(BlueprintCallable, Category = "World Partition Bridge", CallInEditor)
    bool InitializeWorldPartitionBridge();

    UFUNCTION(BlueprintCallable, Category = "World Partition Bridge", CallInEditor)
    void ShutdownWorldPartitionBridge();

    UFUNCTION(BlueprintCallable, Category = "World Partition Bridge", CallInEditor)
    bool IsWorldPartitionBridgeInitialized() const;

    // World Partition Management
    UFUNCTION(BlueprintCallable, Category = "World Partition", CallInEditor)
    bool EnableWorldPartition(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "World Partition", CallInEditor)
    bool DisableWorldPartition(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "World Partition", CallInEditor)
    bool IsWorldPartitionEnabled(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition", CallInEditor)
    UWorldPartition* GetWorldPartition(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition", CallInEditor)
    UWorldPartitionSubsystem* GetWorldPartitionSubsystem(UWorld* World) const;

    // Streaming Management
    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming", CallInEditor)
    bool EnableStreaming(UWorld* World, bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming", CallInEditor)
    bool IsStreamingEnabled(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming", CallInEditor)
    bool LoadCellsAtLocation(UWorld* World, const FVector& Location, float Radius);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming", CallInEditor)
    bool UnloadCellsAtLocation(UWorld* World, const FVector& Location, float Radius);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming", CallInEditor)
    bool LoadCellsByName(UWorld* World, const TArray<FString>& CellNames);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming", CallInEditor)
    bool UnloadCellsByName(UWorld* World, const TArray<FString>& CellNames);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming", CallInEditor)
    bool IsStreamingCompleted(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming", CallInEditor)
    float GetStreamingProgress(UWorld* World) const;

    // Cell Information
    UFUNCTION(BlueprintCallable, Category = "World Partition Cells", CallInEditor)
    TArray<FWorldPartitionCellInfo> GetAllCells(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Cells", CallInEditor)
    TArray<FWorldPartitionCellInfo> GetLoadedCells(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Cells", CallInEditor)
    TArray<FWorldPartitionCellInfo> GetCellsInBounds(UWorld* World, const FBox& Bounds) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Cells", CallInEditor)
    FWorldPartitionCellInfo GetCellInfo(UWorld* World, const FString& CellName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Cells", CallInEditor)
    TArray<FString> GetCellsAtLocation(UWorld* World, const FVector& Location) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Cells", CallInEditor)
    int32 GetTotalCellCount(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Cells", CallInEditor)
    int32 GetLoadedCellCount(UWorld* World) const;

    // Streaming Sources
    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming Sources", CallInEditor)
    bool AddStreamingSource(UWorld* World, const FString& SourceName, const FVector& Location, float LoadingRange);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming Sources", CallInEditor)
    bool RemoveStreamingSource(UWorld* World, const FString& SourceName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming Sources", CallInEditor)
    bool UpdateStreamingSource(UWorld* World, const FString& SourceName, const FVector& Location, float LoadingRange);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming Sources", CallInEditor)
    bool EnableStreamingSource(UWorld* World, const FString& SourceName, bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming Sources", CallInEditor)
    TArray<FWorldPartitionStreamingSourceInfo> GetAllStreamingSources(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming Sources", CallInEditor)
    FWorldPartitionStreamingSourceInfo GetStreamingSourceInfo(UWorld* World, const FString& SourceName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming Sources", CallInEditor)
    bool SetStreamingSourceShape(UWorld* World, const FString& SourceName, EWorldPartitionStreamingSourceShape Shape, const FVector& ShapeExtent);

    UFUNCTION(BlueprintCallable, Category = "World Partition Streaming Sources", CallInEditor)
    bool SetStreamingSourcePriority(UWorld* World, const FString& SourceName, int32 Priority);

    // Grid Management
    UFUNCTION(BlueprintCallable, Category = "World Partition Grid", CallInEditor)
    TArray<FWorldPartitionGridInfo> GetAllGrids(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Grid", CallInEditor)
    FWorldPartitionGridInfo GetGridInfo(UWorld* World, const FString& GridName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Grid", CallInEditor)
    bool SetGridCellSize(UWorld* World, const FString& GridName, int32 CellSize);

    UFUNCTION(BlueprintCallable, Category = "World Partition Grid", CallInEditor)
    bool SetGridLoadingRange(UWorld* World, const FString& GridName, float LoadingRange);

    UFUNCTION(BlueprintCallable, Category = "World Partition Grid", CallInEditor)
    bool EnableGridPreview(UWorld* World, const FString& GridName, bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "World Partition Grid", CallInEditor)
    bool SetGridDebugColor(UWorld* World, const FString& GridName, const FLinearColor& DebugColor);

    // Data Layer Management
    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    TArray<FWorldPartitionDataLayerInfo> GetAllDataLayers(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    FWorldPartitionDataLayerInfo GetDataLayerInfo(UWorld* World, const FString& LayerName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    bool LoadDataLayer(UWorld* World, const FString& LayerName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    bool UnloadDataLayer(UWorld* World, const FString& LayerName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    bool SetDataLayerVisibility(UWorld* World, const FString& LayerName, bool bVisible);

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    bool IsDataLayerLoaded(UWorld* World, const FString& LayerName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    bool CreateDataLayer(UWorld* World, const FString& LayerName, EWorldPartitionDataLayer LayerType);

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    bool DeleteDataLayer(UWorld* World, const FString& LayerName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    TArray<FString> GetActorsInDataLayer(UWorld* World, const FString& LayerName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    bool AddActorToDataLayer(UWorld* World, AActor* Actor, const FString& LayerName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Data Layers", CallInEditor)
    bool RemoveActorFromDataLayer(UWorld* World, AActor* Actor, const FString& LayerName);

    // HLOD Management
    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    TArray<FWorldPartitionHLODInfo> GetAllHLODs(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    FWorldPartitionHLODInfo GetHLODInfo(UWorld* World, const FString& HLODName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    bool GenerateHLODs(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    bool GenerateHLODsForLevel(UWorld* World, EWorldPartitionHLODLevel HLODLevel);

    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    bool DeleteHLODs(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    bool DeleteHLODsForLevel(UWorld* World, EWorldPartitionHLODLevel HLODLevel);

    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    bool SetHLODScreenSize(UWorld* World, EWorldPartitionHLODLevel HLODLevel, float ScreenSize);

    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    float GetHLODScreenSize(UWorld* World, EWorldPartitionHLODLevel HLODLevel) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    bool IsHLODGenerationInProgress(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition HLOD", CallInEditor)
    float GetHLODGenerationProgress(UWorld* World) const;

    // Minimap Generation
    UFUNCTION(BlueprintCallable, Category = "World Partition Minimap", CallInEditor)
    bool GenerateMinimap(UWorld* World, const FString& OutputPath, int32 ImageSize = 2048);

    UFUNCTION(BlueprintCallable, Category = "World Partition Minimap", CallInEditor)
    bool GenerateMinimapForBounds(UWorld* World, const FBox& Bounds, const FString& OutputPath, int32 ImageSize = 2048);

    UFUNCTION(BlueprintCallable, Category = "World Partition Minimap", CallInEditor)
    bool SetMinimapCaptureSettings(UWorld* World, EWorldPartitionMinimapCaptureMode CaptureMode, float CaptureHeight, const FRotator& CaptureRotation);

    UFUNCTION(BlueprintCallable, Category = "World Partition Minimap", CallInEditor)
    bool IsMinimapGenerationInProgress(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Minimap", CallInEditor)
    float GetMinimapGenerationProgress(UWorld* World) const;

    // Actor Management
    UFUNCTION(BlueprintCallable, Category = "World Partition Actors", CallInEditor)
    TArray<AActor*> GetActorsInCell(UWorld* World, const FString& CellName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Actors", CallInEditor)
    TArray<AActor*> GetActorsInBounds(UWorld* World, const FBox& Bounds, EWorldPartitionActorFilter Filter = EWorldPartitionActorFilter::All) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Actors", CallInEditor)
    FString GetActorCell(UWorld* World, AActor* Actor) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Actors", CallInEditor)
    bool SetActorSpatiallyLoaded(UWorld* World, AActor* Actor, bool bSpatiallyLoaded);

    UFUNCTION(BlueprintCallable, Category = "World Partition Actors", CallInEditor)
    bool IsActorSpatiallyLoaded(UWorld* World, AActor* Actor) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Actors", CallInEditor)
    bool MoveActorToCell(UWorld* World, AActor* Actor, const FString& TargetCellName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Actors", CallInEditor)
    int32 GetActorCountInCell(UWorld* World, const FString& CellName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Actors", CallInEditor)
    TArray<FString> GetActorDataLayers(UWorld* World, AActor* Actor) const;

    // Level Instance Support
    UFUNCTION(BlueprintCallable, Category = "World Partition Level Instances", CallInEditor)
    bool CreateLevelInstance(UWorld* World, const FString& LevelPath, const FTransform& Transform, const FString& InstanceName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Level Instances", CallInEditor)
    bool DeleteLevelInstance(UWorld* World, const FString& InstanceName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Level Instances", CallInEditor)
    TArray<FString> GetAllLevelInstances(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Level Instances", CallInEditor)
    bool LoadLevelInstance(UWorld* World, const FString& InstanceName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Level Instances", CallInEditor)
    bool UnloadLevelInstance(UWorld* World, const FString& InstanceName);

    UFUNCTION(BlueprintCallable, Category = "World Partition Level Instances", CallInEditor)
    bool IsLevelInstanceLoaded(UWorld* World, const FString& InstanceName) const;

    // Debug and Visualization
    UFUNCTION(BlueprintCallable, Category = "World Partition Debug", CallInEditor)
    bool EnableDebugVisualization(UWorld* World, bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "World Partition Debug", CallInEditor)
    bool IsDebugVisualizationEnabled(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Debug", CallInEditor)
    bool SetDebugVisualizationMode(UWorld* World, const FString& VisualizationMode);

    UFUNCTION(BlueprintCallable, Category = "World Partition Debug", CallInEditor)
    TArray<FString> GetAvailableDebugVisualizationModes(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Debug", CallInEditor)
    bool DumpWorldPartitionInfo(UWorld* World, const FString& OutputPath) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Debug", CallInEditor)
    FString GetWorldPartitionStats(UWorld* World) const;

    // Utility Functions
    UFUNCTION(BlueprintCallable, Category = "World Partition Utilities", CallInEditor)
    bool ValidateWorldPartition(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Utilities", CallInEditor)
    bool RepairWorldPartition(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "World Partition Utilities", CallInEditor)
    bool ConvertLevelToWorldPartition(const FString& LevelPath, const FString& OutputPath);

    UFUNCTION(BlueprintCallable, Category = "World Partition Utilities", CallInEditor)
    bool OptimizeWorldPartition(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "World Partition Utilities", CallInEditor)
    TArray<FString> GetWorldPartitionErrors(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Utilities", CallInEditor)
    TArray<FString> GetWorldPartitionWarnings(UWorld* World) const;

    // === UE 5.6 ADVANCED FEATURES ===

    /**
     * Configure World Partition streaming policy
     * @param World Target world
     * @param PolicyType Streaming policy type
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    bool SetStreamingPolicy(UWorld* World, EWorldPartitionStreamingPolicy PolicyType);

    /**
     * Get current streaming policy
     * @param World Target world
     * @return Current streaming policy
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    EWorldPartitionStreamingPolicy GetStreamingPolicy(UWorld* World) const;

    /**
     * Configure World Partition runtime hash settings
     * @param World Target world
     * @param CellSize Size of each cell in world units
     * @param LoadingRange Loading range for cells
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    bool ConfigureRuntimeHash(UWorld* World, float CellSize = 25600.0f, float LoadingRange = 51200.0f);

    /**
     * Enable/Disable World Partition debug visualization
     * @param World Target world
     * @param bEnable True to enable debug visualization
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    bool SetDebugVisualizationEnabled(UWorld* World, bool bEnable);

    /**
     * Force World Partition garbage collection
     * @param World Target world
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    bool ForceGarbageCollection(UWorld* World);

    /**
     * Get World Partition memory statistics
     * @param World Target world
     * @return Memory statistics
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    FWorldPartitionMemoryStats GetMemoryStatistics(UWorld* World) const;

    /**
     * Configure World Partition LOD settings
     * @param World Target world
     * @param LODDistance Distance for LOD transitions
     * @param MaxLODLevel Maximum LOD level
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    bool ConfigureLODSettings(UWorld* World, float LODDistance = 10000.0f, int32 MaxLODLevel = 4);

    /**
     * Enable/Disable World Partition async loading
     * @param World Target world
     * @param bEnable True to enable async loading
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    bool SetAsyncLoadingEnabled(UWorld* World, bool bEnable);

    /**
     * Configure World Partition streaming sources
     * @param World Target world
     * @param MaxSources Maximum number of streaming sources
     * @param UpdateFrequency Update frequency in Hz
     * @return True if successful
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    bool ConfigureStreamingSources(UWorld* World, int32 MaxSources = 16, float UpdateFrequency = 30.0f);

    /**
     * Get detailed World Partition statistics
     * @param World Target world
     * @return Detailed statistics
     */
    UFUNCTION(BlueprintCallable, Category = "World Partition Advanced", CallInEditor)
    FWorldPartitionDetailedStats GetDetailedStatistics(UWorld* World) const;

    // Delegates for async operations
    UPROPERTY(BlueprintAssignable, Category = "World Partition Events")
    FOnWorldPartitionCellLoaded OnCellLoaded;

    UPROPERTY(BlueprintAssignable, Category = "World Partition Events")
    FOnWorldPartitionCellUnloaded OnCellUnloaded;

    UPROPERTY(BlueprintAssignable, Category = "World Partition Events")
    FOnWorldPartitionStreamingCompleted OnStreamingCompleted;

    UPROPERTY(BlueprintAssignable, Category = "World Partition Events")
    FOnWorldPartitionDataLayerChanged OnDataLayerChanged;

    UPROPERTY(BlueprintAssignable, Category = "World Partition Events")
    FOnWorldPartitionHLODGenerated OnHLODGenerated;

    UPROPERTY(BlueprintAssignable, Category = "World Partition Events")
    FOnWorldPartitionMinimapGenerated OnMinimapGenerated;

private:
    // Internal state management
    bool bIsInitialized;
    mutable FCriticalSection CriticalSection;

    // Cached references
    TWeakObjectPtr<UWorld> CachedWorld;
    TWeakObjectPtr<UWorldPartition> CachedWorldPartition;
    TWeakObjectPtr<UWorldPartitionSubsystem> CachedWorldPartitionSubsystem;

    // Streaming sources management
    TMap<FString, TWeakObjectPtr<UWorldPartitionStreamingSource>> StreamingSources;

    // Data layer management
    TMap<FString, TWeakObjectPtr<UDataLayerInstance>> DataLayers;

    // HLOD management
    TMap<FString, TWeakObjectPtr<UHLODActor>> HLODActors;

    // Level instance management
    TMap<FString, TWeakObjectPtr<ALevelInstance>> LevelInstances;

    // Async operation tracking
    TMap<FString, TSharedPtr<FEvent>> AsyncOperations;
    TMap<FString, float> OperationProgress;

    // Debug and profiling
    TMap<FString, double> PerformanceMetrics;
    TArray<FString> ErrorMessages;
    TArray<FString> WarningMessages;

    // Internal helper functions
    UWorldPartition* GetValidWorldPartition(UWorld* World) const;
    UWorldPartitionSubsystem* GetValidWorldPartitionSubsystem(UWorld* World) const;
    UDataLayerSubsystem* GetDataLayerSubsystem(UWorld* World) const;
    UWorldPartitionHLODRuntimeSubsystem* GetHLODSubsystem(UWorld* World) const;
    ULevelInstanceSubsystem* GetLevelInstanceSubsystem(UWorld* World) const;

    void LogError(const FString& ErrorMessage) const;
    void LogWarning(const FString& WarningMessage) const;
    void LogInfo(const FString& InfoMessage) const;

    bool ValidateWorld(UWorld* World) const;
    bool ValidateWorldPartitionEnabled(UWorld* World) const;

    void UpdateCachedReferences(UWorld* World);
    void ClearCachedReferences();

    // Async operation helpers
    void StartAsyncOperation(const FString& OperationName);
    void CompleteAsyncOperation(const FString& OperationName, bool bSuccess);
    void UpdateAsyncOperationProgress(const FString& OperationName, float Progress);

    // Event handlers
    void OnWorldPartitionInitialized(UWorldPartition* WorldPartition);
    void OnWorldPartitionSubsystemInitialized(UWorldPartitionSubsystem* Subsystem, UWorld* World);
    void OnWorldPartitionSubsystemDeinitialized(UWorldPartitionSubsystem* Subsystem, UWorld* World);
    void OnCellLoadingStateChanged(const UWorldPartitionRuntimeCell* Cell, EWorldPartitionRuntimeCellState OldState, EWorldPartitionRuntimeCellState NewState);
    void OnDataLayerRuntimeStateChanged(const UDataLayerInstance* DataLayer, EDataLayerRuntimeState OldState, EDataLayerRuntimeState NewState);

    // Performance monitoring
    void StartPerformanceTimer(const FString& TimerName);
    void EndPerformanceTimer(const FString& TimerName);
    double GetPerformanceMetric(const FString& MetricName) const;

    // Memory management
    void CleanupUnusedReferences();
    void OptimizeMemoryUsage();

    // Thread safety helpers
    void LockForRead() const;
    void LockForWrite() const;
    void Unlock() const;

    // Validation helpers
    bool IsValidCellName(const FString& CellName) const;
    bool IsValidDataLayerName(const FString& LayerName) const;
    bool IsValidStreamingSourceName(const FString& SourceName) const;
    bool IsValidHLODName(const FString& HLODName) const;
    bool IsValidLevelInstanceName(const FString& InstanceName) const;

    // Conversion helpers
    EWorldPartitionCellState ConvertRuntimeCellState(EWorldPartitionRuntimeCellState RuntimeState) const;
    EWorldPartitionRuntimeCellState ConvertCellState(EWorldPartitionCellState CellState) const;
    EDataLayerRuntimeState ConvertDataLayerState(bool bLoaded, bool bVisible) const;

    // Utility functions
    FBox CalculateBoundsForActors(const TArray<AActor*>& Actors) const;
    TArray<AActor*> FilterActorsByType(const TArray<AActor*>& Actors, EWorldPartitionActorFilter Filter) const;
    FString GenerateUniqueOperationId() const;

    // Configuration management
    void LoadConfiguration();
    void SaveConfiguration();
    void ResetToDefaults();
};

/**
 * Module class for World Partition Bridge
 */
class AURACRONWORLDPARTITIONBRIDGE_API FAuracronWorldPartitionBridgeModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    /** Get the singleton instance of the API */
    static UAuracronWorldPartitionBridgeAPI* GetAPI();

    /** Check if the module is loaded and ready */
    static bool IsModuleLoaded();

private:
    /** Singleton instance of the API */
    static UAuracronWorldPartitionBridgeAPI* APIInstance;

    /** Module initialization state */
    bool bIsInitialized;

    /** Initialize the module */
    void Initialize();

    /** Cleanup the module */
    void Cleanup();

    /** Register Python bindings */
    void RegisterPythonBindings();

    /** Unregister Python bindings */
    void UnregisterPythonBindings();

    /** Setup logging */
    void SetupLogging();

    /** Setup performance monitoring */
    void SetupPerformanceMonitoring();
};
