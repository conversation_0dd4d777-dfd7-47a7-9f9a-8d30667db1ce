#!/usr/bin/env python3
"""
Auracron Bridge Manager
Manages individual bridge modules, their dependencies, and integration
"""

import os
import json
import logging
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
import yaml

logger = logging.getLogger('BridgeManager')

@dataclass
class BridgeStatus:
    """Status information for a bridge"""
    name: str
    is_built: bool
    last_build_time: Optional[str]
    build_errors: List[str]
    dependencies_satisfied: bool
    integration_status: str

class BridgeManager:
    """Manages Auracron bridge modules"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.source_dir = self.project_root / "Source"
        self.config_file = self.project_root / "Scripts" / "bridge_config.yaml"
        
        # Load bridge configuration
        self.bridge_config = self._load_bridge_config()
        self.bridge_status = {}
        
        # Initialize status tracking
        self._initialize_status_tracking()
    
    def _load_bridge_config(self) -> Dict:
        """Load bridge configuration from YAML file"""
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                return yaml.safe_load(f)
        else:
            # Create default configuration
            default_config = self._create_default_config()
            self._save_bridge_config(default_config)
            return default_config
    
    def _create_default_config(self) -> Dict:
        """Create default bridge configuration"""
        return {
            "bridges": {
                "core": {
                    "Auracron": {
                        "category": "core",
                        "priority": 1,
                        "dependencies": [],
                        "build_flags": ["WITH_EDITOR=1"],
                        "special_handling": False
                    }
                },
                "rendering": {
                    "AuracronLumenBridge": {
                        "category": "rendering",
                        "priority": 2,
                        "dependencies": ["Auracron"],
                        "build_flags": ["WITH_LUMEN=1"],
                        "special_handling": False
                    },
                    "AuracronNaniteBridge": {
                        "category": "rendering", 
                        "priority": 2,
                        "dependencies": ["Auracron"],
                        "build_flags": ["WITH_NANITE=1"],
                        "special_handling": False
                    },
                    "AuracronVFXBridge": {
                        "category": "rendering",
                        "priority": 3,
                        "dependencies": ["Auracron", "AuracronLumenBridge"],
                        "build_flags": ["WITH_NIAGARA=1"],
                        "special_handling": False
                    }
                },
                "world": {
                    "AuracronWorldPartitionBridge": {
                        "category": "world",
                        "priority": 2,
                        "dependencies": ["Auracron"],
                        "build_flags": ["WITH_WORLD_PARTITION=1"],
                        "special_handling": False
                    },
                    "AuracronPCGBridge": {
                        "category": "world",
                        "priority": 3,
                        "dependencies": ["Auracron", "AuracronWorldPartitionBridge"],
                        "build_flags": ["WITH_PCG=1"],
                        "special_handling": False
                    }
                },
                "gameplay": {
                    "AuracronCombatBridge": {
                        "category": "gameplay",
                        "priority": 2,
                        "dependencies": ["Auracron"],
                        "build_flags": ["WITH_GAMEPLAY_ABILITIES=1"],
                        "special_handling": False
                    },
                    "AuracronChampionsBridge": {
                        "category": "gameplay",
                        "priority": 3,
                        "dependencies": ["Auracron", "AuracronCombatBridge"],
                        "build_flags": [],
                        "special_handling": False
                    }
                },
                "social": {
                    "AuracronHarmonyEngineBridge": {
                        "category": "social",
                        "priority": 2,
                        "dependencies": ["Auracron"],
                        "build_flags": ["WITH_HARMONY_ENGINE=1"],
                        "special_handling": True,
                        "ml_models": True,
                        "requires_training_data": True
                    }
                },
                "technical": {
                    "AuracronNetworkingBridge": {
                        "category": "technical",
                        "priority": 1,
                        "dependencies": ["Auracron"],
                        "build_flags": ["WITH_ADVANCED_NETWORKING=1"],
                        "special_handling": False
                    },
                    "AuracronAntiCheatBridge": {
                        "category": "technical",
                        "priority": 2,
                        "dependencies": ["Auracron", "AuracronNetworkingBridge"],
                        "build_flags": ["WITH_ANTI_CHEAT=1"],
                        "special_handling": True
                    }
                }
            },
            "build_settings": {
                "parallel_builds": True,
                "max_parallel_jobs": 4,
                "timeout_minutes": 30,
                "retry_attempts": 2
            },
            "integration_tests": {
                "enabled": True,
                "test_categories": ["unit", "integration", "performance"],
                "required_coverage": 80.0
            }
        }
    
    def _save_bridge_config(self, config: Dict):
        """Save bridge configuration to file"""
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
    
    def validate_bridge_dependencies(self) -> Dict[str, List[str]]:
        """Validate all bridge dependencies"""
        logger.info("Validating bridge dependencies...")
        
        issues = {}
        
        for category, bridges in self.bridge_config["bridges"].items():
            for bridge_name, bridge_info in bridges.items():
                bridge_issues = []
                
                # Check if bridge directory exists
                bridge_path = self.source_dir / bridge_name
                if not bridge_path.exists():
                    bridge_issues.append(f"Bridge directory not found: {bridge_path}")
                
                # Check dependencies
                for dep in bridge_info.get("dependencies", []):
                    dep_path = self.source_dir / dep
                    if not dep_path.exists():
                        bridge_issues.append(f"Dependency not found: {dep}")
                
                # Check build files
                build_file = bridge_path / f"{bridge_name}.Build.cs"
                if bridge_path.exists() and not build_file.exists():
                    bridge_issues.append(f"Build file not found: {build_file}")
                
                if bridge_issues:
                    issues[bridge_name] = bridge_issues
        
        if issues:
            logger.warning(f"Found dependency issues in {len(issues)} bridges")
            for bridge, bridge_issues in issues.items():
                for issue in bridge_issues:
                    logger.warning(f"  {bridge}: {issue}")
        else:
            logger.info("All bridge dependencies validated successfully")
        
        return issues
    
    def generate_bridge_integration_code(self) -> bool:
        """Generate integration code for all bridges"""
        logger.info("Generating bridge integration code...")
        
        # Create main integration header
        integration_header = self._generate_integration_header()
        integration_path = self.source_dir / "Auracron" / "Public" / "AuracronBridgeIntegration.h"
        
        with open(integration_path, 'w') as f:
            f.write(integration_header)
        
        # Create integration implementation
        integration_impl = self._generate_integration_implementation()
        integration_impl_path = self.source_dir / "Auracron" / "Private" / "AuracronBridgeIntegration.cpp"
        
        with open(integration_impl_path, 'w') as f:
            f.write(integration_impl)
        
        logger.info("Bridge integration code generated successfully")
        return True
    
    def _generate_integration_header(self) -> str:
        """Generate integration header file"""
        header = '''#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "AuracronBridgeIntegration.generated.h"

// Forward declarations for all bridges
'''
        
        # Add forward declarations for each bridge
        for category, bridges in self.bridge_config["bridges"].items():
            header += f"\n// {category.title()} Bridges\n"
            for bridge_name in bridges.keys():
                if bridge_name != "Auracron":  # Skip main module
                    header += f"class U{bridge_name}Subsystem;\n"
        
        header += '''
/**
 * Central integration point for all Auracron bridges
 * Manages initialization, coordination, and communication between bridges
 */
UCLASS()
class AURACRON_API UAuracronBridgeIntegration : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // Bridge management
    UFUNCTION(BlueprintCallable, Category = "Bridge Integration")
    void InitializeAllBridges();

    UFUNCTION(BlueprintCallable, Category = "Bridge Integration")
    void ShutdownAllBridges();

    UFUNCTION(BlueprintCallable, Category = "Bridge Integration")
    bool IsBridgeActive(const FString& BridgeName) const;

    // Cross-bridge communication
    UFUNCTION(BlueprintCallable, Category = "Bridge Integration")
    void BroadcastBridgeEvent(const FString& EventName, const FString& EventData);

protected:
'''
        
        # Add subsystem references
        for category, bridges in self.bridge_config["bridges"].items():
            for bridge_name in bridges.keys():
                if bridge_name != "Auracron":
                    header += f"    UPROPERTY()\n    TObjectPtr<U{bridge_name}Subsystem> {bridge_name}Subsystem;\n\n"
        
        header += '''
private:
    void InitializeBridgeCategory(const FString& Category);
    void ValidateBridgeIntegrity();
    void SetupBridgeCommunication();
};
'''
        
        return header
    
    def _generate_integration_implementation(self) -> str:
        """Generate integration implementation file"""
        impl = '''#include "AuracronBridgeIntegration.h"
#include "Engine/Engine.h"

// Include all bridge headers
'''
        
        # Add includes for each bridge
        for category, bridges in self.bridge_config["bridges"].items():
            for bridge_name in bridges.keys():
                if bridge_name != "Auracron":
                    impl += f'#include "{bridge_name}Subsystem.h"\n'
        
        impl += '''
void UAuracronBridgeIntegration::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    UE_LOG(LogTemp, Log, TEXT("Initializing Auracron Bridge Integration"));
    
    InitializeAllBridges();
    SetupBridgeCommunication();
    ValidateBridgeIntegrity();
}

void UAuracronBridgeIntegration::InitializeAllBridges()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing all Auracron bridges"));
    
'''
        
        # Add initialization for each bridge
        for category, bridges in self.bridge_config["bridges"].items():
            impl += f"    // Initialize {category} bridges\n"
            for bridge_name in bridges.keys():
                if bridge_name != "Auracron":
                    impl += f"    {bridge_name}Subsystem = GetGameInstance()->GetSubsystem<U{bridge_name}Subsystem>();\n"
        
        impl += '''
    UE_LOG(LogTemp, Log, TEXT("All bridges initialized"));
}
'''
        
        return impl

def main():
    """Main entry point for bridge manager"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Auracron Bridge Manager")
    parser.add_argument("--validate", action="store_true", help="Validate bridge dependencies")
    parser.add_argument("--generate", action="store_true", help="Generate integration code")
    parser.add_argument("--status", action="store_true", help="Show bridge status")
    parser.add_argument("--project-root", type=str, default=".", help="Project root directory")
    
    args = parser.parse_args()
    
    manager = BridgeManager(args.project_root)
    
    if args.validate:
        issues = manager.validate_bridge_dependencies()
        if not issues:
            print("✅ All bridge dependencies are valid")
        else:
            print("❌ Found dependency issues:")
            for bridge, bridge_issues in issues.items():
                for issue in bridge_issues:
                    print(f"  {bridge}: {issue}")
    
    if args.generate:
        success = manager.generate_bridge_integration_code()
        if success:
            print("✅ Bridge integration code generated")
        else:
            print("❌ Failed to generate integration code")
    
    if args.status:
        manager.show_bridge_status()

if __name__ == "__main__":
    main()
