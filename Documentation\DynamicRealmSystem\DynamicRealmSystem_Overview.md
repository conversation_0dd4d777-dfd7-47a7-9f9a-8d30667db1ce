# Auracron Dynamic Realm System

## Overview
The Dynamic Realm System implements the revolutionary three-layer world of Auracron:

### Layers
1. **<PERSON><PERSON><PERSON> (Terrestrial Layer)** - Ground level with crystal formations
2. **Firmamento Zephyr (Celestial Layer)** - Sky level with floating islands  
3. **<PERSON><PERSON><PERSON> Umbrio (Abyssal Layer)** - Underground level with bioluminescent caves

### Key Features
- Dynamic vertical transitions between layers
- Procedural content generation for each realm
- Real-time environmental evolution through 4 phases
- Cross-layer combat mechanics
- Performance optimization for multi-layer rendering

### Evolution Phases
1. **Despertar (0-15min)** - Only Terrestrial layer active
2. **Convergência (15-25min)** - Celestial layer activates
3. **Intensificação (25-35min)** - Abyssal layer activates
4. **Resolução (35+min)** - All layers at maximum intensity

### Prismal Flow System
Central energy river with serpentine pattern containing 23 strategic islands:
- 5 Nexus Islands (Control)
- 8 Santuário Islands (Safe zones)
- 6 Arsenal Islands (Upgrades)
- 4 Caos Islands (High risk/reward)

### Dynamic Rails
Three types of movement rails:
- **Solar Trilhos** - Golden, speed boost during day
- **Axis Trilhos** - Silver, instant vertical movement
- **Lunar Trilhos** - Blue ethereal, stealth bonus at night

## Implementation Status
✅ Core system architecture
✅ Layer management
✅ Transition system
✅ Prismal Flow
✅ Dynamic Rails
✅ PCG Integration
✅ Performance optimization
