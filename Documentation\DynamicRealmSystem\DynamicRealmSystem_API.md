# Dynamic Realm System API Reference

## Core Classes

### UAuracronDynamicRealmSubsystem
Main subsystem managing the entire realm system.

#### Key Methods
- `InitializeRealmLayers()` - Initialize all three layers
- `ActivateLayer(EAuracronRealmLayer Layer)` - Activate specific layer
- `RequestLayerTransition(AActor* Actor, EAuracronRealmLayer TargetLayer)` - Request layer transition
- `GetLayerData(EAuracronRealmLayer Layer)` - Get layer information

### AAuracronRealmManager
Manages individual realm layers.

#### Key Methods
- `InitializeLayer()` - Initialize the managed layer
- `GenerateLayerContent()` - Generate procedural content
- `SetLayerVisibility(bool bVisible)` - Control layer visibility

### UAuracronLayerComponent
Component for actors to interact with the realm system.

#### Key Methods
- `SetCurrentLayer(EAuracronRealmLayer NewLayer)` - Set actor's layer
- `RequestLayerTransition(EAuracronRealmLayer TargetLayer)` - Request transition
- `GetCurrentLayerModifiers()` - Get layer-specific bonuses/penalties

### AAuracronPrismalFlow
Central energy river system.

#### Key Methods
- `InitializeFlow()` - Initialize the flow system
- `SpawnPrismalIslands()` - Spawn strategic islands
- `UpdateFlowDynamics(float DeltaTime)` - Update flow behavior

### AAuracronDynamicRail
Dynamic movement rail system.

#### Key Methods
- `ActivateRail()` - Activate the rail
- `StartPlayerMovement(APawn* Player)` - Start player movement on rail
- `UpdateRailVisuals(float DeltaTime)` - Update visual effects

## Events

### Realm Events
- `OnRealmLayerChanged` - Fired when actor changes layers
- `OnRealmEvolutionPhaseChanged` - Fired when evolution phase changes
- `OnRealmTransitionStarted` - Fired when transition begins
- `OnRealmTransitionCompleted` - Fired when transition completes

### Island Events  
- `OnPrismalIslandActivated` - Fired when island activates
- `OnPlayerEnteredIsland` - Fired when player enters island
- `OnPlayerLeftIsland` - Fired when player leaves island

## Console Commands
- `Auracron.Realm.ShowInfo` - Show realm information
- `Auracron.Realm.ToggleLayer [LayerIndex]` - Toggle layer visibility
- `Auracron.Realm.TriggerTransition [Source] [Target]` - Trigger transition
- `Auracron.Realm.SpawnContent [LayerIndex]` - Spawn layer content
- `Auracron.Realm.AnalyzePerformance` - Analyze performance metrics
