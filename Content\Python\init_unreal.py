"""
AURACRON Auto-Initialization Script
Este script é executado automaticamente quando o Unreal Editor inicia
Responsável por configurar o ambiente Python para o projeto AURACRON
"""

import unreal
import sys
import os

def setup_auracron_python_environment():
    """Configurar ambiente Python para AURACRON"""
    try:
        unreal.log("=== AURACRON AUTO-INITIALIZATION ===")
        unreal.log("Configurando ambiente Python para AURACRON...")
        
        # Adicionar diretório Scripts/Python ao path
        project_dir = unreal.Paths.project_dir()
        scripts_python_dir = os.path.join(project_dir, "Scripts", "Python")
        
        if os.path.exists(scripts_python_dir):
            if scripts_python_dir not in sys.path:
                sys.path.insert(0, scripts_python_dir)
                unreal.log(f"✓ Adicionado ao Python path: {scripts_python_dir}")
            else:
                unreal.log(f"✓ Python path já configurado: {scripts_python_dir}")
        else:
            unreal.log_warning(f"Diretório <PERSON>ripts/Python não encontrado: {scripts_python_dir}")
        
        # Tentar importar e inicializar o sistema core
        try:
            from Core.AuracronCore import get_auracron_core, test_auracron_integration
            
            unreal.log("✓ Módulo AuracronCore importado com sucesso")
            
            # Executar teste de integração
            if test_auracron_integration():
                unreal.log("✓ Sistema AURACRON inicializado automaticamente")
            else:
                unreal.log_warning("⚠ Falha na inicialização automática do AURACRON")
                
        except ImportError as e:
            unreal.log_warning(f"⚠ Não foi possível importar AuracronCore: {str(e)}")
        except Exception as e:
            unreal.log_error(f"✗ Erro durante inicialização automática: {str(e)}")
        
        unreal.log("=== AURACRON AUTO-INITIALIZATION COMPLETE ===")
        
    except Exception as e:
        unreal.log_error(f"Erro crítico na inicialização automática: {str(e)}")

# Executar configuração automática
setup_auracron_python_environment()
