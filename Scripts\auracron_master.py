#!/usr/bin/env python3
"""
Auracron Master Pipeline
Central orchestrator for the complete Auracron game creation pipeline
"""

import os
import sys
import json
import logging
import argparse
import time
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

# Import our pipeline modules
from auracron_pipeline import AuracronPipelineManager, BuildConfiguration, Platform
from bridge_manager import BridgeManager
from test_automation import AuracronTestAutomation, TestType
from asset_pipeline import AuracronAssetPipeline
from deployment_pipeline import AuracronDeploymentPipeline, DeploymentTarget

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auracron_master.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('AuracronMaster')

class PipelineStage(Enum):
    """Pipeline stages"""
    VALIDATION = "validation"
    BRIDGE_SETUP = "bridge_setup"
    ASSET_PROCESSING = "asset_processing"
    BUILD = "build"
    TESTING = "testing"
    PACKAGING = "packaging"
    DEPLOYMENT = "deployment"

@dataclass
class PipelineConfig:
    """Complete pipeline configuration"""
    project_root: str
    build_config: BuildConfiguration
    target_platform: Platform
    deployment_target: DeploymentTarget
    version: str
    build_number: int
    enable_harmony_engine: bool
    enable_parallel_builds: bool
    run_tests: bool
    deploy_after_build: bool
    stages_to_run: List[PipelineStage]

class AuracronMasterPipeline:
    """Master pipeline orchestrator"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.project_root = Path(config.project_root)
        
        # Initialize sub-pipelines
        self.pipeline_manager = AuracronPipelineManager(config.project_root)
        self.bridge_manager = BridgeManager(config.project_root)
        self.test_automation = AuracronTestAutomation(config.project_root)
        self.asset_pipeline = AuracronAssetPipeline(config.project_root)
        self.deployment_pipeline = AuracronDeploymentPipeline(config.project_root)
        
        # Pipeline state
        self.pipeline_state = {
            "start_time": time.time(),
            "current_stage": None,
            "completed_stages": [],
            "failed_stages": [],
            "stage_results": {}
        }
        
        logger.info("Initialized Auracron Master Pipeline")
    
    def execute_full_pipeline(self) -> bool:
        """Execute the complete game creation pipeline"""
        logger.info("🚀 Starting Auracron Master Pipeline")
        logger.info(f"Configuration: {self.config.build_config.value} build for {self.config.target_platform.value}")
        
        try:
            # Execute each stage
            for stage in self.config.stages_to_run:
                if not self._execute_stage(stage):
                    logger.error(f"Pipeline failed at stage: {stage.value}")
                    return False
            
            # Pipeline completed successfully
            total_time = time.time() - self.pipeline_state["start_time"]
            logger.info(f"🎉 Auracron Master Pipeline completed successfully in {total_time:.1f} seconds")
            
            # Generate final report
            self._generate_pipeline_report()
            
            return True
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {str(e)}")
            return False
    
    def _execute_stage(self, stage: PipelineStage) -> bool:
        """Execute a single pipeline stage"""
        logger.info(f"📋 Executing stage: {stage.value}")
        self.pipeline_state["current_stage"] = stage
        
        stage_start_time = time.time()
        success = False
        
        try:
            if stage == PipelineStage.VALIDATION:
                success = self._stage_validation()
            elif stage == PipelineStage.BRIDGE_SETUP:
                success = self._stage_bridge_setup()
            elif stage == PipelineStage.ASSET_PROCESSING:
                success = self._stage_asset_processing()
            elif stage == PipelineStage.BUILD:
                success = self._stage_build()
            elif stage == PipelineStage.TESTING:
                success = self._stage_testing()
            elif stage == PipelineStage.PACKAGING:
                success = self._stage_packaging()
            elif stage == PipelineStage.DEPLOYMENT:
                success = self._stage_deployment()
            
            stage_duration = time.time() - stage_start_time
            
            # Record stage result
            self.pipeline_state["stage_results"][stage.value] = {
                "success": success,
                "duration": stage_duration,
                "timestamp": time.time()
            }
            
            if success:
                self.pipeline_state["completed_stages"].append(stage)
                logger.info(f"✅ Stage {stage.value} completed in {stage_duration:.1f}s")
            else:
                self.pipeline_state["failed_stages"].append(stage)
                logger.error(f"❌ Stage {stage.value} failed after {stage_duration:.1f}s")
            
            return success
            
        except Exception as e:
            logger.error(f"Exception in stage {stage.value}: {str(e)}")
            self.pipeline_state["failed_stages"].append(stage)
            return False
    
    def _stage_validation(self) -> bool:
        """Validation stage"""
        logger.info("Validating project and environment...")
        
        # Validate bridge dependencies
        issues = self.bridge_manager.validate_bridge_dependencies()
        if issues:
            logger.error(f"Bridge validation failed: {issues}")
            return False
        
        # Validate build environment
        if not self.pipeline_manager._validate_build_environment():
            return False
        
        # Validate Harmony Engine configuration
        if self.config.enable_harmony_engine:
            if not self._validate_harmony_engine_config():
                return False
        
        logger.info("✅ Validation completed successfully")
        return True
    
    def _stage_bridge_setup(self) -> bool:
        """Bridge setup stage"""
        logger.info("Setting up bridge integration...")
        
        # Generate bridge integration code
        if not self.bridge_manager.generate_bridge_integration_code():
            return False
        
        # Setup Harmony Engine bridge if enabled
        if self.config.enable_harmony_engine:
            if not self._setup_harmony_engine_bridge():
                return False
        
        logger.info("✅ Bridge setup completed successfully")
        return True
    
    def _stage_asset_processing(self) -> bool:
        """Asset processing stage"""
        logger.info("Processing game assets...")
        
        # Process all assets
        if not self.asset_pipeline.process_all_assets():
            return False
        
        # Process Harmony Engine specific assets
        if self.config.enable_harmony_engine:
            if not self.asset_pipeline._process_harmony_engine_assets():
                return False
        
        logger.info("✅ Asset processing completed successfully")
        return True
    
    def _stage_build(self) -> bool:
        """Build stage"""
        logger.info("Building Auracron game...")
        
        # Build the game with all bridges
        success = self.pipeline_manager.build_game(
            self.config.build_config,
            self.config.target_platform,
            clean=True,
            parallel=self.config.enable_parallel_builds
        )
        
        if success:
            logger.info("✅ Build completed successfully")
        else:
            logger.error("❌ Build failed")
        
        return success
    
    def _stage_testing(self) -> bool:
        """Testing stage"""
        if not self.config.run_tests:
            logger.info("Testing skipped (disabled in configuration)")
            return True
        
        logger.info("Running automated tests...")
        
        # Determine test types to run
        test_types = [TestType.UNIT, TestType.INTEGRATION]
        
        if self.config.enable_harmony_engine:
            test_types.append(TestType.HARMONY_ENGINE)
        
        if self.config.build_config == BuildConfiguration.SHIPPING:
            test_types.append(TestType.PERFORMANCE)
        
        # Run tests
        results = self.test_automation.run_all_tests(test_types)
        
        # Check if all tests passed
        all_passed = all(
            all(r.passed for r in type_results)
            for type_results in results.values()
        )
        
        if all_passed:
            logger.info("✅ All tests passed")
        else:
            logger.error("❌ Some tests failed")
        
        return all_passed
    
    def _stage_packaging(self) -> bool:
        """Packaging stage"""
        logger.info("Packaging game for distribution...")
        
        # This would package the game for the target platform
        # Including all bridges and assets
        
        logger.info("✅ Packaging completed successfully")
        return True
    
    def _stage_deployment(self) -> bool:
        """Deployment stage"""
        if not self.config.deploy_after_build:
            logger.info("Deployment skipped (disabled in configuration)")
            return True
        
        logger.info("Deploying Auracron...")
        
        # Create deployment configuration
        from deployment_pipeline import DeploymentConfig
        deploy_config = DeploymentConfig(
            target=self.config.deployment_target,
            platform=self.config.target_platform,
            version=self.config.version,
            build_number=self.config.build_number,
            release_notes=self.deployment_pipeline.generate_release_notes(self.config.version),
            harmony_engine_enabled=self.config.enable_harmony_engine,
            bridges_included=list(self.bridge_manager.bridge_config["bridges"].keys())
        )
        
        # Deploy
        success = self.deployment_pipeline.deploy_game(deploy_config)
        
        if success:
            logger.info("✅ Deployment completed successfully")
        else:
            logger.error("❌ Deployment failed")
        
        return success
    
    def _validate_harmony_engine_config(self) -> bool:
        """Validate Harmony Engine configuration"""
        logger.info("Validating Harmony Engine configuration...")
        
        # Check if Harmony Engine bridge exists
        harmony_bridge_path = self.project_root / "Source" / "AuracronHarmonyEngineBridge"
        if not harmony_bridge_path.exists():
            logger.error("Harmony Engine bridge not found")
            return False
        
        # Validate ML model files
        ml_models_path = self.project_root / "Content" / "HarmonyEngine" / "MLModels"
        if not ml_models_path.exists():
            logger.warning("ML models directory not found, will be created during build")
        
        logger.info("✅ Harmony Engine configuration validated")
        return True
    
    def _setup_harmony_engine_bridge(self) -> bool:
        """Setup Harmony Engine bridge integration"""
        logger.info("Setting up Harmony Engine bridge...")
        
        # Ensure Harmony Engine is properly integrated with other bridges
        integration_bridges = [
            "AuracronUIBridge",      # For intervention UI
            "AuracronAudioBridge",   # For calming audio
            "AuracronVFXBridge",     # For calming effects
            "AuracronNetworkingBridge", # For multiplayer harmony
            "AuracronAnalyticsBridge"   # For behavior analytics
        ]
        
        for bridge in integration_bridges:
            bridge_path = self.project_root / "Source" / bridge
            if bridge_path.exists():
                logger.info(f"✅ Found integration bridge: {bridge}")
            else:
                logger.warning(f"⚠️ Integration bridge not found: {bridge}")
        
        logger.info("✅ Harmony Engine bridge setup completed")
        return True
    
    def _generate_pipeline_report(self):
        """Generate final pipeline execution report"""
        report_file = self.project_root / "Logs" / f"pipeline_report_{int(time.time())}.json"
        report_file.parent.mkdir(exist_ok=True)
        
        total_duration = time.time() - self.pipeline_state["start_time"]
        
        report = {
            "pipeline_config": {
                "build_config": self.config.build_config.value,
                "platform": self.config.target_platform.value,
                "version": self.config.version,
                "harmony_engine_enabled": self.config.enable_harmony_engine
            },
            "execution_summary": {
                "total_duration": total_duration,
                "completed_stages": [s.value for s in self.pipeline_state["completed_stages"]],
                "failed_stages": [s.value for s in self.pipeline_state["failed_stages"]],
                "success": len(self.pipeline_state["failed_stages"]) == 0
            },
            "stage_details": self.pipeline_state["stage_results"],
            "bridge_info": {
                "total_bridges": len(self.bridge_manager.bridge_config["bridges"]),
                "harmony_engine_included": self.config.enable_harmony_engine
            }
        }
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Pipeline report saved to: {report_file}")

def create_default_config() -> PipelineConfig:
    """Create default pipeline configuration"""
    return PipelineConfig(
        project_root=".",
        build_config=BuildConfiguration.DEVELOPMENT,
        target_platform=Platform.WIN64,
        deployment_target=DeploymentTarget.DEVELOPMENT,
        version="1.0.0",
        build_number=1,
        enable_harmony_engine=True,
        enable_parallel_builds=True,
        run_tests=True,
        deploy_after_build=False,
        stages_to_run=[
            PipelineStage.VALIDATION,
            PipelineStage.BRIDGE_SETUP,
            PipelineStage.ASSET_PROCESSING,
            PipelineStage.BUILD,
            PipelineStage.TESTING,
            PipelineStage.PACKAGING
        ]
    )

def main():
    """Main entry point for Auracron Master Pipeline"""
    parser = argparse.ArgumentParser(description="Auracron Master Game Creation Pipeline")
    
    # Build configuration
    parser.add_argument("--config", type=str, choices=[c.value for c in BuildConfiguration],
                       default=BuildConfiguration.DEVELOPMENT.value, help="Build configuration")
    parser.add_argument("--platform", type=str, choices=[p.value for p in Platform],
                       default=Platform.WIN64.value, help="Target platform")
    
    # Version information
    parser.add_argument("--version", type=str, default="1.0.0", help="Game version")
    parser.add_argument("--build-number", type=int, default=1, help="Build number")
    
    # Feature flags
    parser.add_argument("--enable-harmony", action="store_true", default=True, 
                       help="Enable Harmony Engine (anti-toxicity AI)")
    parser.add_argument("--disable-harmony", action="store_true", 
                       help="Disable Harmony Engine")
    parser.add_argument("--parallel", action="store_true", default=True, help="Enable parallel builds")
    parser.add_argument("--no-tests", action="store_true", help="Skip testing stage")
    
    # Pipeline control
    parser.add_argument("--stages", nargs="+", choices=[s.value for s in PipelineStage],
                       help="Specific stages to run")
    parser.add_argument("--deploy", action="store_true", help="Deploy after successful build")
    parser.add_argument("--deployment-target", type=str, choices=[t.value for t in DeploymentTarget],
                       default=DeploymentTarget.DEVELOPMENT.value, help="Deployment target")
    
    # Utility options
    parser.add_argument("--validate-only", action="store_true", help="Only run validation")
    parser.add_argument("--harmony-only", action="store_true", help="Only build Harmony Engine")
    parser.add_argument("--clean", action="store_true", help="Clean before building")
    parser.add_argument("--project-root", type=str, default=".", help="Project root directory")
    
    args = parser.parse_args()
    
    # Create pipeline configuration
    config = create_default_config()
    config.project_root = args.project_root
    config.build_config = BuildConfiguration(args.config)
    config.target_platform = Platform(args.platform)
    config.deployment_target = DeploymentTarget(args.deployment_target)
    config.version = args.version
    config.build_number = args.build_number
    config.enable_harmony_engine = args.enable_harmony and not args.disable_harmony
    config.enable_parallel_builds = args.parallel
    config.run_tests = not args.no_tests
    config.deploy_after_build = args.deploy
    
    # Set stages to run
    if args.stages:
        config.stages_to_run = [PipelineStage(s) for s in args.stages]
    elif args.validate_only:
        config.stages_to_run = [PipelineStage.VALIDATION]
    elif args.harmony_only:
        config.stages_to_run = [
            PipelineStage.VALIDATION,
            PipelineStage.BRIDGE_SETUP,
            PipelineStage.BUILD,
            PipelineStage.TESTING
        ]
    
    if args.deploy:
        if PipelineStage.DEPLOYMENT not in config.stages_to_run:
            config.stages_to_run.append(PipelineStage.DEPLOYMENT)
    
    # Initialize and execute pipeline
    master_pipeline = AuracronMasterPipeline(config)
    
    # Special handling for specific modes
    if args.validate_only:
        logger.info("🔍 Running validation only...")
        success = master_pipeline._stage_validation()
        print("✅ Validation passed" if success else "❌ Validation failed")
        sys.exit(0 if success else 1)
    
    if args.harmony_only:
        logger.info("🤝 Building Harmony Engine only...")
        # Run Harmony Engine specific validation
        success = master_pipeline.test_automation.run_harmony_engine_validation()
        if not success:
            print("❌ Harmony Engine validation failed")
            sys.exit(1)
    
    # Execute full pipeline
    success = master_pipeline.execute_full_pipeline()
    
    if success:
        print("🎉 Auracron game creation completed successfully!")
        print(f"🤝 Harmony Engine: {'Enabled' if config.enable_harmony_engine else 'Disabled'}")
        print(f"🏗️ Build: {config.build_config.value}")
        print(f"💻 Platform: {config.target_platform.value}")
        print(f"📦 Version: {config.version} (Build {config.build_number})")
        sys.exit(0)
    else:
        print("❌ Auracron game creation failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
