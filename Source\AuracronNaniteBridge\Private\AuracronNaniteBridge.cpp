// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Geometria Virtualizada Nanite Bridge Implementation

#include "AuracronNaniteBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "ProceduralMeshComponent/Public/ProceduralMeshComponent.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Kismet/GameplayStatics.h"


UAuracronNaniteBridge::UAuracronNaniteBridge(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f; // 1 FPS para geometria (não precisa ser frequente)
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão de Nanite
    NaniteConfiguration.bUseNanite = true;
    NaniteConfiguration.NaniteQuality = EAuracronNaniteQuality::High;
    NaniteConfiguration.MaxRenderDistance = 20000.0f;
    NaniteConfiguration.bUseAggressiveCulling = true;
    NaniteConfiguration.LODBias = 0.0f;
    NaniteConfiguration.bUseMeshStreaming = true;
    NaniteConfiguration.StreamingPoolSizeMB = 512;
    NaniteConfiguration.bUseMeshCompression = true;
    NaniteConfiguration.CompressionLevel = 7;
    NaniteConfiguration.bUseFallbackForMobile = true;
    NaniteConfiguration.bUseAutoInstancing = true;
    NaniteConfiguration.InstancingThreshold = 10;
    NaniteConfiguration.bUseOcclusionCulling = true;
    NaniteConfiguration.bUseFrustumCulling = true;
    NaniteConfiguration.bUseDistanceCulling = true;
    NaniteConfiguration.CullingDistance = 25000.0f;
    
    // Configurações padrão de geometria de realm
    RealmGeometryConfiguration.RealmGeometryDensity = {1.0f, 1.2f, 0.8f};
    RealmGeometryConfiguration.bUseProceduralGeometry = true;
    RealmGeometryConfiguration.ProceduralSeed = 12345;
    RealmGeometryConfiguration.ProceduralComplexity = 1.0f;
    RealmGeometryConfiguration.bUseMeshVariations = true;
    RealmGeometryConfiguration.MeshVariations = 3;
}

void UAuracronNaniteBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Geometria Nanite"));

    // Inicializar sistema
    bSystemInitialized = InitializeNaniteSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers
        GetWorld()->GetTimerManager().SetTimer(
            OptimizationTimer,
            [this]()
            {
                if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
                {
                    if (APawn* Pawn = PC->GetPawn())
                    {
                        OptimizeGeometryByDistance(Pawn->GetActorLocation());
                    }
                }
            },
            2.0f, // A cada 2 segundos
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            CleanupTimer,
            [this]()
            {
                CleanupUnusedGeometry();
            },
            30.0f, // A cada 30 segundos
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Geometria Nanite inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Geometria Nanite"));
    }
}

void UAuracronNaniteBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar componentes ativos
    for (UStaticMeshComponent* Component : ActiveMeshComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveMeshComponents.Empty();
    
    // Limpar componentes instanciados
    for (UInstancedStaticMeshComponent* Component : ActiveInstancedComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveInstancedComponents.Empty();
    
    // Limpar instâncias
    ActiveGeometryInstances.Empty();

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimer);
        GetWorld()->GetTimerManager().ClearTimer(CleanupTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronNaniteBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronNaniteBridge, NaniteConfiguration);
}

void UAuracronNaniteBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar geometria ativa
    ProcessActiveGeometry(DeltaTime);
}

// === Core Nanite Management ===

UStaticMeshComponent* UAuracronNaniteBridge::SpawnNaniteGeometry(const FAuracronGeometryInstance& GeometryConfig)
{
    if (!bSystemInitialized || !GeometryConfig.Mesh.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou mesh inválido"));
        return nullptr;
    }

    if (!ValidateGeometryConfiguration(GeometryConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de geometria inválida"));
        return nullptr;
    }

    // Carregar mesh
    UStaticMesh* StaticMesh = GeometryConfig.Mesh.LoadSynchronous();
    if (!StaticMesh)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar mesh"));
        return nullptr;
    }

    // Criar componente
    UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(GetOwner());
    if (!MeshComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar StaticMeshComponent"));
        return nullptr;
    }

    // Configurar mesh
    MeshComponent->SetStaticMesh(StaticMesh);
    MeshComponent->SetWorldTransform(GeometryConfig.InstanceTransform);

    // Aplicar configurações de Nanite
    ApplyNaniteSettings(MeshComponent, NaniteConfiguration);

    // Aplicar material override se especificado
    if (GeometryConfig.MaterialOverride.IsValid())
    {
        UMaterialInterface* Material = GeometryConfig.MaterialOverride.LoadSynchronous();
        if (Material)
        {
            MeshComponent->SetMaterial(0, Material);
        }
    }

    // Configurar culling
    if (GeometryConfig.bUseDistanceCulling)
    {
        MeshComponent->SetCullDistance(GeometryConfig.CullingDistance);
    }

    // Anexar ao owner
    MeshComponent->AttachToComponent(
        GetOwner()->GetRootComponent(),
        FAttachmentTransformRules::KeepWorldTransform
    );

    // Registrar componente
    GetOwner()->AddInstanceComponent(MeshComponent);
    MeshComponent->RegisterComponent();

    // Adicionar às listas de controle
    ActiveMeshComponents.Add(MeshComponent);
    ActiveGeometryInstances.Add(GeometryConfig);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Geometria Nanite spawnada: %s"), *GeometryConfig.InstanceID);

    // Broadcast evento
    OnGeometrySpawned.Broadcast(MeshComponent, GeometryConfig);

    return MeshComponent;
}

bool UAuracronNaniteBridge::ConvertMeshToNanite(UStaticMesh* SourceMesh)
{
    if (!bSystemInitialized || !SourceMesh)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou mesh inválido"));
        return false;
    }

#if WITH_EDITORONLY_DATA
    // Verificar se já é Nanite
    if (SourceMesh->NaniteSettings.bEnabled)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Mesh já está habilitado para Nanite"));
        return true;
    }

    // Habilitar Nanite com configurações robustas para UE 5.6
    SourceMesh->NaniteSettings.bEnabled = true;
    SourceMesh->NaniteSettings.PositionPrecision = -1; // Auto precision
    SourceMesh->NaniteSettings.NormalPrecision = -1; // Auto precision
    SourceMesh->NaniteSettings.TangentPrecision = -1; // Auto precision
    SourceMesh->NaniteSettings.bLerpUVs = true;
    SourceMesh->NaniteSettings.bPreserveArea = false;
    SourceMesh->NaniteSettings.bExplicitTangents = false;
    SourceMesh->NaniteSettings.KeepPercentTriangles = 1.0f; // Manter 100% dos triângulos inicialmente
    SourceMesh->NaniteSettings.TrimRelativeError = 0.01f; // 1% de erro relativo

    // Marcar para rebuild usando a API correta do UE 5.6
    SourceMesh->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Mesh convertido para Nanite: %s"), *SourceMesh->GetName());

    return true;
#else
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configurações do Nanite não disponíveis em build de runtime"));
    return false;
#endif
}

bool UAuracronNaniteBridge::OptimizeMeshForNanite(UStaticMesh* TargetMesh, int32 TargetTriangles)
{
    if (!bSystemInitialized || !TargetMesh)
    {
        return false;
    }

#if WITH_EDITORONLY_DATA
    // Configurar settings de otimização para Nanite usando acesso direto
    TargetMesh->NaniteSettings.bEnabled = true;
    TargetMesh->NaniteSettings.PositionPrecision = -1; // Auto precision
    TargetMesh->NaniteSettings.NormalPrecision = -1; // Auto precision
    TargetMesh->NaniteSettings.TangentPrecision = -1; // Auto precision
    TargetMesh->NaniteSettings.bLerpUVs = true;
    TargetMesh->NaniteSettings.bPreserveArea = false;
    TargetMesh->NaniteSettings.bExplicitTangents = false;

    // Configurar redução de triângulos se especificado
    if (TargetTriangles > 0)
    {
        // Estimar percentual baseado no número de triângulos atual vs target
        float TargetPercentage = FMath::Clamp(float(TargetTriangles) / FMath::Max(100000.0f, float(TargetTriangles)), 0.1f, 1.0f);
        TargetMesh->NaniteSettings.KeepPercentTriangles = TargetPercentage;
        TargetMesh->NaniteSettings.TrimRelativeError = 0.01f; // 1% de erro relativo
    }
    else
    {
        // Configurações padrão para otimização geral
        TargetMesh->NaniteSettings.KeepPercentTriangles = 0.8f; // Manter 80% dos triângulos
        TargetMesh->NaniteSettings.TrimRelativeError = 0.005f; // 0.5% de erro relativo
    }

    // Marcar para rebuild usando a API correta do UE 5.6
    TargetMesh->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Mesh otimizado para Nanite: %s (Target: %d triângulos)"), *TargetMesh->GetName(), TargetTriangles);

    return true;
#else
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configurações do Nanite não disponíveis em build de runtime"));
    return false;
#endif
}

bool UAuracronNaniteBridge::GenerateNaniteLODs(UStaticMesh* TargetMesh, int32 NumLODs)
{
    if (!bSystemInitialized || !TargetMesh || NumLODs <= 0)
    {
        return false;
    }

#if WITH_EDITORONLY_DATA
    // Habilitar Nanite se não estiver habilitado
    if (!TargetMesh->NaniteSettings.bEnabled)
    {
        TargetMesh->NaniteSettings.bEnabled = true;
        TargetMesh->NaniteSettings.PositionPrecision = -1; // Auto precision
        TargetMesh->NaniteSettings.NormalPrecision = -1; // Auto precision
        TargetMesh->NaniteSettings.TangentPrecision = -1; // Auto precision
        TargetMesh->NaniteSettings.bLerpUVs = true;
        TargetMesh->NaniteSettings.bPreserveArea = false;
        TargetMesh->NaniteSettings.bExplicitTangents = false;
    }

    // Configurar LODs através das configurações do Nanite
    // No UE 5.6, o Nanite gerencia LODs automaticamente, mas podemos configurar a redução
    float ReductionFactor = FMath::Pow(0.5f, NumLODs - 1); // Redução baseada no número de LODs desejados
    TargetMesh->NaniteSettings.KeepPercentTriangles = FMath::Clamp(ReductionFactor, 0.1f, 1.0f);
    TargetMesh->NaniteSettings.TrimRelativeError = FMath::Clamp(0.005f * NumLODs, 0.001f, 0.05f); // Erro relativo baseado no número de LODs

    // Configurações adicionais para LODs robustos
    TargetMesh->NaniteSettings.FallbackPercentTriangles = FMath::Clamp(ReductionFactor * 0.5f, 0.05f, 0.5f);
    TargetMesh->NaniteSettings.FallbackRelativeError = TargetMesh->NaniteSettings.TrimRelativeError * 2.0f;

    // Marcar para rebuild usando a API correta do UE 5.6
    TargetMesh->MarkPackageDirty();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: LODs Nanite configurados para mesh: %s (%d níveis de LOD)"), *TargetMesh->GetName(), NumLODs);

    return true;
#else
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configurações do Nanite não disponíveis em build de runtime"));
    return false;
#endif
}

// === Instanced Geometry ===

UInstancedStaticMeshComponent* UAuracronNaniteBridge::CreateGeometryInstances(UStaticMesh* Mesh, const TArray<FTransform>& Transforms)
{
    if (!bSystemInitialized || !Mesh || Transforms.Num() == 0)
    {
        return nullptr;
    }

    // Criar componente instanciado
    UInstancedStaticMeshComponent* InstancedComponent = NewObject<UInstancedStaticMeshComponent>(GetOwner());
    if (!InstancedComponent)
    {
        return nullptr;
    }

    // Configurar mesh
    InstancedComponent->SetStaticMesh(Mesh);

    // Aplicar configurações de Nanite
    ApplyNaniteSettings(InstancedComponent, NaniteConfiguration);

    // Adicionar instâncias
    for (const FTransform& Transform : Transforms)
    {
        InstancedComponent->AddInstance(Transform);
    }

    // Anexar ao owner
    InstancedComponent->AttachToComponent(
        GetOwner()->GetRootComponent(),
        FAttachmentTransformRules::KeepWorldTransform
    );

    // Registrar componente
    GetOwner()->AddInstanceComponent(InstancedComponent);
    InstancedComponent->RegisterComponent();

    // Adicionar à lista de controle
    ActiveInstancedComponents.Add(InstancedComponent);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Instâncias de geometria criadas: %d instâncias"), Transforms.Num());

    return InstancedComponent;
}

bool UAuracronNaniteBridge::AddGeometryInstance(UInstancedStaticMeshComponent* InstancedComponent, const FTransform& Transform)
{
    if (!bSystemInitialized || !InstancedComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou componente inválido"));
        return false;
    }

    FScopeLock Lock(&NaniteMutex);

    // Adicionar instância
    int32 InstanceIndex = InstancedComponent->AddInstance(Transform);

    if (InstanceIndex >= 0)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Instância adicionada - Índice: %d"), InstanceIndex);
        return true;
    }

    UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao adicionar instância"));
    return false;
}

bool UAuracronNaniteBridge::RemoveGeometryInstance(UInstancedStaticMeshComponent* InstancedComponent, int32 InstanceIndex)
{
    if (!bSystemInitialized || !InstancedComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou componente inválido"));
        return false;
    }

    if (InstanceIndex < 0 || InstanceIndex >= InstancedComponent->GetInstanceCount())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Índice de instância inválido: %d"), InstanceIndex);
        return false;
    }

    FScopeLock Lock(&NaniteMutex);

    // Remover instância
    bool bSuccess = InstancedComponent->RemoveInstance(InstanceIndex);

    if (bSuccess)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Instância removida - Índice: %d"), InstanceIndex);
        return true;
    }

    UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao remover instância"));
    return false;
}

bool UAuracronNaniteBridge::UpdateGeometryInstance(UInstancedStaticMeshComponent* InstancedComponent, int32 InstanceIndex, const FTransform& NewTransform)
{
    if (!bSystemInitialized || !InstancedComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou componente inválido"));
        return false;
    }

    if (InstanceIndex < 0 || InstanceIndex >= InstancedComponent->GetInstanceCount())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Índice de instância inválido: %d"), InstanceIndex);
        return false;
    }

    FScopeLock Lock(&NaniteMutex);

    // Atualizar transform da instância
    bool bSuccess = InstancedComponent->UpdateInstanceTransform(InstanceIndex, NewTransform, true, true);

    if (bSuccess)
    {
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Instância atualizada - Índice: %d"), InstanceIndex);
        return true;
    }

    UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao atualizar instância"));
    return false;
}

// === Realm Geometry ===

bool UAuracronNaniteBridge::GenerateRealmGeometry(int32 RealmIndex, const FVector& CenterLocation, float Radius)
{
    if (!bSystemInitialized || RealmIndex < 0 || Radius <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros inválidos para geração de realm"));
        return false;
    }

    FScopeLock Lock(&NaniteMutex);

    // Gerar geometria baseada na configuração do realm
    TArray<FTransform> GeometryTransforms;

    // Usar seed baseado no índice do realm para consistência
    FRandomStream RandomStream(RealmGeometryConfiguration.ProceduralSeed + RealmIndex);

    // Calcular densidade baseada na configuração
    float DensityValue = RealmGeometryConfiguration.RealmGeometryDensity.Num() > 0 ?
        RealmGeometryConfiguration.RealmGeometryDensity[0] : 1.0f;
    int32 NumInstances = FMath::RoundToInt(Radius * DensityValue * RealmGeometryConfiguration.ProceduralComplexity);
    NumInstances = FMath::Clamp(NumInstances, 10, 10000); // Limitar entre 10 e 10000 instâncias

    // Gerar transforms aleatórios dentro do raio
    for (int32 i = 0; i < NumInstances; i++)
    {
        // Posição aleatória dentro do raio
        FVector RandomOffset = RandomStream.VRand() * RandomStream.FRandRange(0.0f, Radius);
        FVector Position = CenterLocation + RandomOffset;

        // Rotação aleatória
        FRotator RandomRotation = FRotator(
            RandomStream.FRandRange(-15.0f, 15.0f), // Pitch limitado
            RandomStream.FRandRange(0.0f, 360.0f),  // Yaw completo
            RandomStream.FRandRange(-5.0f, 5.0f)    // Roll limitado
        );

        // Escala com variação
        float ScaleVariation = RandomStream.FRandRange(0.8f, 1.2f);
        FVector Scale = FVector(ScaleVariation);

        GeometryTransforms.Add(FTransform(RandomRotation, Position, Scale));
    }

    // Criar geometria usando mesh padrão (seria configurável em produção)
    // Por enquanto, usar um placeholder
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Geometria de realm gerada - Realm: %d, Instâncias: %d, Centro: %s, Raio: %.2f"),
        RealmIndex, NumInstances, *CenterLocation.ToString(), Radius);

    return true;
}

bool UAuracronNaniteBridge::UpdateRealmGeometry(int32 RealmIndex)
{
    if (!bSystemInitialized || RealmIndex < 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Índice de realm inválido: %d"), RealmIndex);
        return false;
    }

    FScopeLock Lock(&NaniteMutex);

    // Atualizar geometria existente do realm
    // Em produção, isso atualizaria as instâncias baseado em mudanças no mundo

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Geometria de realm atualizada - Realm: %d"), RealmIndex);

    return true;
}

bool UAuracronNaniteBridge::TransitionRealmGeometry(int32 FromRealm, int32 ToRealm, float TransitionTime)
{
    if (!bSystemInitialized || FromRealm < 0 || ToRealm < 0 || TransitionTime <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros de transição inválidos"));
        return false;
    }

    FScopeLock Lock(&NaniteMutex);

    // Implementar transição suave entre realms
    // Em produção, isso criaria uma animação de fade/morph entre geometrias

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transição de realm iniciada - De: %d Para: %d, Tempo: %.2f"),
        FromRealm, ToRealm, TransitionTime);

    // Simular transição com timer
    if (UWorld* World = GetWorld())
    {
        FTimerHandle TransitionTimer;
        World->GetTimerManager().SetTimer(
            TransitionTimer,
            FTimerDelegate::CreateLambda([this, FromRealm, ToRealm]()
            {
                UE_LOG(LogTemp, Log, TEXT("AURACRON: Transição de realm completada - De: %d Para: %d"), FromRealm, ToRealm);
            }),
            TransitionTime,
            false
        );
    }

    return true;
}

UStaticMeshComponent* UAuracronNaniteBridge::SpawnPortalGeometry(const FVector& Location, int32 DestinationRealm)
{
    if (!bSystemInitialized || DestinationRealm < 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Parâmetros de portal inválidos"));
        return nullptr;
    }

    // Criar configuração de geometria para portal
    FAuracronGeometryInstance PortalConfig;
    PortalConfig.InstanceID = FString::Printf(TEXT("Portal_Realm_%d_%s"), DestinationRealm, *FGuid::NewGuid().ToString());
    PortalConfig.InstanceTransform = FTransform(FRotator::ZeroRotator, Location, FVector(2.0f)); // Portal maior
    PortalConfig.bUseDistanceCulling = true;
    PortalConfig.CullingDistance = 15000.0f; // Portais visíveis de longe
    PortalConfig.RenderPriority = 100; // Alta prioridade

    // Em produção, carregaria mesh específico de portal
    // Por enquanto, usar configuração padrão

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Portal spawned - Localização: %s, Destino: %d"),
        *Location.ToString(), DestinationRealm);

    // Retornar nullptr por enquanto até ter mesh de portal configurado
    return nullptr;
}

// === Procedural Generation ===

UProceduralMeshComponent* UAuracronNaniteBridge::GenerateProceduralGeometry(const FString& GenerationType, const TMap<FString, float>& Parameters)
{
    if (!bSystemInitialized || GenerationType.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tipo de geração inválido"));
        return nullptr;
    }

    // Criar componente procedural
    UProceduralMeshComponent* ProceduralComponent = NewObject<UProceduralMeshComponent>(GetOwner());
    if (!ProceduralComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao criar ProceduralMeshComponent"));
        return nullptr;
    }

    // Gerar geometria baseada no tipo
    TArray<FVector> Vertices;
    TArray<int32> Triangles;
    TArray<FVector> Normals;
    TArray<FVector2D> UVs;
    TArray<FColor> VertexColors;

    if (GenerationType == TEXT("Plane"))
    {
        GeneratePlaneGeometry(Parameters, Vertices, Triangles, Normals, UVs);
    }
    else if (GenerationType == TEXT("Cube"))
    {
        GenerateCubeGeometry(Parameters, Vertices, Triangles, Normals, UVs);
    }
    else if (GenerationType == TEXT("Sphere"))
    {
        GenerateSphereGeometry(Parameters, Vertices, Triangles, Normals, UVs);
    }
    else if (GenerationType == TEXT("Terrain"))
    {
        GenerateTerrainGeometry(Parameters, Vertices, Triangles, Normals, UVs);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Tipo de geometria não suportado: %s"), *GenerationType);
        ProceduralComponent->DestroyComponent();
        return nullptr;
    }

    // Aplicar geometria ao componente
    ProceduralComponent->CreateMeshSection_LinearColor(
        0, // Section index
        Vertices,
        Triangles,
        Normals,
        UVs,
        TArray<FLinearColor>(), // Vertex colors (empty)
        TArray<FProcMeshTangent>(), // Tangents (empty, will be calculated)
        true // Create collision
    );

    // Anexar ao owner
    ProceduralComponent->AttachToComponent(
        GetOwner()->GetRootComponent(),
        FAttachmentTransformRules::KeepWorldTransform
    );

    // Registrar componente
    GetOwner()->AddInstanceComponent(ProceduralComponent);
    ProceduralComponent->RegisterComponent();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Geometria procedural gerada - Tipo: %s, Vértices: %d"),
        *GenerationType, Vertices.Num());

    return ProceduralComponent;
}

bool UAuracronNaniteBridge::UpdateProceduralGeometry(UProceduralMeshComponent* ProceduralComponent, const TMap<FString, float>& NewParameters)
{
    if (!bSystemInitialized || !ProceduralComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou componente inválido"));
        return false;
    }

    // Limpar seções existentes
    ProceduralComponent->ClearAllMeshSections();

    // Regenerar geometria com novos parâmetros
    // Por simplicidade, usar tipo "Plane" como padrão para atualização
    TArray<FVector> Vertices;
    TArray<int32> Triangles;
    TArray<FVector> Normals;
    TArray<FVector2D> UVs;

    GeneratePlaneGeometry(NewParameters, Vertices, Triangles, Normals, UVs);

    // Aplicar nova geometria
    ProceduralComponent->CreateMeshSection_LinearColor(
        0, // Section index
        Vertices,
        Triangles,
        Normals,
        UVs,
        TArray<FLinearColor>(), // Vertex colors (empty)
        TArray<FProcMeshTangent>(), // Tangents (empty, will be calculated)
        true // Create collision
    );

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Geometria procedural atualizada - Vértices: %d"), Vertices.Num());

    return true;
}

UStaticMesh* UAuracronNaniteBridge::ConvertProceduralToNanite(UProceduralMeshComponent* ProceduralComponent)
{
    if (!bSystemInitialized || !ProceduralComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou componente inválido"));
        return nullptr;
    }

#if WITH_EDITORONLY_DATA
    // Em produção, isso converteria o ProceduralMeshComponent para StaticMesh
    // e habilitaria Nanite. Por enquanto, apenas simular o processo.

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Conversão procedural para Nanite simulada"));

    // Retornar nullptr por enquanto - implementação completa requer ferramentas de editor
    return nullptr;
#else
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Conversão procedural para Nanite não disponível em build de runtime"));
    return nullptr;
#endif
}

// === Performance Management ===

bool UAuracronNaniteBridge::OptimizeGeometryByDistance(const FVector& ViewerLocation)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FScopeLock Lock(&NaniteMutex);

    int32 OptimizedComponents = 0;

    // Otimizar componentes de mesh estáticos
    for (UStaticMeshComponent* MeshComponent : ActiveMeshComponents)
    {
        if (!IsValid(MeshComponent))
        {
            continue;
        }

        float Distance = FVector::Dist(ViewerLocation, MeshComponent->GetComponentLocation());

        // Aplicar LOD baseado na distância
        if (Distance > NaniteConfiguration.MaxRenderDistance)
        {
            // Muito longe - desabilitar renderização
            MeshComponent->SetVisibility(false);
        }
        else if (Distance > NaniteConfiguration.MaxRenderDistance * 0.7f)
        {
            // Distância média - LOD baixo
            MeshComponent->SetVisibility(true);
            MeshComponent->SetForcedLodModel(3);
        }
        else if (Distance > NaniteConfiguration.MaxRenderDistance * 0.3f)
        {
            // Distância próxima - LOD médio
            MeshComponent->SetVisibility(true);
            MeshComponent->SetForcedLodModel(1);
        }
        else
        {
            // Muito próximo - LOD alto
            MeshComponent->SetVisibility(true);
            MeshComponent->SetForcedLodModel(0);
        }

        OptimizedComponents++;
    }

    // Otimizar componentes instanciados
    for (UInstancedStaticMeshComponent* InstancedComponent : ActiveInstancedComponents)
    {
        if (!IsValid(InstancedComponent))
        {
            continue;
        }

        float Distance = FVector::Dist(ViewerLocation, InstancedComponent->GetComponentLocation());

        // Aplicar culling baseado na distância
        if (Distance > NaniteConfiguration.MaxRenderDistance)
        {
            InstancedComponent->SetVisibility(false);
        }
        else
        {
            InstancedComponent->SetVisibility(true);
        }

        OptimizedComponents++;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Geometria otimizada por distância - Componentes: %d, Viewer: %s"),
        OptimizedComponents, *ViewerLocation.ToString());

    return true;
}

bool UAuracronNaniteBridge::SetNaniteQuality(EAuracronNaniteQuality Quality)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FScopeLock Lock(&NaniteMutex);

    EAuracronNaniteQuality OldQuality = NaniteConfiguration.NaniteQuality;
    NaniteConfiguration.NaniteQuality = Quality;

    // Aplicar configurações baseadas na qualidade
    switch (Quality)
    {
        case EAuracronNaniteQuality::Low:
            NaniteConfiguration.MaxRenderDistance = 10000.0f;
            NaniteConfiguration.LODBias = 2.0f;
            NaniteConfiguration.bUseAggressiveCulling = true;
            break;

        case EAuracronNaniteQuality::Medium:
            NaniteConfiguration.MaxRenderDistance = 15000.0f;
            NaniteConfiguration.LODBias = 1.0f;
            NaniteConfiguration.bUseAggressiveCulling = true;
            break;

        case EAuracronNaniteQuality::High:
            NaniteConfiguration.MaxRenderDistance = 20000.0f;
            NaniteConfiguration.LODBias = 0.0f;
            NaniteConfiguration.bUseAggressiveCulling = false;
            break;

        case EAuracronNaniteQuality::Ultra:
            NaniteConfiguration.MaxRenderDistance = 30000.0f;
            NaniteConfiguration.LODBias = -1.0f;
            NaniteConfiguration.bUseAggressiveCulling = false;
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Qualidade Nanite alterada - De: %s Para: %s"),
        *UEnum::GetValueAsString(OldQuality), *UEnum::GetValueAsString(Quality));

    return true;
}

bool UAuracronNaniteBridge::CleanupUnusedGeometry()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FScopeLock Lock(&NaniteMutex);

    int32 CleanedComponents = 0;

    // Limpar componentes de mesh inválidos
    for (int32 i = ActiveMeshComponents.Num() - 1; i >= 0; i--)
    {
        if (!IsValid(ActiveMeshComponents[i]))
        {
            ActiveMeshComponents.RemoveAt(i);
            CleanedComponents++;
        }
    }

    // Limpar componentes instanciados inválidos
    for (int32 i = ActiveInstancedComponents.Num() - 1; i >= 0; i--)
    {
        if (!IsValid(ActiveInstancedComponents[i]))
        {
            ActiveInstancedComponents.RemoveAt(i);
            CleanedComponents++;
        }
    }

    // Limpar instâncias de geometria órfãs
    int32 OriginalInstanceCount = ActiveGeometryInstances.Num();
    ActiveGeometryInstances.RemoveAll([this](const FAuracronGeometryInstance& Instance)
    {
        // Verificar se ainda existe componente correspondente
        for (UStaticMeshComponent* Component : ActiveMeshComponents)
        {
            if (IsValid(Component) && Component->GetName().Contains(Instance.InstanceID))
            {
                return false; // Manter
            }
        }
        return true; // Remover
    });

    int32 CleanedInstances = OriginalInstanceCount - ActiveGeometryInstances.Num();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Limpeza de geometria concluída - Componentes: %d, Instâncias: %d"),
        CleanedComponents, CleanedInstances);

    return true;
}

TMap<FString, float> UAuracronNaniteBridge::GetNaniteStatistics() const
{
    if (!bSystemInitialized)
    {
        return TMap<FString, float>();
    }

    TMap<FString, float> Statistics;

    // Estatísticas básicas
    Statistics.Add(TEXT("ActiveMeshComponents"), float(ActiveMeshComponents.Num()));
    Statistics.Add(TEXT("ActiveInstancedComponents"), float(ActiveInstancedComponents.Num()));
    Statistics.Add(TEXT("TotalGeometryInstances"), float(ActiveGeometryInstances.Num()));
    Statistics.Add(TEXT("MaxRenderDistance"), NaniteConfiguration.MaxRenderDistance);
    Statistics.Add(TEXT("LODBias"), NaniteConfiguration.LODBias);
    Statistics.Add(TEXT("StreamingPoolSizeMB"), float(NaniteConfiguration.StreamingPoolSizeMB));

    // Estatísticas de qualidade
    Statistics.Add(TEXT("NaniteQuality"), float(static_cast<int32>(NaniteConfiguration.NaniteQuality)));
    Statistics.Add(TEXT("UseAggressiveCulling"), NaniteConfiguration.bUseAggressiveCulling ? 1.0f : 0.0f);
    Statistics.Add(TEXT("UseMeshStreaming"), NaniteConfiguration.bUseMeshStreaming ? 1.0f : 0.0f);
    Statistics.Add(TEXT("UseAutoInstancing"), NaniteConfiguration.bUseAutoInstancing ? 1.0f : 0.0f);

    // Contar instâncias totais em componentes instanciados
    int32 TotalInstances = 0;
    for (UInstancedStaticMeshComponent* Component : ActiveInstancedComponents)
    {
        if (IsValid(Component))
        {
            TotalInstances += Component->GetInstanceCount();
        }
    }
    Statistics.Add(TEXT("TotalMeshInstances"), float(TotalInstances));

    return Statistics;
}

// === Internal Methods ===

bool UAuracronNaniteBridge::InitializeNaniteSystem()
{
    if (bSystemInitialized)
    {
        return true;
    }

    // Configurar streaming de mesh
    if (!SetupMeshStreaming())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao configurar streaming de mesh"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema Nanite inicializado"));

    return true;
}

bool UAuracronNaniteBridge::SetupMeshStreaming()
{
    if (!NaniteConfiguration.bUseMeshStreaming)
    {
        return true;
    }

    // Configurar pool de streaming
    if (UWorld* World = GetWorld())
    {
        // Configurações de streaming seriam aplicadas aqui
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Streaming de mesh configurado - Pool: %d MB"), NaniteConfiguration.StreamingPoolSizeMB);
    }

    return true;
}

void UAuracronNaniteBridge::ProcessActiveGeometry(float DeltaTime)
{
    FScopeLock Lock(&NaniteMutex);

    // Atualizar estatísticas de Nanite
    NaniteStatistics.Empty();
    NaniteStatistics.Add(TEXT("ActiveMeshComponents"), float(ActiveMeshComponents.Num()));
    NaniteStatistics.Add(TEXT("ActiveInstancedComponents"), float(ActiveInstancedComponents.Num()));
    NaniteStatistics.Add(TEXT("TotalGeometryInstances"), float(ActiveGeometryInstances.Num()));
}

bool UAuracronNaniteBridge::ValidateGeometryConfiguration(const FAuracronGeometryInstance& Config) const
{
    if (Config.InstanceID.IsEmpty() || !Config.Mesh.IsValid())
    {
        return false;
    }

    if (Config.CullingDistance <= 0.0f || Config.RenderPriority < 0)
    {
        return false;
    }

    return true;
}

bool UAuracronNaniteBridge::ApplyNaniteSettings(UStaticMeshComponent* MeshComponent, const FAuracronNaniteConfiguration& Config)
{
    if (!MeshComponent)
    {
        return false;
    }

    // Configurar culling
    if (Config.bUseDistanceCulling)
    {
        MeshComponent->SetCullDistance(Config.CullingDistance);
    }

    // Configurar LOD bias
    MeshComponent->SetForcedLodModel(FMath::RoundToInt(Config.LODBias));

    return true;
}

// === Geometry Generation Helpers ===

void UAuracronNaniteBridge::GeneratePlaneGeometry(const TMap<FString, float>& Parameters, TArray<FVector>& Vertices, TArray<int32>& Triangles, TArray<FVector>& Normals, TArray<FVector2D>& UVs)
{
    // Parâmetros padrão
    float Width = Parameters.Contains(TEXT("Width")) ? Parameters[TEXT("Width")] : 1000.0f;
    float Height = Parameters.Contains(TEXT("Height")) ? Parameters[TEXT("Height")] : 1000.0f;
    int32 WidthSegments = FMath::Max(1, FMath::RoundToInt(Parameters.Contains(TEXT("WidthSegments")) ? Parameters[TEXT("WidthSegments")] : 10.0f));
    int32 HeightSegments = FMath::Max(1, FMath::RoundToInt(Parameters.Contains(TEXT("HeightSegments")) ? Parameters[TEXT("HeightSegments")] : 10.0f));

    // Gerar vértices
    for (int32 y = 0; y <= HeightSegments; y++)
    {
        for (int32 x = 0; x <= WidthSegments; x++)
        {
            float U = float(x) / float(WidthSegments);
            float V = float(y) / float(HeightSegments);

            FVector Vertex(
                (U - 0.5f) * Width,
                (V - 0.5f) * Height,
                0.0f
            );

            Vertices.Add(Vertex);
            Normals.Add(FVector::UpVector);
            UVs.Add(FVector2D(U, V));
        }
    }

    // Gerar triângulos
    for (int32 y = 0; y < HeightSegments; y++)
    {
        for (int32 x = 0; x < WidthSegments; x++)
        {
            int32 i0 = y * (WidthSegments + 1) + x;
            int32 i1 = i0 + 1;
            int32 i2 = (y + 1) * (WidthSegments + 1) + x;
            int32 i3 = i2 + 1;

            // Primeiro triângulo
            Triangles.Add(i0);
            Triangles.Add(i2);
            Triangles.Add(i1);

            // Segundo triângulo
            Triangles.Add(i1);
            Triangles.Add(i2);
            Triangles.Add(i3);
        }
    }
}

void UAuracronNaniteBridge::GenerateCubeGeometry(const TMap<FString, float>& Parameters, TArray<FVector>& Vertices, TArray<int32>& Triangles, TArray<FVector>& Normals, TArray<FVector2D>& UVs)
{
    float Size = Parameters.Contains(TEXT("Size")) ? Parameters[TEXT("Size")] : 100.0f;
    float HalfSize = Size * 0.5f;

    // Vértices do cubo
    TArray<FVector> CubeVertices = {
        // Face frontal
        FVector(-HalfSize, -HalfSize, -HalfSize), FVector(HalfSize, -HalfSize, -HalfSize), FVector(HalfSize, HalfSize, -HalfSize), FVector(-HalfSize, HalfSize, -HalfSize),
        // Face traseira
        FVector(-HalfSize, -HalfSize, HalfSize), FVector(HalfSize, -HalfSize, HalfSize), FVector(HalfSize, HalfSize, HalfSize), FVector(-HalfSize, HalfSize, HalfSize)
    };

    // Faces do cubo (cada face tem 4 vértices)
    TArray<TArray<int32>> Faces = {
        {0, 1, 2, 3}, // Frente
        {5, 4, 7, 6}, // Trás
        {4, 0, 3, 7}, // Esquerda
        {1, 5, 6, 2}, // Direita
        {3, 2, 6, 7}, // Topo
        {4, 5, 1, 0}  // Base
    };

    TArray<FVector> FaceNormals = {
        FVector(0, 0, -1), // Frente
        FVector(0, 0, 1),  // Trás
        FVector(-1, 0, 0), // Esquerda
        FVector(1, 0, 0),  // Direita
        FVector(0, 0, 1),  // Topo
        FVector(0, 0, -1)  // Base
    };

    // Gerar geometria para cada face
    for (int32 FaceIndex = 0; FaceIndex < Faces.Num(); FaceIndex++)
    {
        const TArray<int32>& Face = Faces[FaceIndex];
        FVector Normal = FaceNormals[FaceIndex];

        int32 BaseVertexIndex = Vertices.Num();

        // Adicionar vértices da face
        for (int32 i = 0; i < 4; i++)
        {
            Vertices.Add(CubeVertices[Face[i]]);
            Normals.Add(Normal);
            UVs.Add(FVector2D(i % 2, i / 2));
        }

        // Adicionar triângulos (2 por face)
        Triangles.Add(BaseVertexIndex + 0);
        Triangles.Add(BaseVertexIndex + 1);
        Triangles.Add(BaseVertexIndex + 2);

        Triangles.Add(BaseVertexIndex + 0);
        Triangles.Add(BaseVertexIndex + 2);
        Triangles.Add(BaseVertexIndex + 3);
    }
}

void UAuracronNaniteBridge::GenerateSphereGeometry(const TMap<FString, float>& Parameters, TArray<FVector>& Vertices, TArray<int32>& Triangles, TArray<FVector>& Normals, TArray<FVector2D>& UVs)
{
    float Radius = Parameters.Contains(TEXT("Radius")) ? Parameters[TEXT("Radius")] : 100.0f;
    int32 Segments = FMath::Max(3, FMath::RoundToInt(Parameters.Contains(TEXT("Segments")) ? Parameters[TEXT("Segments")] : 16.0f));
    int32 Rings = FMath::Max(3, FMath::RoundToInt(Parameters.Contains(TEXT("Rings")) ? Parameters[TEXT("Rings")] : 8.0f));

    // Gerar vértices
    for (int32 Ring = 0; Ring <= Rings; Ring++)
    {
        float V = float(Ring) / float(Rings);
        float Phi = V * PI;

        for (int32 Segment = 0; Segment <= Segments; Segment++)
        {
            float U = float(Segment) / float(Segments);
            float Theta = U * 2.0f * PI;

            FVector Vertex(
                Radius * FMath::Sin(Phi) * FMath::Cos(Theta),
                Radius * FMath::Sin(Phi) * FMath::Sin(Theta),
                Radius * FMath::Cos(Phi)
            );

            Vertices.Add(Vertex);
            Normals.Add(Vertex.GetSafeNormal());
            UVs.Add(FVector2D(U, V));
        }
    }

    // Gerar triângulos
    for (int32 Ring = 0; Ring < Rings; Ring++)
    {
        for (int32 Segment = 0; Segment < Segments; Segment++)
        {
            int32 i0 = Ring * (Segments + 1) + Segment;
            int32 i1 = i0 + 1;
            int32 i2 = (Ring + 1) * (Segments + 1) + Segment;
            int32 i3 = i2 + 1;

            // Primeiro triângulo
            Triangles.Add(i0);
            Triangles.Add(i2);
            Triangles.Add(i1);

            // Segundo triângulo
            Triangles.Add(i1);
            Triangles.Add(i2);
            Triangles.Add(i3);
        }
    }
}

void UAuracronNaniteBridge::GenerateTerrainGeometry(const TMap<FString, float>& Parameters, TArray<FVector>& Vertices, TArray<int32>& Triangles, TArray<FVector>& Normals, TArray<FVector2D>& UVs)
{
    float Width = Parameters.Contains(TEXT("Width")) ? Parameters[TEXT("Width")] : 2000.0f;
    float Height = Parameters.Contains(TEXT("Height")) ? Parameters[TEXT("Height")] : 2000.0f;
    int32 WidthSegments = FMath::Max(1, FMath::RoundToInt(Parameters.Contains(TEXT("WidthSegments")) ? Parameters[TEXT("WidthSegments")] : 50.0f));
    int32 HeightSegments = FMath::Max(1, FMath::RoundToInt(Parameters.Contains(TEXT("HeightSegments")) ? Parameters[TEXT("HeightSegments")] : 50.0f));
    float MaxElevation = Parameters.Contains(TEXT("MaxElevation")) ? Parameters[TEXT("MaxElevation")] : 500.0f;
    float NoiseScale = Parameters.Contains(TEXT("NoiseScale")) ? Parameters[TEXT("NoiseScale")] : 0.01f;

    // Gerar vértices com ruído para terreno
    for (int32 y = 0; y <= HeightSegments; y++)
    {
        for (int32 x = 0; x <= WidthSegments; x++)
        {
            float U = float(x) / float(WidthSegments);
            float V = float(y) / float(HeightSegments);

            float WorldX = (U - 0.5f) * Width;
            float WorldY = (V - 0.5f) * Height;

            // Gerar altura usando ruído Perlin simplificado
            float Elevation = GenerateNoiseHeight(WorldX * NoiseScale, WorldY * NoiseScale) * MaxElevation;

            FVector Vertex(WorldX, WorldY, Elevation);

            Vertices.Add(Vertex);
            UVs.Add(FVector2D(U, V));
        }
    }

    // Calcular normais e gerar triângulos
    for (int32 y = 0; y < HeightSegments; y++)
    {
        for (int32 x = 0; x < WidthSegments; x++)
        {
            int32 i0 = y * (WidthSegments + 1) + x;
            int32 i1 = i0 + 1;
            int32 i2 = (y + 1) * (WidthSegments + 1) + x;
            int32 i3 = i2 + 1;

            // Calcular normal para o quad
            FVector V1 = Vertices[i1] - Vertices[i0];
            FVector V2 = Vertices[i2] - Vertices[i0];
            FVector Normal = FVector::CrossProduct(V1, V2).GetSafeNormal();

            // Adicionar normais (mesmo para todos os vértices do quad)
            if (Normals.Num() <= i0) Normals.SetNum(Vertices.Num());
            Normals[i0] = Normal;
            Normals[i1] = Normal;
            Normals[i2] = Normal;
            Normals[i3] = Normal;

            // Primeiro triângulo
            Triangles.Add(i0);
            Triangles.Add(i2);
            Triangles.Add(i1);

            // Segundo triângulo
            Triangles.Add(i1);
            Triangles.Add(i2);
            Triangles.Add(i3);
        }
    }

    // Preencher normais restantes se necessário
    while (Normals.Num() < Vertices.Num())
    {
        Normals.Add(FVector::UpVector);
    }
}

float UAuracronNaniteBridge::GenerateNoiseHeight(float X, float Y) const
{
    // Implementação simples de ruído usando funções trigonométricas
    float Noise1 = FMath::Sin(X * 2.0f) * FMath::Cos(Y * 2.0f);
    float Noise2 = FMath::Sin(X * 4.0f) * FMath::Cos(Y * 4.0f) * 0.5f;
    float Noise3 = FMath::Sin(X * 8.0f) * FMath::Cos(Y * 8.0f) * 0.25f;

    return (Noise1 + Noise2 + Noise3) * 0.5f + 0.5f; // Normalizar para [0, 1]
}
