

[/Script/EngineSettings.GameMapsSettings]
GameDefaultMap=/Game/Maps/MainMenu
EditorStartupMap=/Game/Maps/MainMenu
LocalMapOptions=
TransitionMap=None
bUseSplitscreen=False
TwoPlayerSplitscreenLayout=Horizontal
ThreePlayerSplitscreenLayout=FavorTop
FourPlayerSplitscreenLayout=Grid
bOffsetPlayerGamepadIds=False
GameInstanceClass=/Script/Auracron.AuracronGameInstance
GameModeMapPrefixes=(("/Game/Maps/Arena","AuracronArenaGameMode"))
GameModeClassAliases=(("AuracronArenaGameMode","/Script/Auracron.AuracronArenaGameMode"))
ServerDefaultMap=/Game/Maps/Arena/Arena_Main
GlobalDefaultGameMode=/Script/Auracron.AuracronGameMode
GlobalDefaultServerGameMode=/Script/Auracron.AuracronGameMode

[/Script/Engine.RendererSettings]
r.AllowStaticLighting=False

r.GenerateMeshDistanceFields=True

r.DynamicGlobalIlluminationMethod=1

r.ReflectionMethod=1

r.SkinCache.CompileShaders=True

r.RayTracing.RayTracingProxies.ProjectEnabled=True

r.Shadow.Virtual.Enable=1

r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True

r.DefaultFeature.LocalExposure.HighlightContrastScale=0.8

r.DefaultFeature.LocalExposure.ShadowContrastScale=0.8

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
-D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM6
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
Compiler=Default
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=24000.000000
LowSampleRate=12000.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

[/Script/LinuxTargetPlatform.LinuxTargetSettings]
-TargetedRHIs=SF_VULKAN_SM5
+TargetedRHIs=SF_VULKAN_SM6

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/WorldPartitionEditor.WorldPartitionEditorSettings]
CommandletClass=Class'/Script/UnrealEd.WorldPartitionConvertCommandlet'

[/Script/Engine.UserInterfaceSettings]
bAuthorizeAutomaticWidgetVariableCreation=False
FontDPIPreset=Standard
FontDPI=72

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_Blank",NewGameName="/Script/Auracron")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_Blank",NewGameName="/Script/Auracron")

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=073580CC43EFBC38F194EBB11B45FF7D
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=

[/Script/Engine.NetworkSettings]
n.VerifyPeer=False
p.EnableMultiplayerWorldOriginRebasing=True

[/Script/OnlineSubsystemUtils.IpNetDriver]
NetConnectionClassName="OnlineSubsystemUtils.IpConnection"
ReplicationDriverClassName="OnlineSubsystemUtils.ReplicationDriver"
MaxClientRate=25000
MaxInternetClientRate=10000
RelevantTimeout=5.0
KeepAliveTime=0.2
InitialConnectTimeout=200.0
ConnectionTimeout=15.0
TimeoutMultiplierForUnoptimizedBuilds=4.0

[/Script/SocketSubsystemEOS.NetDriverEOS]
bIsUsingP2PSockets=True
NetConnectionClassName="OnlineSubsystemEOS.NetConnectionEOS"

[/Script/Engine.GameNetworkManager]
TotalNetBandwidth=32000
MaxDynamicBandwidth=7000
MinDynamicBandwidth=4000

[/Script/PCG.PCGWorldActor]
bPartitionedWorld=True

[/Script/PCG.PCGSubsystem]
bDisableRuntimeGeneration=False

[/Script/WorldPartition.WorldPartitionSubsystem]
bEnableWorldPartition=True

