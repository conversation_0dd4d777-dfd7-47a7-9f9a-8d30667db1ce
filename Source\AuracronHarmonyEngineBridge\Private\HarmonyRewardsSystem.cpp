#include "HarmonyRewardsSystem.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyEngineSubsystem.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "AbilitySystemComponent.h"
#include "Engine/NetConnection.h"
#include "OnlineSubsystemUtils.h"
#include "Online/CoreOnline.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"
#include "Engine/World.h"
#include "Engine/GameInstance.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"

UHarmonyRewardsSystem::UHarmonyRewardsSystem()
{
    // Default configuration
    MaxRewardsPerSession = 10;
    RewardCooldownTime = 60.0f; // 1 minute between rewards
    bEnableProgressiveRewards = true;
    bEnableSpecialEvents = true;
    
    // Initialize special event state
    bSpecialEventActive = false;
    CurrentEventMultiplier = 1.0f;
    
    // Initialize tier requirements
    InitializeTierRequirements();
    
    // Initialize default rewards
    InitializeDefaultRewards();
}

void UHarmonyRewardsSystem::ProcessKindnessReward(const FString& PlayerID, const FKindnessReward& Reward)
{
    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Cannot process reward for empty PlayerID"));
        return;
    }
    
    // Check reward cooldown
    if (IsPlayerOnRewardCooldown(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s is on reward cooldown"), *PlayerID);
        return;
    }
    
    // Check session reward limit
    if (HasPlayerReachedSessionLimit(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s has reached session reward limit"), *PlayerID);
        return;
    }
    
    // Get or create player progress
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);
    
    // Work with the original FKindnessReward
    FKindnessReward ProcessedReward = Reward;

    if (bSpecialEventActive)
    {
        ProcessSpecialEventBonus(PlayerID, ProcessedReward);
    }

    // Update player progress
    Progress.TotalKindnessPoints += ProcessedReward.KindnessPoints;
    Progress.LastRewardTime = FDateTime::Now();
    Progress.RewardsThisSession++;

    // Update category progress
    ERewardCategory Category = DetermineRewardCategory(ProcessedReward);
    int32& CategoryPoints = Progress.CategoryProgress.FindOrAdd(Category);
    CategoryPoints += ProcessedReward.KindnessPoints;

    // Check for tier promotion
    CheckForTierPromotion(PlayerID);

    // Apply reward effects
    ApplyRewardEffects(PlayerID, ProcessedReward);
    
    // Update reward cooldown
    LastRewardTimes.Add(PlayerID, FDateTime::Now());
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Processed kindness reward for player %s: %d points"),
        *PlayerID, Reward.KindnessPoints);
}

bool UHarmonyRewardsSystem::GrantReward(const FString& PlayerID, const FString& RewardID)
{
    if (!ValidateRewardEligibility(PlayerID, RewardID))
    {
        return false;
    }
    
    const FHarmonyReward* Reward = RegisteredRewards.Find(RewardID);
    if (!Reward)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Reward not found: %s"), *RewardID);
        return false;
    }

    // Get player progress
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);

    // Check if player has enough kindness points
    if (Progress.TotalKindnessPoints < Reward->KindnessPointsRequired)
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s doesn't have enough kindness points for reward %s"),
            *PlayerID, *RewardID);
        return false;
    }

    // Grant the reward
    Progress.UnlockedRewards.AddUnique(RewardID);

    // Deduct kindness points if it's a purchasable reward
    if (!Reward->bIsOneTimeReward)
    {
        Progress.TotalKindnessPoints -= Reward->KindnessPointsRequired;
    }

    // Convert to kindness reward for processing
    FKindnessReward KindnessReward = ConvertHarmonyToKindnessReward(*Reward);

    // Apply reward effects
    ApplyRewardEffects(PlayerID, KindnessReward);

    // Notify player
    NotifyPlayerOfReward(PlayerID, KindnessReward);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Granted reward %s to player %s"), *RewardID, *PlayerID);
    
    return true;
}

TArray<FHarmonyReward> UHarmonyRewardsSystem::GetAvailableRewards(const FString& PlayerID)
{
    TArray<FHarmonyReward> AvailableRewards;
    
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return AvailableRewards;
    }
    
    for (const auto& RewardPair : RegisteredRewards)
    {
        const FHarmonyReward& Reward = RewardPair.Value;
        
        // Check if reward is visible and player meets requirements
        if (Reward.bIsVisible && 
            Progress->TotalKindnessPoints >= Reward.KindnessPointsRequired &&
            !Progress->UnlockedRewards.Contains(Reward.RewardID))
        {
            AvailableRewards.Add(Reward);
        }
    }
    
    // Sort by kindness points required (lowest first)
    AvailableRewards.Sort([](const FHarmonyReward& A, const FHarmonyReward& B) {
        return A.KindnessPointsRequired < B.KindnessPointsRequired;
    });
    
    return AvailableRewards;
}

TArray<FHarmonyReward> UHarmonyRewardsSystem::GetUnlockedRewards(const FString& PlayerID)
{
    TArray<FHarmonyReward> UnlockedRewards;
    
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return UnlockedRewards;
    }
    
    for (const FString& RewardID : Progress->UnlockedRewards)
    {
        const FHarmonyReward* Reward = RegisteredRewards.Find(RewardID);
        if (Reward)
        {
            UnlockedRewards.Add(*Reward);
        }
    }
    
    return UnlockedRewards;
}

ERewardTier UHarmonyRewardsSystem::CalculatePlayerTier(int32 TotalKindnessPoints)
{
    // Determine tier based on total kindness points
    if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Legendary])
    {
        return ERewardTier::Legendary;
    }
    else if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Diamond])
    {
        return ERewardTier::Diamond;
    }
    else if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Platinum])
    {
        return ERewardTier::Platinum;
    }
    else if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Gold])
    {
        return ERewardTier::Gold;
    }
    else if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Silver])
    {
        return ERewardTier::Silver;
    }
    
    return ERewardTier::Bronze;
}

float UHarmonyRewardsSystem::GetProgressToNextTier(const FString& PlayerID)
{
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return 0.0f;
    }
    
    ERewardTier CurrentTier = CalculatePlayerTier(Progress->TotalKindnessPoints);
    ERewardTier NextTier = GetNextTier(CurrentTier);
    
    if (NextTier == CurrentTier)
    {
        return 1.0f; // Already at maximum tier
    }
    
    int32 CurrentTierRequirement = TierKindnessRequirements[CurrentTier];
    int32 NextTierRequirement = TierKindnessRequirements[NextTier];
    int32 PointsInCurrentTier = Progress->TotalKindnessPoints - CurrentTierRequirement;
    int32 PointsNeededForNextTier = NextTierRequirement - CurrentTierRequirement;
    
    return static_cast<float>(PointsInCurrentTier) / PointsNeededForNextTier;
}

// Private helper function implementations

void UHarmonyRewardsSystem::InitializeTierRequirements()
{
    TierKindnessRequirements.Add(ERewardTier::Bronze, 0);
    TierKindnessRequirements.Add(ERewardTier::Silver, 100);
    TierKindnessRequirements.Add(ERewardTier::Gold, 500);
    TierKindnessRequirements.Add(ERewardTier::Platinum, 1500);
    TierKindnessRequirements.Add(ERewardTier::Diamond, 5000);
    TierKindnessRequirements.Add(ERewardTier::Legendary, 15000);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initialized tier requirements"));
}

void UHarmonyRewardsSystem::InitializeDefaultRewards()
{
    // Create default rewards for each category and tier
    
    // Kindness rewards
    CreateDefaultReward(TEXT("First_Kindness"), ERewardCategory::Kindness, ERewardTier::Bronze, 
        TEXT("First Act of Kindness"), TEXT("Your first step towards building a better community!"), 10);
    
    CreateDefaultReward(TEXT("Kindness_Champion"), ERewardCategory::Kindness, ERewardTier::Gold, 
        TEXT("Kindness Champion"), TEXT("A true champion of kindness and positivity!"), 500);
    
    // Mentorship rewards
    CreateDefaultReward(TEXT("Helpful_Guide"), ERewardCategory::Mentorship, ERewardTier::Silver, 
        TEXT("Helpful Guide"), TEXT("You've shown others the way to improvement!"), 200);
    
    CreateDefaultReward(TEXT("Master_Mentor"), ERewardCategory::Mentorship, ERewardTier::Diamond, 
        TEXT("Master Mentor"), TEXT("Your guidance has transformed countless players!"), 2000);
    
    // Leadership rewards
    CreateDefaultReward(TEXT("Team_Leader"), ERewardCategory::Leadership, ERewardTier::Gold, 
        TEXT("Natural Leader"), TEXT("Your leadership inspires others to be their best!"), 750);
    
    // Healing rewards
    CreateDefaultReward(TEXT("Community_Healer"), ERewardCategory::Healing, ERewardTier::Platinum, 
        TEXT("Community Healer"), TEXT("You've helped heal the wounds of toxicity!"), 1000);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initialized %d default rewards"), RegisteredRewards.Num());
}

void UHarmonyRewardsSystem::CreateDefaultReward(const FString& RewardID, ERewardCategory Category, ERewardTier Tier,
    const FString& Name, const FString& Description, int32 KindnessPointsRequired)
{
    FHarmonyReward Reward;
    Reward.RewardID = RewardID;
    Reward.Category = Category;
    Reward.Tier = Tier;
    Reward.RewardName = Name;
    Reward.Description = Description;
    Reward.KindnessPointsRequired = KindnessPointsRequired;
    // Create temporary instance to call methods
    UHarmonyRewardsSystem* TempInstance = NewObject<UHarmonyRewardsSystem>();
    Reward.ExperienceBonus = TempInstance->CalculateTierExperienceBonus(Tier);
    Reward.CurrencyBonus = TempInstance->CalculateTierCurrencyBonus(Tier);
    Reward.bIsOneTimeReward = true;
    Reward.bIsVisible = true;
    
    // Add appropriate tags
    switch (Category)
    {
        case ERewardCategory::Kindness:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_Kindness);
            break;
        case ERewardCategory::Mentorship:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_Mentorship);
            break;
        case ERewardCategory::Healing:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_Healing);
            break;
        case ERewardCategory::Leadership:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_CommunityHero);
            break;
    }
    
    RegisteredRewards.Add(RewardID, Reward);
}

bool UHarmonyRewardsSystem::ValidateRewardEligibility(const FString& PlayerID, const FString& RewardID)
{
    if (PlayerID.IsEmpty() || RewardID.IsEmpty())
    {
        return false;
    }
    
    const FHarmonyReward* Reward = RegisteredRewards.Find(RewardID);
    if (!Reward)
    {
        return false;
    }
    
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return false;
    }
    
    // Check if already unlocked (for one-time rewards)
    if (Reward->bIsOneTimeReward && Progress->UnlockedRewards.Contains(RewardID))
    {
        return false;
    }
    
    // Check kindness points requirement
    if (Progress->TotalKindnessPoints < Reward->KindnessPointsRequired)
    {
        return false;
    }
    
    return true;
}

bool UHarmonyRewardsSystem::IsPlayerOnRewardCooldown(const FString& PlayerID)
{
    const FDateTime* LastRewardTime = LastRewardTimes.Find(PlayerID);
    if (!LastRewardTime)
    {
        return false;
    }
    
    FDateTime CurrentTime = FDateTime::Now();
    float TimeSinceLastReward = (CurrentTime - *LastRewardTime).GetTotalSeconds();
    
    return TimeSinceLastReward < RewardCooldownTime;
}

bool UHarmonyRewardsSystem::HasPlayerReachedSessionLimit(const FString& PlayerID)
{
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return false;
    }
    
    return Progress->RewardsThisSession >= MaxRewardsPerSession;
}

void UHarmonyRewardsSystem::CheckForTierPromotion(const FString& PlayerID)
{
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);
    
    ERewardTier NewTier = CalculatePlayerTier(Progress.TotalKindnessPoints);
    
    if (NewTier != Progress.CurrentTier)
    {
        ProcessTierPromotion(PlayerID, NewTier);
    }
    
    // Update tier progress
    CalculateTierProgress(PlayerID);
}

void UHarmonyRewardsSystem::ProcessTierPromotion(const FString& PlayerID, ERewardTier NewTier)
{
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);
    ERewardTier OldTier = Progress.CurrentTier;
    
    Progress.CurrentTier = NewTier;
    Progress.TierProgress = 0.0f; // Reset progress for new tier
    
    // Create tier promotion reward
    FHarmonyReward TierReward;
    TierReward.RewardID = FString::Printf(TEXT("TierPromotion_%s_%d"), *PlayerID, static_cast<int32>(NewTier));
    TierReward.Category = ERewardCategory::Consistency;
    TierReward.Tier = NewTier;
    TierReward.RewardName = FString::Printf(TEXT("%s Tier Achieved!"), *GetTierDisplayName(NewTier));
    TierReward.Description = FString::Printf(TEXT("Congratulations on reaching %s tier through consistent positive behavior!"), *GetTierDisplayName(NewTier));
    TierReward.ExperienceBonus = CalculateTierExperienceBonus(NewTier);
    TierReward.CurrencyBonus = CalculateTierCurrencyBonus(NewTier);
    TierReward.bIsOneTimeReward = true;
    
    // Convert to kindness reward for processing
    FKindnessReward KindnessTierReward = ConvertHarmonyToKindnessReward(TierReward);

    // Apply tier promotion effects
    ApplyRewardEffects(PlayerID, KindnessTierReward);

    // Notify player of promotion
    NotifyPlayerOfReward(PlayerID, KindnessTierReward);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s promoted from %s to %s tier"), 
        *PlayerID, *GetTierDisplayName(OldTier), *GetTierDisplayName(NewTier));
}







ERewardCategory UHarmonyRewardsSystem::DetermineRewardCategory(const FKindnessReward& Reward)
{
    // Analyze reward tags to determine category
    if (Reward.RewardTags.HasTag(HarmonyEngineGameplayTags::Behavior_Mentoring))
    {
        return ERewardCategory::Mentorship;
    }
    else if (Reward.RewardTags.HasTag(HarmonyEngineGameplayTags::Behavior_Healing))
    {
        return ERewardCategory::Healing;
    }
    else if (Reward.RewardTags.HasTag(HarmonyEngineGameplayTags::Reward_CommunityHero))
    {
        return ERewardCategory::Leadership;
    }
    
    return ERewardCategory::Kindness; // Default category
}

float UHarmonyRewardsSystem::CalculateTierExperienceBonus(ERewardTier Tier)
{
    switch (Tier)
    {
        case ERewardTier::Bronze: return 100.0f;
        case ERewardTier::Silver: return 250.0f;
        case ERewardTier::Gold: return 500.0f;
        case ERewardTier::Platinum: return 1000.0f;
        case ERewardTier::Diamond: return 2500.0f;
        case ERewardTier::Legendary: return 5000.0f;
        default: return 0.0f;
    }
}

float UHarmonyRewardsSystem::CalculateTierCurrencyBonus(ERewardTier Tier)
{
    switch (Tier)
    {
        case ERewardTier::Bronze: return 50.0f;
        case ERewardTier::Silver: return 150.0f;
        case ERewardTier::Gold: return 400.0f;
        case ERewardTier::Platinum: return 1000.0f;
        case ERewardTier::Diamond: return 2500.0f;
        case ERewardTier::Legendary: return 10000.0f;
        default: return 0.0f;
    }
}

FString UHarmonyRewardsSystem::GetTierDisplayName(ERewardTier Tier)
{
    switch (Tier)
    {
        case ERewardTier::Bronze: return TEXT("Bronze");
        case ERewardTier::Silver: return TEXT("Silver");
        case ERewardTier::Gold: return TEXT("Gold");
        case ERewardTier::Platinum: return TEXT("Platinum");
        case ERewardTier::Diamond: return TEXT("Diamond");
        case ERewardTier::Legendary: return TEXT("Legendary");
        default: return TEXT("Unknown");
    }
}

ERewardTier UHarmonyRewardsSystem::GetNextTier(ERewardTier CurrentTier)
{
    switch (CurrentTier)
    {
        case ERewardTier::Bronze: return ERewardTier::Silver;
        case ERewardTier::Silver: return ERewardTier::Gold;
        case ERewardTier::Gold: return ERewardTier::Platinum;
        case ERewardTier::Platinum: return ERewardTier::Diamond;
        case ERewardTier::Diamond: return ERewardTier::Legendary;
        case ERewardTier::Legendary: return ERewardTier::Legendary; // Max tier
        default: return ERewardTier::Bronze;
    }
}

// Implementation of missing functions

void UHarmonyRewardsSystem::ProcessSpecialEventBonus(const FString& PlayerID, FKindnessReward& Reward)
{
    if (!bSpecialEventActive)
    {
        return;
    }

    // Apply event multiplier to kindness points
    int32 OriginalPoints = Reward.KindnessPoints;
    Reward.KindnessPoints = FMath::RoundToInt(Reward.KindnessPoints * CurrentEventMultiplier);

    // Apply multiplier to experience bonus
    Reward.ExperienceMultiplier *= CurrentEventMultiplier;

    // Update reward description to indicate special event
    Reward.RewardDescription += FString::Printf(TEXT(" (Special Event: %s - %.1fx bonus!)"), *CurrentEventName, CurrentEventMultiplier);

    UE_LOG(LogHarmonyEngine, Log, TEXT("Applied special event bonus to player %s: %d -> %d points"),
        *PlayerID, OriginalPoints, Reward.KindnessPoints);
}

void UHarmonyRewardsSystem::CalculateTierProgress(const FString& PlayerID)
{
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);

    ERewardTier CurrentTier = Progress.CurrentTier;
    ERewardTier NextTier = GetNextTier(CurrentTier);

    if (NextTier == CurrentTier)
    {
        Progress.TierProgress = 1.0f; // Already at max tier
        return;
    }

    int32 CurrentTierRequirement = TierKindnessRequirements[CurrentTier];
    int32 NextTierRequirement = TierKindnessRequirements[NextTier];
    int32 PointsInCurrentTier = Progress.TotalKindnessPoints - CurrentTierRequirement;
    int32 PointsNeededForNextTier = NextTierRequirement - CurrentTierRequirement;

    Progress.TierProgress = static_cast<float>(PointsInCurrentTier) / PointsNeededForNextTier;
    Progress.TierProgress = FMath::Clamp(Progress.TierProgress, 0.0f, 1.0f);
}

void UHarmonyRewardsSystem::ApplyExperienceBonus(const FString& PlayerID, float ExperienceBonus)
{
    // Apply experience bonus to player
    APlayerController* PC = GetPlayerController(PlayerID);
    if (!PC || !PC->GetPlayerState<APlayerState>())
    {
        return;
    }

    // In a full implementation, this would integrate with the game's experience system
    UE_LOG(LogHarmonyEngine, Log, TEXT("Applied experience bonus of %.2f to player %s"), ExperienceBonus, *PlayerID);

    // Example integration:
    // if (UExperienceComponent* ExpComp = PC->GetPlayerState()->FindComponentByClass<UExperienceComponent>())
    // {
    //     ExpComp->AddExperience(ExperienceBonus);
    // }
}

void UHarmonyRewardsSystem::ApplyCurrencyBonus(const FString& PlayerID, float CurrencyBonus)
{
    // Apply currency bonus to player
    APlayerController* PC = GetPlayerController(PlayerID);
    if (!PC || !PC->GetPlayerState<APlayerState>())
    {
        return;
    }

    // In a full implementation, this would integrate with the game's currency system
    UE_LOG(LogHarmonyEngine, Log, TEXT("Applied currency bonus of %.2f to player %s"), CurrencyBonus, *PlayerID);

    // Example integration:
    // if (UCurrencyComponent* CurrencyComp = PC->GetPlayerState()->FindComponentByClass<UCurrencyComponent>())
    // {
    //     CurrencyComp->AddCurrency(CurrencyBonus);
    // }
}



UAbilitySystemComponent* UHarmonyRewardsSystem::GetPlayerAbilitySystemComponent(const FString& PlayerID)
{
    APlayerController* PC = GetPlayerController(PlayerID);
    if (!PC)
    {
        return nullptr;
    }

    // Try to get ASC from pawn
    if (PC->GetPawn() && PC->GetPawn()->Implements<UAbilitySystemInterface>())
    {
        return Cast<IAbilitySystemInterface>(PC->GetPawn())->GetAbilitySystemComponent();
    }

    // Try to get ASC from player state
    if (PC->GetPlayerState<APlayerState>() && PC->GetPlayerState<APlayerState>()->Implements<UAbilitySystemInterface>())
    {
        return Cast<IAbilitySystemInterface>(PC->GetPlayerState<APlayerState>())->GetAbilitySystemComponent();
    }

    return nullptr;
}

APlayerController* UHarmonyRewardsSystem::GetPlayerController(const FString& PlayerID)
{
    // Get player controller through HarmonyEngineSubsystem
    UHarmonyEngineSubsystem* HarmonySubsystem = GetWorld()->GetSubsystem<UHarmonyEngineSubsystem>();
    if (!HarmonySubsystem)
    {
        return nullptr;
    }

    // In a full implementation, HarmonyEngineSubsystem would provide player lookup
    // For now, we'll search through all player controllers
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC && PC->GetPlayerState<APlayerState>())
            {
                FString CurrentPlayerID = PC->GetPlayerState<APlayerState>()->GetUniqueId().ToString();
                if (CurrentPlayerID == PlayerID)
                {
                    return PC;
                }
            }
        }
    }

    return nullptr;
}

void UHarmonyRewardsSystem::CreateDefaultReward(const FString& RewardID, ERewardCategory Category, ERewardTier Tier, const FString& Name, const FString& Description)
{
    FHarmonyReward Reward;
    Reward.RewardID = RewardID;
    Reward.RewardName = Name;
    Reward.Description = Description;
    Reward.Category = Category;
    Reward.Tier = Tier;
    Reward.bIsActive = true;
    Reward.bIsRepeatable = false;

    // Set tier-based bonuses
    Reward.ExperienceBonus = CalculateTierExperienceBonus(Tier);
    Reward.CurrencyBonus = CalculateTierCurrencyBonus(Tier);

    // Add appropriate tags based on category
    switch (Category)
    {
        case ERewardCategory::Kindness:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Behavior_Positive);
            break;
        case ERewardCategory::Mentorship:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Behavior_Mentoring);
            break;
        case ERewardCategory::Leadership:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_CommunityHero);
            break;
        case ERewardCategory::Healing:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Behavior_Healing);
            break;
        default:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Behavior_Positive);
            break;
    }

    // Store the reward
    RegisteredRewards.Add(RewardID, Reward);

    UE_LOG(LogHarmonyEngine, Log, TEXT("Created default reward: %s (%s)"), *Name, *RewardID);
}

FHarmonyReward UHarmonyRewardsSystem::ConvertKindnessToHarmonyReward(const FKindnessReward& KindnessReward, const FString& RewardID)
{
    FHarmonyReward HarmonyReward;

    // Set basic properties
    HarmonyReward.RewardID = RewardID.IsEmpty() ? FGuid::NewGuid().ToString() : RewardID;
    HarmonyReward.Category = DetermineRewardCategory(KindnessReward);
    HarmonyReward.Tier = ERewardTier::Bronze; // Default tier, can be enhanced based on points
    HarmonyReward.RewardName = TEXT("Kindness Reward");
    HarmonyReward.Description = KindnessReward.RewardDescription;

    // Convert kindness points to required points
    HarmonyReward.KindnessPointsRequired = FMath::Max(0, KindnessReward.KindnessPoints - 50); // Require some base points

    // Convert experience multiplier to bonus
    HarmonyReward.ExperienceBonus = (KindnessReward.ExperienceMultiplier - 1.0f) * 100.0f;

    // Set currency bonus based on kindness points
    HarmonyReward.CurrencyBonus = KindnessReward.KindnessPoints * 10.0f;

    // Copy reward tags
    HarmonyReward.RewardTags = KindnessReward.RewardTags;

    // Set default properties
    HarmonyReward.bIsOneTimeReward = KindnessReward.bIsSpecialReward;
    HarmonyReward.bIsActive = true;
    HarmonyReward.bIsRepeatable = !KindnessReward.bIsSpecialReward;
    HarmonyReward.bIsVisible = true;

    return HarmonyReward;
}

FKindnessReward UHarmonyRewardsSystem::ConvertHarmonyToKindnessReward(const FHarmonyReward& HarmonyReward)
{
    FKindnessReward KindnessReward;

    // Convert points - use currency bonus as base
    KindnessReward.KindnessPoints = FMath::RoundToInt(HarmonyReward.CurrencyBonus / 10.0f);

    // Convert experience bonus to multiplier
    KindnessReward.ExperienceMultiplier = 1.0f + (HarmonyReward.ExperienceBonus / 100.0f);

    // Set description
    KindnessReward.RewardDescription = HarmonyReward.Description;

    // Copy reward tags
    KindnessReward.RewardTags = HarmonyReward.RewardTags;

    // Set special reward flag
    KindnessReward.bIsSpecialReward = HarmonyReward.bIsOneTimeReward;

    return KindnessReward;
}

// Implementation of missing UFUNCTION methods

bool UHarmonyRewardsSystem::IsRewardUnlocked(const FString& PlayerID, const FString& RewardID)
{
    // Check if reward is unlocked for player using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("AURACRON: Checking if reward %s is unlocked for player %s"), *RewardID, *PlayerID);

    if (PlayerID.IsEmpty() || RewardID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid parameters for IsRewardUnlocked"));
        return false;
    }

    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("AURACRON: No progress found for player %s"), *PlayerID);
        return false;
    }

    bool bIsUnlocked = Progress->UnlockedRewards.Contains(RewardID);
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("AURACRON: Reward %s is %s for player %s"),
           *RewardID, bIsUnlocked ? TEXT("unlocked") : TEXT("locked"), *PlayerID);

    return bIsUnlocked;
}

FPlayerRewardProgress UHarmonyRewardsSystem::GetPlayerProgress(const FString& PlayerID)
{
    // Get player progress using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("AURACRON: Getting progress for player: %s"), *PlayerID);

    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid player ID for GetPlayerProgress"));
        return FPlayerRewardProgress();
    }

    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: No progress found for player %s, returning default"), *PlayerID);
        return FPlayerRewardProgress();
    }

    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("AURACRON: Player %s has %d total kindness points, tier: %d"),
           *PlayerID, Progress->TotalKindnessPoints, (int32)Progress->CurrentTier);

    return *Progress;
}

void UHarmonyRewardsSystem::UpdatePlayerProgress(const FString& PlayerID, int32 Points, ERewardCategory Category)
{
    // Update player progress using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Updating progress for player %s: +%d points in category %d"),
           *PlayerID, Points, (int32)Category);

    if (PlayerID.IsEmpty() || Points <= 0)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid parameters for UpdatePlayerProgress"));
        return;
    }

    // Get or create player progress
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);

    // Update total points
    Progress.TotalKindnessPoints += Points;

    // Update category progress
    int32& CategoryPoints = Progress.CategoryProgress.FindOrAdd(Category);
    CategoryPoints += Points;

    // Update last activity time
    Progress.LastRewardTime = FDateTime::Now();

    // Check for tier promotion
    ERewardTier NewTier = CalculatePlayerTier(Progress.TotalKindnessPoints);
    if (NewTier != Progress.CurrentTier)
    {
        ProcessTierPromotion(PlayerID, NewTier);
    }

    // Update tier progress
    CalculateTierProgress(PlayerID);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Player %s now has %d total points, tier: %d"),
           *PlayerID, Progress.TotalKindnessPoints, (int32)Progress.CurrentTier);
}

void UHarmonyRewardsSystem::RegisterReward(const FHarmonyReward& Reward)
{
    // Register reward using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Registering reward: %s (%s)"), *Reward.RewardName, *Reward.RewardID);

    if (Reward.RewardID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot register reward with empty ID"));
        return;
    }

    // Check if reward already exists
    if (RegisteredRewards.Contains(Reward.RewardID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Reward %s already exists, updating"), *Reward.RewardID);
    }

    // Store the reward
    RegisteredRewards.Add(Reward.RewardID, Reward);

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Successfully registered reward %s. Total rewards: %d"),
           *Reward.RewardID, RegisteredRewards.Num());
}

void UHarmonyRewardsSystem::UnregisterReward(const FString& RewardID)
{
    // Unregister reward using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Unregistering reward: %s"), *RewardID);

    if (RewardID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Cannot unregister reward with empty ID"));
        return;
    }

    if (!RegisteredRewards.Contains(RewardID))
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Reward %s not found for unregistration"), *RewardID);
        return;
    }

    // Remove the reward
    RegisteredRewards.Remove(RewardID);

    // Remove from all player progress (if they had it unlocked)
    for (auto& ProgressPair : PlayerProgress)
    {
        FPlayerRewardProgress& Progress = ProgressPair.Value;
        Progress.UnlockedRewards.Remove(RewardID);
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Successfully unregistered reward %s. Remaining rewards: %d"),
           *RewardID, RegisteredRewards.Num());
}

FHarmonyReward UHarmonyRewardsSystem::GetReward(const FString& RewardID)
{
    // Get reward using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("AURACRON: Getting reward: %s"), *RewardID);

    if (RewardID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid reward ID for GetReward"));
        return FHarmonyReward();
    }

    const FHarmonyReward* Reward = RegisteredRewards.Find(RewardID);
    if (!Reward)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Reward not found: %s"), *RewardID);
        return FHarmonyReward();
    }

    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("AURACRON: Found reward %s: %s"), *RewardID, *Reward->RewardName);

    return *Reward;
}

void UHarmonyRewardsSystem::TriggerSpecialEvent(const FString& EventName, float Multiplier)
{
    // Trigger special event using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Triggering special event: %s (%.1fx multiplier)"), *EventName, Multiplier);

    if (EventName.IsEmpty() || Multiplier <= 0.0f)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid parameters for TriggerSpecialEvent"));
        return;
    }

    // Set event state
    bSpecialEventActive = true;
    CurrentEventName = EventName;
    CurrentEventMultiplier = FMath::Clamp(Multiplier, 1.0f, 10.0f); // Reasonable limits
    SpecialEventStartTime = FDateTime::Now();

    // Set default event duration (can be made configurable)
    SpecialEventDuration = FTimespan::FromHours(24); // 24 hour event

    double EventDurationHours = SpecialEventDuration.GetTotalHours();
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Special event '%s' is now active with %.1fx multiplier for %.1f hours"),
           *CurrentEventName, CurrentEventMultiplier, EventDurationHours);

    // Broadcast event to all players (would integrate with notification system)
    BroadcastSpecialEventNotification(EventName, Multiplier);
}

bool UHarmonyRewardsSystem::IsSpecialEventActive() const
{
    // Check if special event is active using UE 5.6 robust implementation
    if (!bSpecialEventActive)
    {
        return false;
    }

    // Check if event has expired
    FDateTime Now = FDateTime::Now();
    FTimespan ElapsedTime = Now - SpecialEventStartTime;

    if (ElapsedTime > SpecialEventDuration)
    {
        // Event has expired - would normally clean this up in a tick function
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Special event '%s' has expired"), *CurrentEventName);
        return false;
    }

    return true;
}

float UHarmonyRewardsSystem::GetCurrentEventMultiplier() const
{
    // Get current event multiplier using UE 5.6 robust implementation
    if (!IsSpecialEventActive())
    {
        return 1.0f; // No bonus when no event is active
    }

    return CurrentEventMultiplier;
}

// Implementation of missing private helper functions

void UHarmonyRewardsSystem::ApplyRewardEffects(const FString& PlayerID, const FKindnessReward& Reward)
{
    // Apply reward effects using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Applying reward effects for player %s"), *PlayerID);

    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid player ID for ApplyRewardEffects"));
        return;
    }

    // Apply experience bonus
    if (Reward.ExperienceMultiplier > 1.0f)
    {
        float ExperienceBonus = (Reward.ExperienceMultiplier - 1.0f) * 100.0f;
        ApplyExperienceBonus(PlayerID, ExperienceBonus);
    }

    // Apply currency bonus based on kindness points
    if (Reward.KindnessPoints > 0)
    {
        float CurrencyBonus = Reward.KindnessPoints * 10.0f; // 10 currency per kindness point
        ApplyCurrencyBonus(PlayerID, CurrencyBonus);
    }

    // Apply gameplay effects if available
    UAbilitySystemComponent* ASC = GetPlayerAbilitySystemComponent(PlayerID);
    if (ASC && !Reward.RewardTags.IsEmpty())
    {
        // In a full implementation, this would apply gameplay effects based on reward tags
        UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Would apply gameplay effects for reward tags"));
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Applied reward effects for player %s: %d kindness points, %.2fx experience"),
           *PlayerID, Reward.KindnessPoints, Reward.ExperienceMultiplier);
}

void UHarmonyRewardsSystem::NotifyPlayerOfReward(const FString& PlayerID, const FKindnessReward& Reward)
{
    // Notify player of reward using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying player %s of reward: %s"), *PlayerID, *Reward.RewardDescription);

    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid player ID for NotifyPlayerOfReward"));
        return;
    }

    APlayerController* PC = GetPlayerController(PlayerID);
    if (!PC)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Player controller not found for %s"), *PlayerID);
        return;
    }

    // Create notification message
    FString NotificationTitle = TEXT("Kindness Reward!");
    FString NotificationMessage = FString::Printf(TEXT("You earned %d kindness points! %s"),
                                                  Reward.KindnessPoints, *Reward.RewardDescription);

    // In a full implementation, this would integrate with the game's notification system
    // For now, we'll use a simple client RPC or UI notification
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Reward notification - Title: %s, Message: %s"),
           *NotificationTitle, *NotificationMessage);

    // Example integration with UI system:
    // if (UUINotificationSubsystem* NotificationSystem = GetWorld()->GetSubsystem<UUINotificationSubsystem>())
    // {
    //     NotificationSystem->ShowRewardNotification(PC, NotificationTitle, NotificationMessage, Reward);
    // }
}

void UHarmonyRewardsSystem::BroadcastSpecialEventNotification(const FString& EventName, float Multiplier)
{
    // Broadcast special event notification using UE 5.6 robust implementation
    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Broadcasting special event notification: %s (%.1fx)"), *EventName, Multiplier);

    if (EventName.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("AURACRON: Invalid event name for broadcast"));
        return;
    }

    // Create event notification message
    FString EventTitle = FString::Printf(TEXT("Special Event: %s"), *EventName);
    double EventDurationHours = SpecialEventDuration.GetTotalHours();
    FString EventMessage = FString::Printf(TEXT("All kindness rewards are now %.1fx more valuable! Event duration: %.1f hours"),
                                          Multiplier, EventDurationHours);

    // Broadcast to all connected players
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC && PC->GetPlayerState<APlayerState>())
            {
                // In a full implementation, this would send a client RPC or UI notification
                UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Notifying player of special event: %s"),
                       *PC->GetPlayerState<APlayerState>()->GetPlayerName());
            }
        }
    }

    UE_LOG(LogHarmonyEngine, Log, TEXT("AURACRON: Special event notification broadcasted successfully"));
}
