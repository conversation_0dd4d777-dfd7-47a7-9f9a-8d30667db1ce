// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Landscape Integration Header
// Bridge 3.7: World Partition - Landscape Integration

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"

// Landscape includes for UE5.6
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "LandscapeStreamingProxy.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeSubsystem.h"
#include "LandscapeHeightfieldCollisionComponent.h"

// Material includes
#include "Materials/Material.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialParameterCollection.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Texture2D.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Math/Box.h"

#include "AuracronWorldPartitionLandscape.generated.h"

// Forward declarations
class UAuracronWorldPartitionLandscapeManager;
class ALandscape;
class ALandscapeProxy;
class ALandscapeStreamingProxy;
class ULandscapeComponent;

// =============================================================================
// LANDSCAPE TYPES AND ENUMS
// =============================================================================

// Landscape streaming states
UENUM(BlueprintType)
enum class EAuracronLandscapeStreamingState : uint8
{
    Unloaded                UMETA(DisplayName = "Unloaded"),
    Loading                 UMETA(DisplayName = "Loading"),
    Loaded                  UMETA(DisplayName = "Loaded"),
    Unloading               UMETA(DisplayName = "Unloading"),
    Failed                  UMETA(DisplayName = "Failed")
};

// Landscape LOD states
UENUM(BlueprintType)
enum class EAuracronLandscapeLODState : uint8
{
    LOD0                    UMETA(DisplayName = "LOD 0 (Highest)"),
    LOD1                    UMETA(DisplayName = "LOD 1"),
    LOD2                    UMETA(DisplayName = "LOD 2"),
    LOD3                    UMETA(DisplayName = "LOD 3"),
    LOD4                    UMETA(DisplayName = "LOD 4"),
    LOD5                    UMETA(DisplayName = "LOD 5"),
    LOD6                    UMETA(DisplayName = "LOD 6"),
    LOD7                    UMETA(DisplayName = "LOD 7 (Lowest)")
};

// Heightmap quality levels
UENUM(BlueprintType)
enum class EAuracronHeightmapQuality : uint8
{
    Low                     UMETA(DisplayName = "Low"),
    Medium                  UMETA(DisplayName = "Medium"),
    High                    UMETA(DisplayName = "High"),
    Ultra                   UMETA(DisplayName = "Ultra")
};

// Material streaming types
UENUM(BlueprintType)
enum class EAuracronMaterialStreamingType : uint8
{
    Static                  UMETA(DisplayName = "Static"),
    Dynamic                 UMETA(DisplayName = "Dynamic"),
    Adaptive                UMETA(DisplayName = "Adaptive"),
    OnDemand                UMETA(DisplayName = "On Demand")
};

// =============================================================================
// LANDSCAPE CONFIGURATION
// =============================================================================

/**
 * Landscape Configuration
 * Configuration settings for landscape integration with world partition
 */
USTRUCT(BlueprintType)
struct FAuracronLandscapeConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    bool bEnableLandscapeStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    bool bEnableHeightmapStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    bool bEnableMaterialStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape")
    bool bEnableLandscapeLOD = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float LandscapeStreamingDistance = 20000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float LandscapeUnloadingDistance = 30000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    int32 MaxConcurrentLandscapeOperations = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    EAuracronHeightmapQuality DefaultHeightmapQuality = EAuracronHeightmapQuality::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 HeightmapResolution = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 ComponentSize = 127;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float LODDistanceMultiplier = 2.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    float BaseLODDistance = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "LOD")
    int32 MaxLODLevel = 7;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    EAuracronMaterialStreamingType MaterialStreamingType = EAuracronMaterialStreamingType::Dynamic;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    float MaterialStreamingDistance = 15000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MaxLandscapeMemoryUsageMB = 2048.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableLandscapeCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableLandscapeDebug = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogLandscapeOperations = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
    TSoftObjectPtr<UMaterialInterface> DefaultLandscapeMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quality")
    int32 QuadsPerComponent = 63;

    FAuracronLandscapeConfiguration()
    {
        bEnableLandscapeStreaming = true;
        bEnableHeightmapStreaming = true;
        bEnableMaterialStreaming = true;
        bEnableLandscapeLOD = true;
        LandscapeStreamingDistance = 20000.0f;
        LandscapeUnloadingDistance = 30000.0f;
        MaxConcurrentLandscapeOperations = 4;
        DefaultHeightmapQuality = EAuracronHeightmapQuality::High;
        HeightmapResolution = 1024;
        ComponentSize = 127;
        LODDistanceMultiplier = 2.0f;
        BaseLODDistance = 1000.0f;
        MaxLODLevel = 7;
        MaterialStreamingType = EAuracronMaterialStreamingType::Dynamic;
        MaterialStreamingDistance = 15000.0f;
        MaxLandscapeMemoryUsageMB = 2048.0f;
        bEnableLandscapeCaching = true;
        bEnableLandscapeDebug = false;
        bLogLandscapeOperations = false;
    }
};

// =============================================================================
// LANDSCAPE DESCRIPTOR
// =============================================================================

/**
 * Landscape Descriptor
 * Descriptor for landscape components and their properties
 */
USTRUCT(BlueprintType)
struct FAuracronLandscapeDescriptor
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    FString LandscapeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    FString LandscapeName;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    FVector Location = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    FRotator Rotation = FRotator::ZeroRotator;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    FVector Scale = FVector::OneVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    FBox Bounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    EAuracronLandscapeStreamingState StreamingState = EAuracronLandscapeStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    EAuracronLandscapeLODState CurrentLODState = EAuracronLandscapeLODState::LOD0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    int32 ComponentCountX = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    int32 ComponentCountY = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    int32 HeightmapResolution = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    EAuracronHeightmapQuality HeightmapQuality = EAuracronHeightmapQuality::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    TArray<FString> MaterialLayers;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    bool bIsStreaming = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    bool bHasCollision = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    FDateTime LastAccessTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    TWeakObjectPtr<ALandscapeProxy> LandscapeProxy;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landscape Descriptor")
    TWeakObjectPtr<ULandscapeInfo> LandscapeInfo;

    FAuracronLandscapeDescriptor()
    {
        Location = FVector::ZeroVector;
        Rotation = FRotator::ZeroRotator;
        Scale = FVector::OneVector;
        StreamingState = EAuracronLandscapeStreamingState::Unloaded;
        CurrentLODState = EAuracronLandscapeLODState::LOD0;
        ComponentCountX = 0;
        ComponentCountY = 0;
        HeightmapResolution = 1024;
        HeightmapQuality = EAuracronHeightmapQuality::High;
        bIsStreaming = false;
        bHasCollision = true;
        MemoryUsageMB = 0.0f;
        CreationTime = FDateTime::Now();
        LastAccessTime = CreationTime;
    }
};

// =============================================================================
// HEIGHTMAP DATA
// =============================================================================

/**
 * Heightmap Data
 * Data structure for heightmap information and streaming
 */
USTRUCT(BlueprintType)
struct FAuracronHeightmapData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    FString HeightmapId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    int32 Width = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    int32 Height = 1024;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    float MinHeight = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    float MaxHeight = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    EAuracronHeightmapQuality Quality = EAuracronHeightmapQuality::High;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    bool bIsCompressed = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    float CompressionRatio = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Heightmap")
    FDateTime LastModified;

    UPROPERTY(EditAnywhere, Category = "Heightmap")
    TArray<uint16> HeightData;

    FAuracronHeightmapData()
    {
        Width = 1024;
        Height = 1024;
        MinHeight = 0.0f;
        MaxHeight = 1000.0f;
        Quality = EAuracronHeightmapQuality::High;
        bIsCompressed = false;
        CompressionRatio = 1.0f;
        MemoryUsageMB = 0.0f;
        LastModified = FDateTime::Now();
    }
};

// =============================================================================
// LANDSCAPE STATISTICS
// =============================================================================

/**
 * Landscape Statistics
 * Performance and usage statistics for landscape system
 */
USTRUCT(BlueprintType)
struct FAuracronLandscapeStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalLandscapes = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LoadedLandscapes = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 StreamingLandscapes = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageLoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 HeightmapOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 MaterialOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LODTransitions = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 FailedOperations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float LandscapeEfficiency = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronLandscapeStatistics()
    {
        TotalLandscapes = 0;
        LoadedLandscapes = 0;
        StreamingLandscapes = 0;
        TotalMemoryUsageMB = 0.0f;
        AverageLoadingTime = 0.0f;
        HeightmapOperations = 0;
        MaterialOperations = 0;
        LODTransitions = 0;
        FailedOperations = 0;
        LandscapeEfficiency = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// WORLD PARTITION LANDSCAPE MANAGER
// =============================================================================

/**
 * World Partition Landscape Manager
 * Central manager for landscape integration with world partition
 */
UCLASS(BlueprintType, Blueprintable)
class UAuracronWorldPartitionLandscapeManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    static UAuracronWorldPartitionLandscapeManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    void Initialize(const FAuracronLandscapeConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    void Tick(float DeltaTime);

    // Landscape creation and management
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    FString CreateLandscape(const FVector& Location, int32 ComponentCountX, int32 ComponentCountY, int32 HeightmapResolution = 1024);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool RemoveLandscape(const FString& LandscapeId);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    FAuracronLandscapeDescriptor GetLandscapeDescriptor(const FString& LandscapeId) const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    TArray<FAuracronLandscapeDescriptor> GetAllLandscapes() const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    TArray<FString> GetLandscapeIds() const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool DoesLandscapeExist(const FString& LandscapeId) const;

    // Landscape streaming
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool LoadLandscape(const FString& LandscapeId);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool UnloadLandscape(const FString& LandscapeId);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    EAuracronLandscapeStreamingState GetLandscapeStreamingState(const FString& LandscapeId) const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    TArray<FString> GetLoadedLandscapes() const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    TArray<FString> GetStreamingLandscapes() const;

    // Heightmap management
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool LoadHeightmap(const FString& LandscapeId, const FAuracronHeightmapData& HeightmapData);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool UnloadHeightmap(const FString& LandscapeId);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    FAuracronHeightmapData GetHeightmapData(const FString& LandscapeId) const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool UpdateHeightmap(const FString& LandscapeId, const TArray<float>& HeightData);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    float GetHeightAtLocation(const FString& LandscapeId, const FVector& Location) const;

    // Material streaming
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool LoadLandscapeMaterials(const FString& LandscapeId);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool UnloadLandscapeMaterials(const FString& LandscapeId);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool SetLandscapeMaterial(const FString& LandscapeId, const FString& MaterialPath);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    TArray<FString> GetLandscapeMaterials(const FString& LandscapeId) const;

    // LOD management
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool SetLandscapeLOD(const FString& LandscapeId, EAuracronLandscapeLODState LODState);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    EAuracronLandscapeLODState GetLandscapeLOD(const FString& LandscapeId) const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    void UpdateDistanceBasedLODs(const FVector& ViewerLocation);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    EAuracronLandscapeLODState CalculateLODForDistance(float Distance) const;

    // Cell integration
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    TArray<FString> GetLandscapesInCell(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    FString GetLandscapeCell(const FString& LandscapeId) const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool MoveLandscapeToCell(const FString& LandscapeId, const FString& CellId);

    // Collision management
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool EnableLandscapeCollision(const FString& LandscapeId, bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool IsLandscapeCollisionEnabled(const FString& LandscapeId) const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool UpdateCollisionMesh(const FString& LandscapeId);

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    void SetConfiguration(const FAuracronLandscapeConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    FAuracronLandscapeConfiguration GetConfiguration() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    FAuracronLandscapeStatistics GetLandscapeStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    int32 GetTotalLandscapeCount() const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    int32 GetLoadedLandscapeCount() const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    float GetTotalMemoryUsage() const;

    // Debug and utilities
    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    void EnableLandscapeDebug(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    bool IsLandscapeDebugEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    void LogLandscapeState() const;

    UFUNCTION(BlueprintCallable, Category = "Landscape Manager")
    void DrawDebugLandscapeInfo(UWorld* World) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLandscapeLoaded, FString, LandscapeId, bool, bSuccess);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLandscapeUnloaded, FString, LandscapeId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLandscapeLODChanged, FString, LandscapeId, EAuracronLandscapeLODState, NewLOD);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHeightmapUpdated, FString, LandscapeId, bool, bSuccess);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLandscapeLoaded OnLandscapeLoaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLandscapeUnloaded OnLandscapeUnloaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnLandscapeLODChanged OnLandscapeLODChanged;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnHeightmapUpdated OnHeightmapUpdated;

private:
    static UAuracronWorldPartitionLandscapeManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronLandscapeConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    UPROPERTY()
    TWeakObjectPtr<ULandscapeSubsystem> LandscapeSubsystem;

    // Landscape data
    TMap<FString, FAuracronLandscapeDescriptor> LandscapeDescriptors;
    TMap<FString, FAuracronHeightmapData> HeightmapData;
    TMap<FString, TWeakObjectPtr<ALandscape>> LandscapeReferences;
    TMap<FString, FString> LandscapeToCellMap; // LandscapeId -> CellId
    TMap<FString, TSet<FString>> CellToLandscapesMap; // CellId -> LandscapeIds

    // Statistics
    FAuracronLandscapeStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection LandscapeLock;

    // Internal functions
    void UpdateStatistics();
    FString GenerateLandscapeId(const FVector& Location) const;
    bool ValidateLandscapeId(const FString& LandscapeId) const;
    void OnLandscapeLoadedInternal(const FString& LandscapeId, bool bSuccess);
    void OnLandscapeUnloadedInternal(const FString& LandscapeId);
    void ValidateConfiguration();
    void UpdateLandscapeCellMapping(const FString& LandscapeId);
    ULandscapeSubsystem* GetLandscapeSubsystem() const;
    bool CreateLandscapeProxy(const FString& LandscapeId, const FVector& Location, int32 ComponentCountX, int32 ComponentCountY);
};
