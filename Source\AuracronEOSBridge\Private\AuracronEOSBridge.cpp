// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON EOS Bridge - Main Module Implementation
// Bridge 2.1: EOS Integration - Core Infrastructure

#include "AuracronEOSBridge.h"
#include "Online/OnlineSessionNames.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Async/Async.h"
#include "Kismet/GameplayStatics.h"

// Modern UE5.6 Online Subsystem includes
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Interfaces/OnlineIdentityInterface.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "Interfaces/OnlineFriendsInterface.h"
#include "Interfaces/OnlineAchievementsInterface.h"
#include "OnlineSessionSettings.h"

UAuracronEOSBridge::UAuracronEOSBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f;
    
    // Initialize connection status
    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Disconnected;
    bIsInSession = false;
    
    // Initialize configuration with defaults
    Configuration.bAutoLogin = true;
    Configuration.bEnableFriends = true;
    Configuration.bEnableAchievements = true;
    Configuration.bEnableStats = true;
    Configuration.MaxRetryAttempts = 3;
    Configuration.RetryDelaySeconds = 5.0f;
    
    // Initialize retry counter
    CurrentRetryAttempts = 0;
}

void UAuracronEOSBridge::BeginPlay()
{
    Super::BeginPlay();
    
    // Initialize EOS subsystem
    OnlineSubsystem = IOnlineSubsystem::Get();
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to get Online Subsystem"));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        return;
    }
    
    // Get interfaces
    IdentityInterface = OnlineSubsystem->GetIdentityInterface();
    SessionInterface = OnlineSubsystem->GetSessionInterface();
    FriendsInterface = OnlineSubsystem->GetFriendsInterface();
    AchievementsInterface = OnlineSubsystem->GetAchievementsInterface();

    // Initialize session search
    SessionSearch = MakeShareable(new FOnlineSessionSearch());
    
    if (!IdentityInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to get Identity Interface"));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        return;
    }
    
    // Setup delegates using modern UE5.6 API
    if (IdentityInterface.IsValid())
    {
        IdentityInterface->OnLoginCompleteDelegates->AddUObject(this, &UAuracronEOSBridge::OnLoginComplete);
    }
    
    if (SessionInterface.IsValid())
    {
        SessionInterface->OnCreateSessionCompleteDelegates.AddUObject(this, &UAuracronEOSBridge::OnCreateSessionComplete);
    }
    
    // Auto-login if enabled
    if (Configuration.bAutoLogin)
    {
        LoginWithEOS(TEXT("AccountPortal"));
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: EOS Bridge initialized successfully"));
}

void UAuracronEOSBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Cleanup delegates
    if (IdentityInterface.IsValid())
    {
        IdentityInterface->OnLoginCompleteDelegates->RemoveAll(this);
    }
    
    if (SessionInterface.IsValid())
    {
        SessionInterface->OnCreateSessionCompleteDelegates.RemoveAll(this);
    }
    
    // Logout if connected
    if (CurrentConnectionStatus == EAuracronEOSConnectionStatus::Authenticated)
    {
        LogoutFromEOS();
    }
    
    Super::EndPlay(EndPlayReason);
}

void UAuracronEOSBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    // Update connection status and handle reconnection logic
    UpdateConnectionStatus();
}

bool UAuracronEOSBridge::LoginWithEOS(const FString& LoginType)
{
    if (!IdentityInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Identity Interface not available"));
        return false;
    }
    
    if (CurrentConnectionStatus == EAuracronEOSConnectionStatus::Authenticated)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Already authenticated"));
        return true;
    }
    
    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Connecting;
    
    // Use modern UE5.6 login credentials
    FOnlineAccountCredentials Credentials;
    Credentials.Type = TEXT("accountportal"); // Modern EOS login type
    Credentials.Id = TEXT("");
    Credentials.Token = TEXT("");
    
    bool bLoginStarted = IdentityInterface->Login(0, Credentials);
    if (!bLoginStarted)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to start login process"));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Login process started"));
    return true;
}

bool UAuracronEOSBridge::LogoutFromEOS()
{
    if (!IdentityInterface.IsValid())
    {
        return false;
    }

    if (CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        return false;
    }
    
    IdentityInterface->Logout(0);
    CurrentConnectionStatus = EAuracronEOSConnectionStatus::Disconnected;
    CurrentUserID.Empty();
    CurrentDisplayName.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Logged out from EOS"));
    return true;
}

bool UAuracronEOSBridge::CreateSession(const FAuracronEOSSessionConfiguration& SessionConfig)
{
    if (!SessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Session Interface not available"));
        return false;
    }
    
    if (CurrentConnectionStatus != EAuracronEOSConnectionStatus::Authenticated)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Not authenticated"));
        return false;
    }
    
    // Create session settings using modern UE5.6 API
    TSharedPtr<FOnlineSessionSettings> SessionSettings = MakeShareable(new FOnlineSessionSettings());
    SessionSettings->NumPublicConnections = SessionConfig.MaxPlayers;
    SessionSettings->NumPrivateConnections = 0;
    SessionSettings->bIsLANMatch = false;
    SessionSettings->bShouldAdvertise = SessionConfig.bIsPublic;
    SessionSettings->bAllowJoinInProgress = SessionConfig.bAllowJoinInProgress;
    SessionSettings->bAllowInvites = true;
    SessionSettings->bUsesPresence = true;
    SessionSettings->bUseLobbiesIfAvailable = true;
    
    // Set custom session data using modern UE5.6 API
    SessionSettings->Set(FName("GameMode"), SessionConfig.GameMode, EOnlineDataAdvertisementType::ViaOnlineService);
    SessionSettings->Set(FName("MapName"), SessionConfig.MapName, EOnlineDataAdvertisementType::ViaOnlineService);
    
    // Get user ID for session creation
    TSharedPtr<const FUniqueNetId> UserId = IdentityInterface->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to get User ID"));
        return false;
    }
    
    bool bSessionCreated = SessionInterface->CreateSession(*UserId, FName(*SessionConfig.SessionName), *SessionSettings);
    if (!bSessionCreated)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create session"));
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Session creation started"));
    return true;
}

void UAuracronEOSBridge::OnLoginComplete(int32 LocalUserNum, bool bWasSuccessful, const FUniqueNetId& UserId, const FString& Error)
{
    if (bWasSuccessful)
    {
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Authenticated;
        CurrentUserID = UserId.ToString();
        
        // Get display name using modern API
        if (IdentityInterface.IsValid())
        {
            CurrentDisplayName = IdentityInterface->GetPlayerNickname(0);
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Login EOS bem-sucedido - Usuário: %s"), *CurrentDisplayName);
        
        // Load friends list if enabled
        if (Configuration.bEnableFriends)
        {
            LoadFriendsList();
        }
    }
    else
    {
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Login EOS falhou: %s"), *Error);
        
        // Handle retry logic
        HandleRetryLogic();
    }
    
    // Broadcast login completion
    OnEOSLoginCompleted.Broadcast(bWasSuccessful, Error);
}

void UAuracronEOSBridge::OnCreateSessionComplete(FName SessionName, bool bWasSuccessful)
{
    if (bWasSuccessful)
    {
        bIsInSession = true;
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Session created successfully: %s"), *SessionName.ToString());
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to create session: %s"), *SessionName.ToString());
    }
    
    // Broadcast session creation result
    OnSessionCreated.Broadcast(bWasSuccessful, SessionName.ToString());
}

bool UAuracronEOSBridge::LoadFriendsList()
{
    if (!FriendsInterface.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Friends Interface not available"));
        return false;
    }
    
    // Read friends list using modern UE5.6 API
    FriendsInterface->ReadFriendsList(0, EFriendsLists::ToString(EFriendsLists::Default),
        FOnReadFriendsListComplete::CreateUObject(this, &UAuracronEOSBridge::OnReadFriendsListComplete));
    return true;
}

void UAuracronEOSBridge::OnReadFriendsListComplete(int32 LocalUserNum, bool bWasSuccessful, const FString& ListName, const FString& ErrorStr)
{
    if (bWasSuccessful)
    {
        if (FriendsInterface.IsValid())
        {
            // Clear existing friends list
            // Note: Using simple clear without mutex for now
            // In production, implement proper thread safety
            FriendsList.Empty();
            
            // Get friends list
            TArray<TSharedRef<FOnlineFriend>> Friends;
            FriendsInterface->GetFriendsList(0, ListName, Friends);
            
            // Convert to our format
            for (const auto& Friend : Friends)
            {
                FAuracronFriendData AuracronFriend;
                AuracronFriend.UserID = Friend->GetUserId()->ToString();
                AuracronFriend.DisplayName = Friend->GetDisplayName();
                AuracronFriend.bIsOnline = Friend->GetPresence().bIsOnline;
                AuracronFriend.StatusText = Friend->GetPresence().Status.StatusStr;
                
                // Add friend to list
                // Note: Using simple add without mutex for now
                // In production, implement proper thread safety
                FriendsList.Add(AuracronFriend);
            }
            
            UE_LOG(LogTemp, Log, TEXT("AURACRON: Lista de amigos carregada - %d amigos"), FriendsList.Num());
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to load friends list: %s"), *ErrorStr);
    }
}

void UAuracronEOSBridge::UpdateConnectionStatus()
{
    // Implement connection status monitoring and auto-reconnection logic
    if (CurrentConnectionStatus == EAuracronEOSConnectionStatus::Error && Configuration.bAutoLogin)
    {
        // Implement retry logic with exponential backoff
        HandleRetryLogic();
    }
}

void UAuracronEOSBridge::HandleRetryLogic()
{
    if (CurrentRetryAttempts < Configuration.MaxRetryAttempts)
    {
        CurrentRetryAttempts++;
        float DelayTime = Configuration.RetryDelaySeconds * FMath::Pow(2.0f, CurrentRetryAttempts - 1);
        
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Retrying login in %f seconds (Attempt %d/%d)"), 
               DelayTime, CurrentRetryAttempts, Configuration.MaxRetryAttempts);
        
        // Schedule retry using lambda to handle parameter
        if (GetWorld())
        {
            FTimerDelegate TimerDelegate;
            TimerDelegate.BindLambda([this]()
            {
                LoginWithEOS(TEXT("AccountPortal"));
            });
            GetWorld()->GetTimerManager().SetTimer(RetryTimerHandle, TimerDelegate, DelayTime, false);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Max retry attempts reached. Login failed permanently."));
        CurrentConnectionStatus = EAuracronEOSConnectionStatus::Error;
    }
}

void UAuracronEOSBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    DOREPLIFETIME(UAuracronEOSBridge, CurrentConnectionStatus);
    DOREPLIFETIME(UAuracronEOSBridge, bIsInSession);
    DOREPLIFETIME(UAuracronEOSBridge, CurrentUserID);
}

EAuracronEOSConnectionStatus UAuracronEOSBridge::GetAuthenticationStatus() const
{
    return CurrentConnectionStatus;
}

FString UAuracronEOSBridge::GetUserID() const
{
    return CurrentUserID;
}

bool UAuracronEOSBridge::FindSessions(EAuracronEOSSessionType SessionType, const FString& Region)
{
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for FindSessions"));
        return false;
    }
    
    IOnlineSessionPtr LocalSessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!LocalSessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SessionInterface not available"));
        return false;
    }
    
    // Create search settings
    SessionSearch = MakeShareable(new FOnlineSessionSearch());
    SessionSearch->bIsLanQuery = false;
    SessionSearch->MaxSearchResults = 50;
    SessionSearch->PingBucketSize = 50;
    
    // Set session type filter
    SessionSearch->QuerySettings.Set(SEARCH_KEYWORDS, FString::Printf(TEXT("AuracronSessionType_%s"), *UEnum::GetValueAsString(SessionType)), EOnlineComparisonOp::Equals);
    
    if (!Region.IsEmpty())
    {
        // UE 5.6: Use custom region setting instead of deprecated SEARCH_REGION
        SessionSearch->QuerySettings.Set(FName(TEXT("REGION")), Region, EOnlineComparisonOp::Equals);
    }
    
    // Start the search
    return LocalSessionInterface->FindSessions(0, SessionSearch.ToSharedRef());
}

bool UAuracronEOSBridge::JoinSession(const FString& SessionID)
{
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for JoinSession"));
        return false;
    }
    
    IOnlineSessionPtr LocalSessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!LocalSessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SessionInterface not available"));
        return false;
    }
    
    // Find the session in our search results
    if (SessionSearch.IsValid())
    {
        for (const FOnlineSessionSearchResult& SearchResult : SessionSearch->SearchResults)
        {
            FString ResultSessionId = SearchResult.GetSessionIdStr();
            if (ResultSessionId == SessionID)
            {
                return SessionInterface->JoinSession(0, NAME_GameSession, SearchResult);
            }
        }
    }
    
    UE_LOG(LogTemp, Error, TEXT("AURACRON: Session %s not found in search results"), *SessionID);
    return false;
}

bool UAuracronEOSBridge::LeaveSession()
{
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for LeaveSession"));
        return false;
    }
    
    IOnlineSessionPtr LocalSessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!LocalSessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SessionInterface not available"));
        return false;
    }
    
    if (!bIsInSession)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Not currently in a session"));
        return false;
    }
    
    return LocalSessionInterface->DestroySession(NAME_GameSession);
}

bool UAuracronEOSBridge::DestroySession()
{
    if (!OnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for DestroySession"));
        return false;
    }
    
    IOnlineSessionPtr LocalSessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!LocalSessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SessionInterface not available"));
        return false;
    }
    
    return LocalSessionInterface->DestroySession(NAME_GameSession);
}

// === Authentication Implementation ===

FString UAuracronEOSBridge::GetDisplayName() const
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for GetDisplayName"));
        return FString();
    }

    IOnlineIdentityPtr LocalIdentityInterface = LocalOnlineSubsystem->GetIdentityInterface();
    if (!LocalIdentityInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: IdentityInterface not available"));
        return FString();
    }

    TSharedPtr<const FUniqueNetId> UserId = LocalIdentityInterface->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: No valid user ID for GetDisplayName"));
        return FString();
    }

    return LocalIdentityInterface->GetPlayerNickname(*UserId);
}

// === Friends Implementation ===

TArray<FAuracronFriendData> UAuracronEOSBridge::GetFriendsList() const
{
    TArray<FAuracronFriendData> Result;

    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for GetFriendsList"));
        return Result;
    }

    IOnlineFriendsPtr LocalFriendsInterface = LocalOnlineSubsystem->GetFriendsInterface();
    if (!LocalFriendsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: FriendsInterface not available"));
        return Result;
    }

    TArray<TSharedRef<FOnlineFriend>> Friends;
    LocalFriendsInterface->GetFriendsList(0, EFriendsLists::ToString(EFriendsLists::Default), Friends);

    for (const auto& Friend : Friends)
    {
        FAuracronFriendData AuracronFriend;
        AuracronFriend.UserID = Friend->GetUserId()->ToString();
        AuracronFriend.DisplayName = Friend->GetDisplayName();
        AuracronFriend.bIsOnline = Friend->GetPresence().bIsOnline;
        AuracronFriend.StatusText = Friend->GetPresence().Status.StatusStr;

        Result.Add(AuracronFriend);
    }

    return Result;
}

bool UAuracronEOSBridge::AddFriend(const FString& UserID)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for AddFriend"));
        return false;
    }

    IOnlineFriendsPtr LocalFriendsInterface = LocalOnlineSubsystem->GetFriendsInterface();
    if (!LocalFriendsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: FriendsInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> LocalUserId = LocalOnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!LocalUserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid local user ID"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> FriendUserId = LocalOnlineSubsystem->GetIdentityInterface()->CreateUniquePlayerId(UserID);
    if (!FriendUserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid friend user ID: %s"), *UserID);
        return false;
    }

    return LocalFriendsInterface->SendInvite(0, *FriendUserId, EFriendsLists::ToString(EFriendsLists::Default));
}

bool UAuracronEOSBridge::RemoveFriend(const FString& UserID)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for RemoveFriend"));
        return false;
    }

    IOnlineFriendsPtr LocalFriendsInterface = LocalOnlineSubsystem->GetFriendsInterface();
    if (!LocalFriendsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: FriendsInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> LocalUserId = LocalOnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!LocalUserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid local user ID"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> FriendUserId = LocalOnlineSubsystem->GetIdentityInterface()->CreateUniquePlayerId(UserID);
    if (!FriendUserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid friend user ID: %s"), *UserID);
        return false;
    }

    return LocalFriendsInterface->DeleteFriend(0, *FriendUserId, EFriendsLists::ToString(EFriendsLists::Default));
}

bool UAuracronEOSBridge::InviteFriendToSession(const FString& FriendID)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for InviteFriendToSession"));
        return false;
    }

    IOnlineSessionPtr LocalSessionInterface = LocalOnlineSubsystem->GetSessionInterface();
    if (!LocalSessionInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: SessionInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> LocalUserId = OnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!LocalUserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid local user ID"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> FriendUserId = OnlineSubsystem->GetIdentityInterface()->CreateUniquePlayerId(FriendID);
    if (!FriendUserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid friend user ID: %s"), *FriendID);
        return false;
    }

    return SessionInterface->SendSessionInviteToFriend(0, NAME_GameSession, *FriendUserId);
}

// === Achievements Implementation ===

bool UAuracronEOSBridge::UnlockAchievement(const FString& AchievementID)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for UnlockAchievement"));
        return false;
    }

    IOnlineAchievementsPtr LocalAchievementsInterface = LocalOnlineSubsystem->GetAchievementsInterface();
    if (!LocalAchievementsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: AchievementsInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> UserId = LocalOnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return false;
    }

    FOnlineAchievementsWriteRef WriteObject = MakeShareable(new FOnlineAchievementsWrite());
    WriteObject->SetFloatStat(AchievementID, 100.0f);

    LocalAchievementsInterface->WriteAchievements(*UserId, WriteObject);
    return true;
}

bool UAuracronEOSBridge::UpdateAchievementProgress(const FString& AchievementID, float Progress)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for UpdateAchievementProgress"));
        return false;
    }

    IOnlineAchievementsPtr LocalAchievementsInterface = LocalOnlineSubsystem->GetAchievementsInterface();
    if (!LocalAchievementsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: AchievementsInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> UserId = LocalOnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return false;
    }

    FOnlineAchievementsWriteRef WriteObject = MakeShareable(new FOnlineAchievementsWrite());
    WriteObject->SetFloatStat(AchievementID, Progress);

    LocalAchievementsInterface->WriteAchievements(*UserId, WriteObject);
    return true;
}

TArray<FAuracronEOSAchievement> UAuracronEOSBridge::GetAchievements() const
{
    TArray<FAuracronEOSAchievement> Result;

    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for GetAchievements"));
        return Result;
    }

    IOnlineAchievementsPtr LocalAchievementsInterface = LocalOnlineSubsystem->GetAchievementsInterface();
    if (!LocalAchievementsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: AchievementsInterface not available"));
        return Result;
    }

    TSharedPtr<const FUniqueNetId> UserId = LocalOnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return Result;
    }

    TArray<FOnlineAchievement> Achievements;
    AchievementsInterface->GetCachedAchievements(*UserId, Achievements);

    for (const auto& Achievement : Achievements)
    {
        FAuracronEOSAchievement AuracronAchievement;
        AuracronAchievement.AchievementID = Achievement.Id;
        AuracronAchievement.AchievementName = FText::FromString(Achievement.Id); // Use ID as name for now
        AuracronAchievement.AchievementDescription = FText::FromString(Achievement.Id); // Use ID as description for now
        AuracronAchievement.Progress = Achievement.Progress / 100.0f; // Convert to 0-1 range
        AuracronAchievement.bIsUnlocked = Achievement.Progress >= 100.0f;

        Result.Add(AuracronAchievement);
    }

    return Result;
}

bool UAuracronEOSBridge::IsAchievementUnlocked(const FString& AchievementID) const
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for IsAchievementUnlocked"));
        return false;
    }

    IOnlineAchievementsPtr LocalAchievementsInterface = LocalOnlineSubsystem->GetAchievementsInterface();
    if (!LocalAchievementsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: AchievementsInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> UserId = LocalOnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return false;
    }

    TArray<FOnlineAchievement> Achievements;
    LocalAchievementsInterface->GetCachedAchievements(*UserId, Achievements);

    for (const auto& Achievement : Achievements)
    {
        if (Achievement.Id == AchievementID)
        {
            return Achievement.Progress >= 100.0f;
        }
    }

    return false;
}

// === Leaderboards Implementation ===

bool UAuracronEOSBridge::SubmitScore(const FString& LeaderboardID, int32 Score)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for SubmitScore"));
        return false;
    }

    IOnlineLeaderboardsPtr LeaderboardsInterface = LocalOnlineSubsystem->GetLeaderboardsInterface();
    if (!LeaderboardsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: LeaderboardsInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> UserId = LocalOnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return false;
    }

    FOnlineLeaderboardWrite WriteObject;
    WriteObject.LeaderboardNames.Add(*LeaderboardID);
    WriteObject.SetIntStat(FString(*LeaderboardID), Score);

    return LeaderboardsInterface->WriteLeaderboards(FName(*LeaderboardID), *UserId, WriteObject);
}

int32 UAuracronEOSBridge::GetPlayerRank(const FString& LeaderboardID) const
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for GetPlayerRank"));
        return -1;
    }

    IOnlineLeaderboardsPtr LeaderboardsInterface = OnlineSubsystem->GetLeaderboardsInterface();
    if (!LeaderboardsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: LeaderboardsInterface not available"));
        return -1;
    }

    TSharedPtr<const FUniqueNetId> UserId = OnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return -1;
    }

    // Note: This is a simplified implementation. In a real scenario, you would need to
    // read the leaderboard data and find the player's rank
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: GetPlayerRank not fully implemented - returning placeholder"));
    return 0;
}

TArray<FString> UAuracronEOSBridge::GetTopRankings(const FString& LeaderboardID, int32 Count) const
{
    TArray<FString> Result;

    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for GetTopRankings"));
        return Result;
    }

    IOnlineLeaderboardsPtr LeaderboardsInterface = LocalOnlineSubsystem->GetLeaderboardsInterface();
    if (!LeaderboardsInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: LeaderboardsInterface not available"));
        return Result;
    }

    // Note: This is a simplified implementation. In a real scenario, you would need to
    // read the leaderboard data and return the top rankings
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: GetTopRankings not fully implemented - returning empty array"));
    return Result;
}

// === Player Data Implementation ===

bool UAuracronEOSBridge::SavePlayerData(const FString& Key, const FString& Data)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for SavePlayerData"));
        return false;
    }

    IOnlineUserCloudPtr UserCloudInterface = OnlineSubsystem->GetUserCloudInterface();
    if (!UserCloudInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: UserCloudInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> UserId = OnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return false;
    }

    TArray<uint8> DataBytes;
    FTCHARToUTF8 UTF8String(*Data);
    DataBytes.Append((uint8*)UTF8String.Get(), UTF8String.Length());

    return UserCloudInterface->WriteUserFile(*UserId, Key, DataBytes);
}

FString UAuracronEOSBridge::LoadPlayerData(const FString& Key)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for LoadPlayerData"));
        return FString();
    }

    IOnlineUserCloudPtr UserCloudInterface = LocalOnlineSubsystem->GetUserCloudInterface();
    if (!UserCloudInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: UserCloudInterface not available"));
        return FString();
    }

    TSharedPtr<const FUniqueNetId> UserId = LocalOnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return FString();
    }

    TArray<uint8> DataBytes;
    if (UserCloudInterface->GetFileContents(*UserId, Key, DataBytes))
    {
        return FString(UTF8_TO_TCHAR(DataBytes.GetData()));
    }

    return FString();
}

bool UAuracronEOSBridge::DeletePlayerData(const FString& Key)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for DeletePlayerData"));
        return false;
    }

    IOnlineUserCloudPtr UserCloudInterface = LocalOnlineSubsystem->GetUserCloudInterface();
    if (!UserCloudInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: UserCloudInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> UserId = LocalOnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return false;
    }

    return UserCloudInterface->DeleteUserFile(*UserId, Key, true, true);
}

// === Presence Implementation ===

bool UAuracronEOSBridge::SetPresence(const FString& Status, const TMap<FString, FString>& Properties)
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for SetPresence"));
        return false;
    }

    IOnlinePresencePtr PresenceInterface = OnlineSubsystem->GetPresenceInterface();
    if (!PresenceInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: PresenceInterface not available"));
        return false;
    }

    TSharedPtr<const FUniqueNetId> UserId = OnlineSubsystem->GetIdentityInterface()->GetUniquePlayerId(0);
    if (!UserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: No valid user ID"));
        return false;
    }

    FOnlineUserPresenceStatus PresenceStatus;
    PresenceStatus.StatusStr = Status;
    PresenceStatus.State = EOnlinePresenceState::Online;

    for (const auto& Property : Properties)
    {
        PresenceStatus.Properties.Add(Property.Key, Property.Value);
    }

    PresenceInterface->SetPresence(*UserId, PresenceStatus);
    return true;
}

FString UAuracronEOSBridge::GetFriendPresence(const FString& FriendID) const
{
    IOnlineSubsystem* LocalOnlineSubsystem = IOnlineSubsystem::Get();
    if (!LocalOnlineSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: OnlineSubsystem not available for GetFriendPresence"));
        return FString();
    }

    IOnlinePresencePtr PresenceInterface = OnlineSubsystem->GetPresenceInterface();
    if (!PresenceInterface.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: PresenceInterface not available"));
        return FString();
    }

    TSharedPtr<const FUniqueNetId> FriendUserId = OnlineSubsystem->GetIdentityInterface()->CreateUniquePlayerId(FriendID);
    if (!FriendUserId.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid friend user ID: %s"), *FriendID);
        return FString();
    }

    TSharedPtr<FOnlineUserPresence> Presence;
    if (PresenceInterface->GetCachedPresence(*FriendUserId, Presence) == EOnlineCachedResult::Success && Presence.IsValid())
    {
        return Presence->Status.StatusStr;
    }

    return FString();
}

// === Metrics Implementation ===

bool UAuracronEOSBridge::SendCustomMetric(const FString& MetricName, const TMap<FString, FString>& Properties)
{
    // Note: This is a placeholder implementation. In a real EOS integration,
    // you would use the EOS Analytics interface to send custom metrics
    UE_LOG(LogTemp, Log, TEXT("AURACRON: SendCustomMetric - %s"), *MetricName);

    for (const auto& Property : Properties)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Metric Property - %s: %s"), *Property.Key, *Property.Value);
    }

    return true;
}

bool UAuracronEOSBridge::TrackGameEvent(const FString& EventName, const TMap<FString, float>& NumericData, const TMap<FString, FString>& StringData)
{
    // Note: This is a placeholder implementation. In a real EOS integration,
    // you would use the EOS Analytics interface to track game events
    UE_LOG(LogTemp, Log, TEXT("AURACRON: TrackGameEvent - %s"), *EventName);

    for (const auto& Data : NumericData)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Event Numeric Data - %s: %f"), *Data.Key, Data.Value);
    }

    for (const auto& Data : StringData)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Event String Data - %s: %s"), *Data.Key, *Data.Value);
    }

    return true;
}

// === Replication Callbacks ===

void UAuracronEOSBridge::OnRep_ConnectionStatus()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Connection status changed to: %d"), (int32)CurrentConnectionStatus);

    // Broadcast connection status change to delegates
    OnConnectionStatusChanged.Broadcast(CurrentConnectionStatus);
}
