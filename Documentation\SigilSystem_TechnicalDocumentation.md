# Auracron Sigil System (Fusion 2.0) - Technical Documentation

## Overview

The Auracron Sigil System (Fusion 2.0) is a comprehensive gameplay system that allows players to equip and combine three different types of magical sigils to create unique archetypes with specialized abilities. The system supports 150 unique archetype combinations (5 Aegis × 5 Ruin × 6 Vesper).

## System Architecture

### Core Components

1. **UAuracronSigilosBridge** - Main component managing the entire sigil system
2. **UAuracronSigilAttributeSet** - Attribute set for sigil-related stats
3. **UAuracronSigilAbility** - Base class for sigil abilities
4. **AuracronSigilTags** - Native GameplayTags for the system

### Sigil Types

#### Aegis Sigils (Defense/Protection)
- **Primordial**: Basic shield that absorbs damage
- **Cristalino**: Shield that reflects damage back to attackers
- **Temporal**: Shield that slows incoming projectiles
- **Espectral**: Shield that absorbs magical effects
- **Absoluto**: Shield that grants temporary invulnerability

#### Ruin Sigils (Damage/Destruction)
- **Flamejante**: Continuous fire damage that spreads
- **Gélido**: Ice damage that slows enemies
- **Sombrio**: Shadow damage that reduces vision
- **Corrosivo**: Corrosive damage that reduces armor
- **Aniquilador**: Massive instant damage with execution

#### Vesper Sigils (Support/Utility)
- **Curativo**: HP regeneration for user and allies
- **Energético**: MP regeneration and cost reduction
- **Velocidade**: Movement and attack speed increase
- **Visão**: Vision range and true sight
- **Teleporte**: Short-range teleportation with charges
- **Temporal**: Time manipulation (haste/slow effects)

## Technical Implementation

### UE 5.6 Features Used

1. **Enhanced Input System**: For responsive input handling
2. **Gameplay Ability System**: For ability management and effects
3. **Native GameplayTags**: For efficient tag-based systems
4. **Niagara VFX**: For advanced visual effects
5. **MetaSound**: For dynamic audio systems
6. **Common UI**: For responsive interface design
7. **Replication System**: For multiplayer synchronization

### Key Classes and Structures

```cpp
// Main sigil types
enum class EAuracronSigiloType : uint8
{
    None, Aegis, Ruin, Vesper
};

// Equipped sigil structure
struct FAuracronEquippedSigil
{
    EAuracronSigiloType MainType;
    EAuracronAegisSigilType AegisSubtype;
    EAuracronRuinSigilType RuinSubtype;
    EAuracronVesperSigilType VesperSubtype;
    int32 Level;
    int32 Experience;
    float CurrentCooldown;
    bool bIsActive;
};

// Archetype structure (150 combinations)
struct FAuracronSigilArchetype
{
    FGameplayTag ArchetypeTag;
    FText ArchetypeName;
    FText ArchetypeDescription;
    EAuracronAegisSigilType AegisComponent;
    EAuracronRuinSigilType RuinComponent;
    EAuracronVesperSigilType VesperComponent;
    TSubclassOf<UGameplayAbility> FusionAbility;
    TArray<TSubclassOf<UGameplayEffect>> ArchetypePassiveEffects;
    float PowerMultiplier;
    float CooldownReduction;
    float EnergyEfficiency;
    int32 RequiredLevel;
    FLinearColor PrimaryColor;
    FLinearColor SecondaryColor;
    TSoftObjectPtr<UNiagaraSystem> ArchetypeVFX;
    TSoftObjectPtr<UMetaSoundSource> ArchetypeAudio;
};
```

### Core Functionality

#### Sigil Equipment
```cpp
// Equip specific sigil types
bool EquipAegisSigil(EAuracronAegisSigilType AegisType);
bool EquipRuinSigil(EAuracronRuinSigilType RuinType);
bool EquipVesperSigil(EAuracronVesperSigilType VesperType);
```

#### Individual Activation
```cpp
// Activate individual sigils
bool ActivateAegisSigil();
bool ActivateRuinSigil();
bool ActivateVesperSigil();
```

#### Fusion 2.0 Activation
```cpp
// Activate all three sigils simultaneously
bool ActivateFusion20();
bool CanActivateFusion20() const;
```

#### Archetype Management
```cpp
// Get current archetype and available combinations
FAuracronSigilArchetype GetCurrentArchetype() const;
TArray<FAuracronSigilArchetype> GetAvailableArchetypes() const;
FText GenerateArchetypeName(...) const;
```

## Gameplay Mechanics

### Fusion 2.0 System

1. **Equipment Phase**: Players equip one sigil of each type (Aegis, Ruin, Vesper)
2. **Individual Use**: Each sigil can be activated independently with unique effects
3. **Fusion Activation**: All three sigils can be activated simultaneously for enhanced effects
4. **Archetype Formation**: The combination creates a unique archetype with special abilities
5. **Cooldown Management**: Individual and fusion cooldowns are managed separately

### Progression System

- **Sigil Levels**: Each sigil can be leveled from 1 to 20
- **Experience Gain**: Sigils gain experience through use and combat
- **Power Scaling**: Higher levels increase effectiveness and unlock new features
- **Archetype Evolution**: Archetype properties scale with sigil levels

### Balance Considerations

- **Individual Power**: Each sigil is balanced for solo use
- **Fusion Bonus**: Fusion 2.0 provides 50% power bonus but longer cooldowns
- **Archetype Synergy**: Certain combinations have natural synergies
- **Resource Management**: Energy costs scale with power level

## Performance Optimization

### Memory Management
- Object pooling for visual effects
- Async loading for heavy assets
- LOD system for distant effects
- Garbage collection optimization

### Network Optimization
- Efficient replication of sigil states
- Compressed data structures
- Predictive client-side effects
- Server authority for critical operations

### Rendering Optimization
- Instanced particle systems
- Dynamic batching for similar effects
- Culling for off-screen effects
- Adaptive quality based on performance

## Integration Points

### With Existing Systems
- **Character System**: Integrates with player stats and abilities
- **Combat System**: Provides additional combat options
- **Progression System**: Ties into character advancement
- **UI System**: Provides dedicated interface elements

### External Dependencies
- **Gameplay Ability System**: Core dependency for abilities
- **Enhanced Input**: For input handling
- **Niagara**: For visual effects
- **MetaSound**: For audio effects
- **Common UI**: For interface components

## Configuration Files

1. **SigilSystemConfig.json**: Main system configuration
2. **AegisSigilsConfig.json**: Aegis sigil specifications
3. **RuinSigilsConfig.json**: Ruin sigil specifications
4. **VesperSigilsConfig.json**: Vesper sigil specifications
5. **SigilFusionCombinations.json**: All 150 archetype combinations
6. **SigilInputConfig.json**: Input system configuration
7. **SigilUIConfig.json**: UI system configuration

## Development Guidelines

### Code Standards
- Follow UE 5.6 coding conventions
- Use modern C++ features (C++20)
- Implement proper error handling
- Maintain thread safety for multiplayer
- Document all public APIs

### Testing Requirements
- Unit tests for core functionality
- Integration tests for system interactions
- Performance tests for optimization
- Multiplayer tests for replication
- Platform-specific tests for compatibility

### Deployment Checklist
- Verify all 150 combinations work correctly
- Test performance on target platforms
- Validate multiplayer synchronization
- Check accessibility compliance
- Confirm localization support

## Future Enhancements

### Planned Features
- **Sigil Crafting**: Create custom sigils
- **Legendary Combinations**: Ultra-rare archetype variants
- **Seasonal Events**: Limited-time sigil types
- **Guild Synergies**: Team-based archetype bonuses
- **PvP Balancing**: Separate PvP/PvE configurations

### Technical Improvements
- **AI-Driven Balancing**: Automatic balance adjustments
- **Cloud Analytics**: Usage data collection
- **Dynamic Content**: Server-side archetype updates
- **Cross-Platform Sync**: Save data synchronization
- **VR Support**: Virtual reality compatibility

## Troubleshooting

### Common Issues
1. **Sigils not activating**: Check AbilitySystemComponent initialization
2. **Visual effects missing**: Verify Niagara system setup
3. **Replication issues**: Check network authority
4. **Performance drops**: Review effect pooling settings
5. **Input not responding**: Validate Enhanced Input configuration

### Debug Tools
- **Sigil Debug UI**: Real-time system state display
- **Effect Visualization**: Visual debugging for effects
- **Performance Profiler**: Built-in performance monitoring
- **Network Debugger**: Replication state tracking
- **Log Categories**: Detailed logging for troubleshooting

## API Reference

### Public Methods
- Equipment: `EquipAegisSigil()`, `EquipRuinSigil()`, `EquipVesperSigil()`
- Activation: `ActivateAegisSigil()`, `ActivateRuinSigil()`, `ActivateVesperSigil()`
- Fusion: `ActivateFusion20()`, `CanActivateFusion20()`
- Query: `GetCurrentArchetype()`, `GetAvailableArchetypes()`

### Events/Delegates
- `OnSigilEquipped`: Fired when sigil is equipped
- `OnSigilActivated`: Fired when sigil is activated
- `OnArchetypeFormed`: Fired when archetype is created
- `OnFusion20Activated`: Fired when Fusion 2.0 is activated
- `OnFusion20Ended`: Fired when Fusion 2.0 ends

### GameplayTags
- Root: `Sigil`
- States: `Sigil.State.*`
- Types: `Sigil.Type.*`
- Effects: `Sigil.Effect.*`
- Input: `Sigil.Input.*`

---

**Author**: Auracron Development Team  
**Version**: 1.0.0  
**Date**: 2025-08-07  
**UE Version**: 5.6+
