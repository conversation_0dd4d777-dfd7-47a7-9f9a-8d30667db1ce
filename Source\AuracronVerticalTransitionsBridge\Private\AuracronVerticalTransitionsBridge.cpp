// AURACRON - Implementação do Bridge C++ para Sistema de Transições Verticais
// Integração com Unreal Engine 5.6 APIs
// Autor: Augment Agent
// Data: 2025-08-03
// Versão: 1.0.0

#include "AuracronVerticalTransitionsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "NavigationSystem.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Particles/ParticleSystemComponent.h"
#include "Components/AudioComponent.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "TimerManager.h"
#include "DrawDebugHelpers.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"

UAuracronVerticalTransitionsBridge::UAuracronVerticalTransitionsBridge(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // 10 FPS para otimização

    // Configurações padrão
    bUse3DNavigation = true;
    TransitionDetectionRadius = 200.0f;
    MaxChannelingTime = 10.0f;
    bUseVisualEffects = true;
    bUseAudioEffects = true;
    GenerationSeed = 98765;
    
    // Inicializar configurações padrão de transições
    InitializeDefaultTransitionProperties();
}

void UAuracronVerticalTransitionsBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Transições Verticais"));

    // Validar configuração
    if (!ValidateSystemConfiguration())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Configuração do sistema inválida"));
        return;
    }

    // Inicializar sistema
    bSystemInitialized = InitializeTransitionSystem();
    
    if (bSystemInitialized)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Transições Verticais inicializado com sucesso"));
        
        // Construir rede de transições
        BuildTransitionNetwork();
        
        // Configurar timer de atualização
        if (GetWorld())
        {
            GetWorld()->GetTimerManager().SetTimer(UpdateTimer, this, 
                &UAuracronVerticalTransitionsBridge::UpdateTransitionSystem, 1.0f, true);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Transições Verticais"));
    }
}

void UAuracronVerticalTransitionsBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Limpar timer
    if (GetWorld() && UpdateTimer.IsValid())
    {
        GetWorld()->GetTimerManager().ClearTimer(UpdateTimer);
    }
    
    // Cancelar todos os channelings
    for (auto& ChannelingPair : PlayersChanneling)
    {
        CancelTransitionChanneling(ChannelingPair.Key);
    }
    
    // Limpar rede de transições
    ClearTransitionNetwork();
    
    Super::EndPlay(EndPlayReason);
}

void UAuracronVerticalTransitionsBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (!bSystemInitialized)
    {
        return;
    }
    
    // Atualizar cooldowns
    UpdateTransitionCooldowns(DeltaTime);
    
    // Processar channelings ativos
    ProcessActiveChannelings(DeltaTime);
    
    // Detectar jogadores próximos a transições
    if (GetWorld() && GetWorld()->GetTimeSeconds() - LastPlayerDetectionTime > 0.5f)
    {
        DetectPlayersNearTransitions();
        LastPlayerDetectionTime = GetWorld()->GetTimeSeconds();
    }
}

// === Core Transition Management ===

bool UAuracronVerticalTransitionsBridge::CreateTransitionPoint(const FTransitionPointData& TransitionData)
{
    if (TransitionData.TransitionID.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: ID de transição não pode estar vazio"));
        return false;
    }
    
    FScopeLock Lock(&TransitionMutex);
    
    // Verificar se já existe
    for (const FTransitionPointData& ExistingTransition : TransitionNetwork.TransitionPoints)
    {
        if (ExistingTransition.TransitionID == TransitionData.TransitionID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s já existe"), *TransitionData.TransitionID);
            return false;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Criando ponto de transição %s"), *TransitionData.TransitionID);
    
    // Adicionar à rede
    TransitionNetwork.TransitionPoints.Add(TransitionData);
    TransitionNetwork.TotalTransitions++;
    
    // Criar componente visual se especificado
    if (TransitionData.TransitionMesh.IsValid())
    {
        CreateTransitionVisualComponent(TransitionData);
    }
    
    // Configurar efeitos
    if (bUseVisualEffects)
    {
        SetupVisualEffects(TransitionData.TransitionID);
    }
    
    if (bUseAudioEffects)
    {
        SetupAudioEffects(TransitionData.TransitionID);
    }
    
    // Atualizar navegação se necessário
    if (bUse3DNavigation)
    {
        UpdateNavigationForTransition(TransitionData);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ponto de transição %s criado com sucesso"), *TransitionData.TransitionID);
    return true;
}

bool UAuracronVerticalTransitionsBridge::RemoveTransitionPoint(const FString& TransitionID)
{
    if (TransitionID.IsEmpty())
    {
        return false;
    }
    
    FScopeLock Lock(&TransitionMutex);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removendo ponto de transição %s"), *TransitionID);
    
    // Encontrar e remover da rede
    int32 RemovedCount = TransitionNetwork.TransitionPoints.RemoveAll([&TransitionID](const FTransitionPointData& Transition)
    {
        return Transition.TransitionID == TransitionID;
    });
    
    if (RemovedCount > 0)
    {
        TransitionNetwork.TotalTransitions -= RemovedCount;
        
        // Remover componentes visuais
        if (UStaticMeshComponent* Component = TransitionComponents.FindRef(TransitionID))
        {
            Component->DestroyComponent();
            TransitionComponents.Remove(TransitionID);
        }
        
        // Remover componentes de efeitos
        if (UParticleSystemComponent* EffectComponent = EffectComponents.FindRef(TransitionID))
        {
            EffectComponent->DestroyComponent();
            EffectComponents.Remove(TransitionID);
        }
        
        // Remover componentes de áudio
        if (UAudioComponent* AudioComponent = AudioComponents.FindRef(TransitionID))
        {
            AudioComponent->Stop();
            AudioComponent->DestroyComponent();
            AudioComponents.Remove(TransitionID);
        }
        
        // Remover cooldown
        TransitionCooldowns.Remove(TransitionID);
        
        // Cancelar channeling se ativo
        for (auto It = PlayersChanneling.CreateIterator(); It; ++It)
        {
            if (It.Value() == TransitionID)
            {
                CancelTransitionChanneling(It.Key());
                It.RemoveCurrent();
            }
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Ponto de transição %s removido"), *TransitionID);
        return true;
    }
    
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ponto de transição %s não encontrado"), *TransitionID);
    return false;
}

bool UAuracronVerticalTransitionsBridge::ActivateTransition(const FString& TransitionID, APawn* Player)
{
    if (!Player || TransitionID.IsEmpty())
    {
        return false;
    }
    
    // Encontrar dados da transição
    FTransitionPointData* TransitionData = FindTransitionData(TransitionID);
    if (!TransitionData)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s não encontrada"), *TransitionID);
        return false;
    }
    
    // Verificar se está ativa
    if (!TransitionData->bIsActive)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s está desativada"), *TransitionID);
        return false;
    }
    
    // Verificar cooldown
    if (IsTransitionOnCooldown(TransitionID))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transição %s está em cooldown"), *TransitionID);
        return false;
    }
    
    // Verificar se jogador pode usar
    if (!CanPlayerUseTransition(TransitionID, Player))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Jogador não pode usar transição %s"), *TransitionID);
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Ativando transição %s para jogador"), *TransitionID);
    
    // Verificar se requer channeling
    if (TransitionData->Properties.bRequiresChannel)
    {
        return StartTransitionChanneling(TransitionID, Player);
    }
    else
    {
        return ExecuteTransition(TransitionID, Player);
    }
}

bool UAuracronVerticalTransitionsBridge::ExecuteTransition(const FString& TransitionID, APawn* Player)
{
    if (!Player || TransitionID.IsEmpty())
    {
        return false;
    }
    
    FTransitionPointData* TransitionData = FindTransitionData(TransitionID);
    if (!TransitionData)
    {
        return false;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Executando transição %s"), *TransitionID);
    
    // Reproduzir efeito de ativação
    PlayTransitionEffect(TransitionID, true);
    
    // Calcular posição de destino
    FVector DestinationLocation = TransitionData->DestinationLocation;
    FRotator DestinationRotation = TransitionData->DestinationRotation;
    
    // Ajustar posição baseado no tipo de transição
    AdjustDestinationForTransitionType(TransitionData->TransitionType, DestinationLocation, DestinationRotation);
    
    // Executar transição baseado no tipo
    bool bSuccess = false;
    switch (TransitionData->TransitionType)
    {
        case EAuracronVerticalTransitionType::Portal:
            bSuccess = ExecuteTeleportTransition(Player, DestinationLocation, DestinationRotation);
            break;

        case EAuracronVerticalTransitionType::Elevator:
        case EAuracronVerticalTransitionType::FloatingPlatform:
            bSuccess = ExecuteElevatorTransition(Player, DestinationLocation, TransitionData->Properties.TransitionSpeed);
            break;
            
        case EAuracronVerticalTransitionType::JumpPad:
            bSuccess = ExecuteJumpPadTransition(Player, DestinationLocation, TransitionData->Properties.TransitionSpeed);
            break;

        case EAuracronVerticalTransitionType::WindCurrent:
            bSuccess = ExecuteWindCurrentTransition(Player, DestinationLocation, TransitionData->Properties.TransitionSpeed);
            break;

        case EAuracronVerticalTransitionType::Stairs:
        case EAuracronVerticalTransitionType::CaveEntrance:
            bSuccess = ExecuteWalkTransition(Player, DestinationLocation);
            break;
            
        default:
            bSuccess = ExecuteTeleportTransition(Player, DestinationLocation, DestinationRotation);
            break;
    }
    
    if (bSuccess)
    {
        // Aplicar cooldown
        TransitionCooldowns.Add(TransitionID, TransitionData->Properties.CooldownTime);
        
        // Reproduzir efeito de chegada
        PlayTransitionEffect(TransitionID, false);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Transição %s executada com sucesso"), *TransitionID);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao executar transição %s"), *TransitionID);
    }
    
    return bSuccess;
}

// === Internal Helper Functions ===

void UAuracronVerticalTransitionsBridge::UpdateTransitionCooldowns(float DeltaTime)
{
    FScopeLock Lock(&TransitionMutex);
    
    // Atualizar cooldowns
    for (auto& CooldownPair : TransitionCooldowns)
    {
        if (CooldownPair.Value > 0.0f)
        {
            CooldownPair.Value -= DeltaTime;
            if (CooldownPair.Value <= 0.0f)
            {
                CooldownPair.Value = 0.0f;
                UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Cooldown da transição %s finalizado"), *CooldownPair.Key);
            }
        }
    }
}

void UAuracronVerticalTransitionsBridge::ProcessActiveChannelings(float DeltaTime)
{
    FScopeLock Lock(&TransitionMutex);
    
    TArray<APawn*> PlayersToRemove;
    
    // Processar channelings ativos
    for (auto& ChannelingPair : PlayersChanneling)
    {
        APawn* Player = ChannelingPair.Key;
        const FString& TransitionID = ChannelingPair.Value;
        
        if (!IsValid(Player))
        {
            PlayersToRemove.Add(Player);
            continue;
        }
        
        // Verificar se o jogador ainda está na área de transição
        // Implementação básica - pode ser expandida
        UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Processando channeling do jogador para transição %s"), *TransitionID);
    }
    
    // Remover jogadores inválidos
    for (APawn* Player : PlayersToRemove)
    {
        PlayersChanneling.Remove(Player);
    }
}

void UAuracronVerticalTransitionsBridge::DetectPlayersNearTransitions()
{
    if (!GetWorld())
    {
        return;
    }
    
    FScopeLock Lock(&TransitionMutex);
    
    // Obter todos os jogadores no mundo
    for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!IsValid(PC) || !IsValid(PC->GetPawn()))
        {
            continue;
        }
        
        APawn* Player = PC->GetPawn();
        FVector PlayerLocation = Player->GetActorLocation();
        
        // Verificar proximidade com pontos de transição
        for (const auto& TransitionPair : TransitionPoints)
        {
            const FTransitionPointData& TransitionData = TransitionPair.Value;
            float Distance = FVector::Dist(PlayerLocation, TransitionData.Location);
            
            if (Distance <= TransitionData.TriggerRadius)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Jogador próximo à transição %s (distância: %.2f)"), *TransitionPair.Key, Distance);
                // Aqui pode ser adicionada lógica adicional para notificar o jogador
            }
        }
    }
}

void UAuracronVerticalTransitionsBridge::InitializeDefaultTransitionProperties()
{
    // Initialize default transition properties using UE 5.6 initialization system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing default transition properties"));

    // Setup default transition types
    DefaultTransitionProperties.Add(EAuracronVerticalTransitionType::TeleportCircle, FTransitionProperties());
    DefaultTransitionProperties.Add(EAuracronVerticalTransitionType::Elevator, FTransitionProperties());
    DefaultTransitionProperties.Add(EAuracronVerticalTransitionType::JumpPad, FTransitionProperties());
    DefaultTransitionProperties.Add(EAuracronVerticalTransitionType::WindCurrent, FTransitionProperties());
    DefaultTransitionProperties.Add(EAuracronVerticalTransitionType::Portal, FTransitionProperties());

    // Configure teleport properties
    FTransitionProperties& TeleportProps = DefaultTransitionProperties[EAuracronVerticalTransitionType::TeleportCircle];
    TeleportProps.TransitionSpeed = 0.0f; // Instant
    TeleportProps.EnergyConsumption = 50.0f;
    TeleportProps.CooldownTime = 5.0f;
    TeleportProps.RequiredLevel = 1;

    // Configure elevator properties
    FTransitionProperties& ElevatorProps = DefaultTransitionProperties[EAuracronVerticalTransitionType::Elevator];
    ElevatorProps.TransitionSpeed = 300.0f;
    ElevatorProps.EnergyConsumption = 10.0f;
    ElevatorProps.CooldownTime = 2.0f;
    ElevatorProps.RequiredLevel = 1;

    // Configure jump pad properties
    FTransitionProperties& JumpPadProps = DefaultTransitionProperties[EAuracronVerticalTransitionType::JumpPad];
    JumpPadProps.TransitionSpeed = 800.0f;
    JumpPadProps.EnergyConsumption = 20.0f;
    JumpPadProps.CooldownTime = 3.0f;
    JumpPadProps.RequiredLevel = 2;

    // Configure wind current properties
    FTransitionProperties& WindProps = DefaultTransitionProperties[EAuracronVerticalTransitionType::WindCurrent];
    WindProps.TransitionSpeed = 400.0f;
    WindProps.EnergyConsumption = 15.0f;
    WindProps.CooldownTime = 1.0f;
    WindProps.RequiredLevel = 3;

    // Configure walk properties
    FTransitionProperties& PortalProps = DefaultTransitionProperties[EAuracronVerticalTransitionType::Portal];
    PortalProps.TransitionSpeed = 0.0f; // Instant
    PortalProps.EnergyConsumption = 25.0f;
    PortalProps.CooldownTime = 2.0f;
    PortalProps.RequiredLevel = 1;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Default transition properties initialized"));
}

bool UAuracronVerticalTransitionsBridge::ValidateSystemConfiguration()
{
    // Validate system configuration using UE 5.6 validation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Validating system configuration"));

    // Check if world is valid
    if (!GetWorld())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: World is not valid"));
        return false;
    }

    // Check if owner is valid
    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Owner is not valid"));
        return false;
    }

    // Validate configuration parameters
    if (TransitionDetectionRadius <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid transition detection radius: %.2f"), TransitionDetectionRadius);
        return false;
    }

    if (MaxChannelingTime <= 0.0f)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Invalid max channeling time: %.2f"), MaxChannelingTime);
        return false;
    }

    // Check if default transition properties are initialized
    if (DefaultTransitionProperties.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Default transition properties not initialized"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: System configuration validated successfully"));
    return true;
}

bool UAuracronVerticalTransitionsBridge::InitializeTransitionSystem()
{
    // Initialize transition system using UE 5.6 initialization system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing transition system"));

    try
    {
        // Initialize transition points map
        TransitionPoints.Empty();

        // Initialize active transitions map
        ActiveTransitions.Empty();

        // Initialize transition cooldowns
        TransitionCooldowns.Empty();

        // Initialize transition statistics
        TransitionStatistics.Empty();

        // Setup transition network
        if (!SetupTransitionNetwork())
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to setup transition network"));
            return false;
        }

        // Initialize visual and audio components if enabled
        if (bUseVisualEffects)
        {
            InitializeVisualEffects();
        }

        if (bUseAudioEffects)
        {
            InitializeAudioEffects();
        }

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition system initialized successfully"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Exception during transition system initialization: %hs"), e.what());
        return false;
    }
}

bool UAuracronVerticalTransitionsBridge::SetupTransitionNetwork()
{
    // Setup transition network using UE 5.6 network system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up transition network"));

    // Initialize network connections
    TransitionNetworkConnections.Empty();

    // Create default network topology
    // This would typically be loaded from configuration or generated procedurally

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition network setup complete"));
    return true;
}

void UAuracronVerticalTransitionsBridge::InitializeVisualEffects()
{
    // Initialize visual effects using UE 5.6 VFX system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing visual effects"));

    // Clear existing effect components
    for (auto& EffectPair : EffectComponents)
    {
        if (EffectPair.Value)
        {
            EffectPair.Value->DestroyComponent();
        }
    }
    EffectComponents.Empty();

    // Initialize default visual effects for each transition type
    // This would typically load particle systems and materials

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Visual effects initialized"));
}

void UAuracronVerticalTransitionsBridge::InitializeAudioEffects()
{
    // Initialize audio effects using UE 5.6 audio system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing audio effects"));

    // Clear existing audio components
    for (auto& AudioPair : AudioComponents)
    {
        if (AudioPair.Value)
        {
            AudioPair.Value->DestroyComponent();
        }
    }
    AudioComponents.Empty();

    // Initialize default audio effects for each transition type
    // This would typically load sound cues and audio components

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Audio effects initialized"));
}

void UAuracronVerticalTransitionsBridge::UpdateTransitionSystem()
{
    // Update transition system using UE 5.6 update system
    if (!bSystemInitialized)
    {
        return;
    }

    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    float DeltaTime = CurrentTime - LastPlayerDetectionTime;
    LastPlayerDetectionTime = CurrentTime;

    // Update transition cooldowns
    UpdateTransitionCooldowns(DeltaTime);

    // Process active channelings
    ProcessActiveChannelings(DeltaTime);

    // Detect players near transitions
    DetectPlayersNearTransitions();

    // Update transition statistics
    UpdateTransitionStatistics();
}

void UAuracronVerticalTransitionsBridge::CreateTransitionVisualComponent(const FTransitionPointData& TransitionData)
{
    // Create visual component for transition using UE 5.6 component system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creating visual component for transition %s"), *TransitionData.TransitionID);

    if (!GetOwner())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Cannot create visual component - no owner"));
        return;
    }

    // Create static mesh component
    UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(GetOwner());
    if (MeshComponent)
    {
        MeshComponent->AttachToComponent(GetOwner()->GetRootComponent(),
            FAttachmentTransformRules::KeepWorldTransform);

        // Set mesh if available
        if (TransitionData.TransitionMesh.IsValid())
        {
            MeshComponent->SetStaticMesh(TransitionData.TransitionMesh.Get());
        }

        // Set location
        MeshComponent->SetWorldLocation(TransitionData.Location);

        // Store component reference
        TransitionComponents.Add(TransitionData.TransitionID, MeshComponent);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Visual component created for transition %s"), *TransitionData.TransitionID);
    }
}

void UAuracronVerticalTransitionsBridge::UpdateNavigationForTransition(const FTransitionPointData& TransitionData)
{
    // Update navigation for transition using UE 5.6 navigation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Updating navigation for transition %s"), *TransitionData.TransitionID);

    // This would typically update the navigation mesh or add navigation links
    // For now, we'll just log the operation

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Navigation updated for transition %s"), *TransitionData.TransitionID);
}

void UAuracronVerticalTransitionsBridge::UpdateTransitionStatistics()
{
    // Update transition statistics using UE 5.6 statistics system
    // This would typically track usage patterns, performance metrics, etc.

    // Update total usage count
    int32 TotalUsage = 0;
    for (const auto& StatPair : TransitionStatistics)
    {
        TotalUsage += StatPair.Value;
    }

    // Log statistics periodically
    static float LastStatsLog = 0.0f;
    float CurrentTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    if (CurrentTime - LastStatsLog > 60.0f) // Log every minute
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition Statistics - Total Points: %d, Total Usage: %d"),
            TransitionPoints.Num(), TotalUsage);
        LastStatsLog = CurrentTime;
    }
}

// === Helper Method Implementations ===

FTransitionPointData* UAuracronVerticalTransitionsBridge::FindTransitionData(const FString& TransitionID)
{
    return TransitionPoints.Find(TransitionID);
}

bool UAuracronVerticalTransitionsBridge::IsTransitionOnCooldown(const FString& TransitionID) const
{
    const float* CooldownTime = TransitionCooldowns.Find(TransitionID);
    if (CooldownTime)
    {
        float CurrentTime = GetWorld()->GetTimeSeconds();
        return CurrentTime < *CooldownTime;
    }
    return false;
}

void UAuracronVerticalTransitionsBridge::AdjustDestinationForTransitionType(EAuracronVerticalTransitionType TransitionType, FVector& DestinationLocation, FRotator& DestinationRotation)
{
    switch (TransitionType)
    {
        case EAuracronVerticalTransitionType::TeleportCircle:
            // Teleport é instantâneo, sem ajustes necessários
            break;
        case EAuracronVerticalTransitionType::Elevator:
            // Ajustar para posição de entrada do elevador
            DestinationLocation.Z += 50.0f; // Offset para entrada
            break;
        case EAuracronVerticalTransitionType::JumpPad:
            // Ajustar para trajetória de salto
            DestinationLocation.Z += 100.0f; // Altura do salto
            break;
        case EAuracronVerticalTransitionType::WindCurrent:
            // Ajustar para corrente de vento
            DestinationLocation.Z += 25.0f; // Leve elevação
            break;
        case EAuracronVerticalTransitionType::Portal:
            // Portal instantâneo, sem ajustes
            break;
        default:
            break;
    }
}

bool UAuracronVerticalTransitionsBridge::ExecuteTeleportTransition(APawn* Player, const FVector& DestinationLocation, const FRotator& DestinationRotation)
{
    if (!IsValid(Player))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid player for teleport transition"));
        return false;
    }

    // Teleporte instantâneo
    Player->SetActorLocation(DestinationLocation, false, nullptr, ETeleportType::TeleportPhysics);
    Player->SetActorRotation(DestinationRotation, ETeleportType::TeleportPhysics);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player teleported to %s"), *DestinationLocation.ToString());
    return true;
}

bool UAuracronVerticalTransitionsBridge::ExecuteElevatorTransition(APawn* Player, const FVector& DestinationLocation, float TransitionSpeed)
{
    if (!IsValid(Player))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid player for elevator transition"));
        return false;
    }

    // Implementação de elevador - movimento suave
    FVector CurrentLocation = Player->GetActorLocation();
    FVector Direction = (DestinationLocation - CurrentLocation).GetSafeNormal();

    // Aplicar movimento gradual (em produção, usar timeline ou interpolação)
    FVector NewLocation = FMath::VInterpTo(CurrentLocation, DestinationLocation, GetWorld()->GetDeltaSeconds(), TransitionSpeed);
    Player->SetActorLocation(NewLocation, true);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Elevator transition in progress for player"));
    return true;
}

bool UAuracronVerticalTransitionsBridge::ExecuteJumpPadTransition(APawn* Player, const FVector& DestinationLocation, float TransitionSpeed)
{
    if (!IsValid(Player))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid player for jump pad transition"));
        return false;
    }

    // Aplicar impulso de salto
    if (UPrimitiveComponent* RootComponent = Cast<UPrimitiveComponent>(Player->GetRootComponent()))
    {
        FVector LaunchVelocity = (DestinationLocation - Player->GetActorLocation()).GetSafeNormal() * TransitionSpeed;
        LaunchVelocity.Z = FMath::Max(LaunchVelocity.Z, 800.0f); // Garantir altura mínima

        RootComponent->SetPhysicsLinearVelocity(LaunchVelocity);
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Jump pad launched player with velocity %s"), *LaunchVelocity.ToString());
        return true;
    }

    return false;
}

bool UAuracronVerticalTransitionsBridge::ExecuteWindCurrentTransition(APawn* Player, const FVector& DestinationLocation, float TransitionSpeed)
{
    if (!IsValid(Player))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid player for wind current transition"));
        return false;
    }

    // Aplicar força de vento gradual
    if (UPrimitiveComponent* RootComponent = Cast<UPrimitiveComponent>(Player->GetRootComponent()))
    {
        FVector WindForce = (DestinationLocation - Player->GetActorLocation()).GetSafeNormal() * TransitionSpeed * 100.0f;
        RootComponent->AddForce(WindForce);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Wind current applied force %s to player"), *WindForce.ToString());
        return true;
    }

    return false;
}

bool UAuracronVerticalTransitionsBridge::ExecuteWalkTransition(APawn* Player, const FVector& DestinationLocation)
{
    if (!IsValid(Player))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Invalid player for walk transition"));
        return false;
    }

    // Transição de caminhada - movimento direto
    Player->SetActorLocation(DestinationLocation, true);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Player walked to %s"), *DestinationLocation.ToString());
    return true;
}

// === Missing Method Implementations ===

bool UAuracronVerticalTransitionsBridge::DeactivateTransition(const FString& TransitionID)
{
    if (TransitionID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot deactivate transition - empty ID"));
        return false;
    }

    FScopeLock Lock(&TransitionMutex);

    FTransitionPointData* TransitionData = FindTransitionData(TransitionID);
    if (!TransitionData)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transition %s not found for deactivation"), *TransitionID);
        return false;
    }

    TransitionData->bIsActive = false;
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition %s deactivated"), *TransitionID);

    // Cancel any active channelings for this transition
    for (auto It = PlayersChanneling.CreateIterator(); It; ++It)
    {
        if (It.Value() == TransitionID)
        {
            CancelTransitionChanneling(It.Key());
            It.RemoveCurrent();
        }
    }

    return true;
}

bool UAuracronVerticalTransitionsBridge::BuildTransitionNetwork()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Building transition network"));

    FScopeLock Lock(&TransitionMutex);

    // Clear existing network
    TransitionNetwork.TransitionPoints.Empty();
    TransitionNetwork.TotalTransitions = 0;
    TransitionNetwork.ActiveConnections = 0;

    // Build network from existing transition points
    for (const auto& TransitionPair : TransitionPoints)
    {
        TransitionNetwork.TransitionPoints.Add(TransitionPair.Value);
        TransitionNetwork.TotalTransitions++;

        if (TransitionPair.Value.bIsActive)
        {
            TransitionNetwork.ActiveConnections++;
        }
    }

    // Validate network connectivity
    bool bNetworkValid = ValidateNetworkConnectivity();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition network built - %d points, %d active, valid: %s"),
        TransitionNetwork.TotalTransitions, TransitionNetwork.ActiveConnections,
        bNetworkValid ? TEXT("Yes") : TEXT("No"));

    return bNetworkValid;
}

bool UAuracronVerticalTransitionsBridge::OptimizeTransitionNetwork()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Optimizing transition network"));

    FScopeLock Lock(&TransitionMutex);

    // Remove inactive transitions from optimization
    int32 OptimizedCount = 0;

    for (auto It = TransitionPoints.CreateIterator(); It; ++It)
    {
        FTransitionPointData& TransitionData = It.Value();

        // Optimize based on usage statistics
        const int32* UsageCount = TransitionStatistics.Find(TransitionData.TransitionID);
        if (UsageCount && *UsageCount == 0)
        {
            // Low usage transitions can be optimized
            TransitionData.Properties.CooldownTime *= 0.8f; // Reduce cooldown
            OptimizedCount++;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Network optimization complete - %d transitions optimized"), OptimizedCount);
    return true;
}

bool UAuracronVerticalTransitionsBridge::ValidateNetworkConnectivity()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Validating network connectivity"));

    FScopeLock Lock(&TransitionMutex);

    // Check if we have at least one active transition
    bool bHasActiveTransitions = false;
    for (const auto& TransitionPair : TransitionPoints)
    {
        if (TransitionPair.Value.bIsActive)
        {
            bHasActiveTransitions = true;
            break;
        }
    }

    if (!bHasActiveTransitions)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: No active transitions found in network"));
        return false;
    }

    // Validate transition destinations
    for (const auto& TransitionPair : TransitionPoints)
    {
        const FTransitionPointData& TransitionData = TransitionPair.Value;

        // Check if destination is valid
        if (TransitionData.DestinationLocation.IsZero())
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Transition %s has invalid destination"), *TransitionData.TransitionID);
            return false;
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Network connectivity validated successfully"));
    return true;
}

TArray<FString> UAuracronVerticalTransitionsBridge::FindPathBetweenRealms(ERealmType SourceRealm, ERealmType DestinationRealm)
{
    TArray<FString> Path;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Finding path from realm %d to realm %d"), (int32)SourceRealm, (int32)DestinationRealm);

    FScopeLock Lock(&TransitionMutex);

    // Simple pathfinding - find transitions that connect the realms
    for (const auto& TransitionPair : TransitionPoints)
    {
        const FTransitionPointData& TransitionData = TransitionPair.Value;

        // Check if this transition connects the desired realms
        if (TransitionData.SourceRealm == SourceRealm && TransitionData.DestinationRealm == DestinationRealm)
        {
            Path.Add(TransitionData.TransitionID);
        }
    }

    // If no direct path, try to find intermediate transitions
    if (Path.Num() == 0)
    {
        // Find intermediate realm connections
        for (const auto& TransitionPair : TransitionPoints)
        {
            const FTransitionPointData& TransitionData = TransitionPair.Value;

            if (TransitionData.SourceRealm == SourceRealm)
            {
                // Found a transition from source realm, add it to path
                Path.Add(TransitionData.TransitionID);

                // Look for connection from this destination to final destination
                for (const auto& SecondTransitionPair : TransitionPoints)
                {
                    const FTransitionPointData& SecondTransitionData = SecondTransitionPair.Value;

                    if (SecondTransitionData.SourceRealm == TransitionData.DestinationRealm &&
                        SecondTransitionData.DestinationRealm == DestinationRealm)
                    {
                        Path.Add(SecondTransitionData.TransitionID);
                        break;
                    }
                }

                if (Path.Num() > 1)
                {
                    break; // Found a valid path
                }
                else
                {
                    Path.Empty(); // Reset if no complete path found
                }
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Found path with %d transitions"), Path.Num());
    return Path;
}

bool UAuracronVerticalTransitionsBridge::CanPlayerUseTransition(const FString& TransitionID, APawn* Player) const
{
    if (!IsValid(Player) || TransitionID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&TransitionMutex);

    const FTransitionPointData* TransitionData = TransitionPoints.Find(TransitionID);
    if (!TransitionData)
    {
        return false;
    }

    // Check if transition is active
    if (!TransitionData->bIsActive)
    {
        return false;
    }

    // Check if player is close enough
    float Distance = FVector::Dist(Player->GetActorLocation(), TransitionData->Location);
    if (Distance > TransitionData->TriggerRadius)
    {
        return false;
    }

    // Check player level requirement
    // This would typically check player stats/level
    // For now, assume all players can use all transitions

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Player can use transition %s"), *TransitionID);
    return true;
}

TArray<FString> UAuracronVerticalTransitionsBridge::GetNearbyTransitions(const FVector& Location, float Radius) const
{
    TArray<FString> NearbyTransitions;

    FScopeLock Lock(&TransitionMutex);

    for (const auto& TransitionPair : TransitionPoints)
    {
        const FTransitionPointData& TransitionData = TransitionPair.Value;

        float Distance = FVector::Dist(Location, TransitionData.Location);
        if (Distance <= Radius && TransitionData.bIsActive)
        {
            NearbyTransitions.Add(TransitionData.TransitionID);
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Found %d nearby transitions"), NearbyTransitions.Num());
    return NearbyTransitions;
}

bool UAuracronVerticalTransitionsBridge::StartTransitionChanneling(const FString& TransitionID, APawn* Player)
{
    if (!IsValid(Player) || TransitionID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&TransitionMutex);

    // Check if player is already channeling
    if (PlayersChanneling.Contains(Player))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Player is already channeling a transition"));
        return false;
    }

    FTransitionPointData* TransitionData = FindTransitionData(TransitionID);
    if (!TransitionData)
    {
        return false;
    }

    // Start channeling
    PlayersChanneling.Add(Player, TransitionID);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Started channeling transition %s for player"), *TransitionID);

    // Set timer for channeling completion
    if (GetWorld())
    {
        FTimerHandle ChannelingTimer;
        FTimerDelegate ChannelingDelegate = FTimerDelegate::CreateUFunction(this, FName("CompleteChanneling"));

        GetWorld()->GetTimerManager().SetTimer(ChannelingTimer, ChannelingDelegate,
            TransitionData->Properties.ChannelingTime, false);
    }

    return true;
}

bool UAuracronVerticalTransitionsBridge::CancelTransitionChanneling(APawn* Player)
{
    if (!IsValid(Player))
    {
        return false;
    }

    FScopeLock Lock(&TransitionMutex);

    FString* TransitionID = PlayersChanneling.Find(Player);
    if (!TransitionID)
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Cancelled channeling transition %s for player"), **TransitionID);

    PlayersChanneling.Remove(Player);

    // Clear any timers associated with this channeling
    // This would typically clear the channeling timer

    return true;
}

bool UAuracronVerticalTransitionsBridge::PlayTransitionEffect(const FString& TransitionID, bool bActivation)
{
    if (TransitionID.IsEmpty())
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Playing %s effect for transition %s"),
        bActivation ? TEXT("activation") : TEXT("arrival"), *TransitionID);

    // Play visual effects
    if (bUseVisualEffects)
    {
        if (TObjectPtr<UParticleSystemComponent>* EffectComponent = EffectComponents.Find(TransitionID))
        {
            if (*EffectComponent)
            {
                (*EffectComponent)->Activate();
            }
        }
    }

    // Play audio effects
    if (bUseAudioEffects)
    {
        if (TObjectPtr<UAudioComponent>* AudioComponent = AudioComponents.Find(TransitionID))
        {
            if (*AudioComponent)
            {
                (*AudioComponent)->Play();
            }
        }
    }

    return true;
}

bool UAuracronVerticalTransitionsBridge::SetupVisualEffects(const FString& TransitionID)
{
    if (TransitionID.IsEmpty())
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up visual effects for transition %s"), *TransitionID);

    // This would typically create and configure particle system components
    // For now, we'll just log the setup

    return true;
}

bool UAuracronVerticalTransitionsBridge::SetupAudioEffects(const FString& TransitionID)
{
    if (TransitionID.IsEmpty())
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Setting up audio effects for transition %s"), *TransitionID);

    // This would typically create and configure audio components
    // For now, we'll just log the setup

    return true;
}

bool UAuracronVerticalTransitionsBridge::InitializePythonBindings()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing Python bindings for transition system"));

    // This would typically set up Python script bindings for the transition system
    // Using UE 5.6 Python integration

    try
    {
        // Initialize Python environment if not already done
        // Set up transition system Python API
        // Register callback functions

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Python bindings initialized successfully"));
        return true;
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to initialize Python bindings"));
        return false;
    }
}

bool UAuracronVerticalTransitionsBridge::ExecutePythonScript(const FString& ScriptPath)
{
    if (ScriptPath.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Cannot execute Python script - empty path"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Executing Python script: %s"), *ScriptPath);

    // This would typically execute a Python script using UE 5.6 Python integration
    // The script could modify transition properties, generate new transitions, etc.

    try
    {
        // Execute Python script
        // Pass transition system data to script
        // Process script results

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Python script executed successfully"));
        return true;
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Failed to execute Python script: %s"), *ScriptPath);
        return false;
    }
}

FString UAuracronVerticalTransitionsBridge::GetTransitionDataForPython() const
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating transition data for Python"));

    FScopeLock Lock(&TransitionMutex);

    // Generate JSON string with transition data for Python scripts
    FString JsonData = TEXT("{");
    JsonData += TEXT("\"transitions\": [");

    bool bFirst = true;
    for (const auto& TransitionPair : TransitionPoints)
    {
        const FTransitionPointData& TransitionData = TransitionPair.Value;

        if (!bFirst)
        {
            JsonData += TEXT(",");
        }
        bFirst = false;

        JsonData += FString::Printf(TEXT("{\"id\":\"%s\",\"active\":%s,\"location\":[%.2f,%.2f,%.2f]}"),
            *TransitionData.TransitionID,
            TransitionData.bIsActive ? TEXT("true") : TEXT("false"),
            TransitionData.Location.X,
            TransitionData.Location.Y,
            TransitionData.Location.Z);
    }

    JsonData += TEXT("]}");

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Generated transition data JSON"));
    return JsonData;
}

FString UAuracronVerticalTransitionsBridge::GetNetworkStatistics() const
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Generating network statistics"));

    FScopeLock Lock(&TransitionMutex);

    // Calculate statistics
    int32 TotalTransitions = TransitionPoints.Num();
    int32 ActiveTransitionsCount = 0;
    int32 TotalUsage = 0;

    for (const auto& TransitionPair : TransitionPoints)
    {
        if (TransitionPair.Value.bIsActive)
        {
            ActiveTransitionsCount++;
        }

        const int32* UsageCount = TransitionStatistics.Find(TransitionPair.Key);
        if (UsageCount)
        {
            TotalUsage += *UsageCount;
        }
    }

    // Generate statistics string
    FString Stats = FString::Printf(TEXT("Network Statistics:\n"));
    Stats += FString::Printf(TEXT("Total Transitions: %d\n"), TotalTransitions);
    Stats += FString::Printf(TEXT("Active Transitions: %d\n"), ActiveTransitionsCount);
    Stats += FString::Printf(TEXT("Total Usage: %d\n"), TotalUsage);
    Stats += FString::Printf(TEXT("Average Usage: %.2f\n"), TotalTransitions > 0 ? (float)TotalUsage / TotalTransitions : 0.0f);
    Stats += FString::Printf(TEXT("Active Players Channeling: %d\n"), PlayersChanneling.Num());

    return Stats;
}

bool UAuracronVerticalTransitionsBridge::ValidateNetworkIntegrity() const
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Validating network integrity"));

    FScopeLock Lock(&TransitionMutex);

    // Check for orphaned transitions
    for (const auto& TransitionPair : TransitionPoints)
    {
        const FTransitionPointData& TransitionData = TransitionPair.Value;

        // Validate transition data integrity
        if (TransitionData.TransitionID.IsEmpty())
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Found transition with empty ID"));
            return false;
        }

        if (TransitionData.Location.IsZero() && TransitionData.DestinationLocation.IsZero())
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Transition %s has invalid locations"), *TransitionData.TransitionID);
            return false;
        }

        if (TransitionData.TriggerRadius <= 0.0f)
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Transition %s has invalid trigger radius"), *TransitionData.TransitionID);
            return false;
        }
    }

    // Check for duplicate IDs
    TSet<FString> UniqueIDs;
    for (const auto& TransitionPair : TransitionPoints)
    {
        if (UniqueIDs.Contains(TransitionPair.Key))
        {
            UE_LOG(LogTemp, Error, TEXT("AURACRON: Duplicate transition ID found: %s"), *TransitionPair.Key);
            return false;
        }
        UniqueIDs.Add(TransitionPair.Key);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Network integrity validated successfully"));
    return true;
}

void UAuracronVerticalTransitionsBridge::ClearTransitionNetwork()
{
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Clearing transition network"));

    FScopeLock Lock(&TransitionMutex);

    // Cancel all active channelings
    for (auto& ChannelingPair : PlayersChanneling)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Cancelling channeling for player"));
    }
    PlayersChanneling.Empty();

    // Clear all transition data
    TransitionPoints.Empty();
    TransitionCooldowns.Empty();
    TransitionStatistics.Empty();

    // Destroy visual components
    for (auto& ComponentPair : TransitionComponents)
    {
        if (ComponentPair.Value)
        {
            ComponentPair.Value->DestroyComponent();
        }
    }
    TransitionComponents.Empty();

    // Destroy effect components
    for (auto& EffectPair : EffectComponents)
    {
        if (EffectPair.Value)
        {
            EffectPair.Value->DestroyComponent();
        }
    }
    EffectComponents.Empty();

    // Destroy audio components
    for (auto& AudioPair : AudioComponents)
    {
        if (AudioPair.Value)
        {
            AudioPair.Value->Stop();
            AudioPair.Value->DestroyComponent();
        }
    }
    AudioComponents.Empty();

    // Clear network data
    TransitionNetwork.TransitionPoints.Empty();
    TransitionNetwork.TotalTransitions = 0;
    TransitionNetwork.ActiveConnections = 0;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Transition network cleared"));
}

// === Helper Method for Channeling Completion ===

UFUNCTION()
void UAuracronVerticalTransitionsBridge::CompleteChanneling(APawn* Player, const FString& TransitionID)
{
    if (!IsValid(Player) || TransitionID.IsEmpty())
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Completing channeling for transition %s"), *TransitionID);

    // Remove from channeling list
    PlayersChanneling.Remove(Player);

    // Execute the transition
    ExecuteTransition(TransitionID, Player);
}
