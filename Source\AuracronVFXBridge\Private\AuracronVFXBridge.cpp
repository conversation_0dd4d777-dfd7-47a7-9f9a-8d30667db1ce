// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Partículas e Efeitos Visuais Bridge Implementation

#include "AuracronVFXBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "NiagaraSystem.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Async/Async.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "Kismet/GameplayStatics.h"

UAuracronVFXBridge::UAuracronVFXBridge(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.033f; // 30 FPS para VFX responsivo
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Configurações padrão
    CurrentVFXQuality = EAuracronVFXQuality::High;
    
    RealmVFXConfiguration.AmbientEffectDensity = 1.0f;
    RealmVFXConfiguration.bUseWeatherEffects = true;
    RealmVFXConfiguration.WeatherIntensity = 1.0f;
    RealmVFXConfiguration.bUseVolumetricFog = true;
    RealmVFXConfiguration.FogDensity = 0.3f;
    
    // Cores de fog por realm
    RealmVFXConfiguration.RealmFogColors.Add(FLinearColor(0.8f, 0.9f, 1.0f, 1.0f)); // Planície - azul claro
    RealmVFXConfiguration.RealmFogColors.Add(FLinearColor(1.0f, 0.9f, 0.7f, 1.0f)); // Firmamento - dourado
    RealmVFXConfiguration.RealmFogColors.Add(FLinearColor(0.6f, 0.3f, 0.8f, 1.0f)); // Abismo - roxo escuro
}

void UAuracronVFXBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de VFX Niagara"));

    // Inicializar sistema
    bSystemInitialized = InitializeVFXSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers
        GetWorld()->GetTimerManager().SetTimer(
            CleanupTimer,
            [this]()
            {
                CleanupInactiveVFX();
            },
            5.0f, // A cada 5 segundos
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            OptimizationTimer,
            [this]()
            {
                if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
                {
                    if (APawn* Pawn = PC->GetPawn())
                    {
                        OptimizeVFXByDistance(Pawn->GetActorLocation());
                    }
                }
            },
            1.0f, // A cada segundo
            true
        );
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de VFX Niagara inicializado com sucesso"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de VFX Niagara"));
    }
}

void UAuracronVFXBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Parar todos os efeitos
    PauseAllVFXEffects();
    
    // Limpar componentes ativos
    for (UNiagaraComponent* Component : ActiveVFXComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    ActiveVFXComponents.Empty();
    
    // Limpar pool
    for (UNiagaraComponent* Component : NiagaraComponentPool)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }
    NiagaraComponentPool.Empty();
    
    // Limpar materiais dinâmicos
    ActiveDynamicMaterials.Empty();

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(CleanupTimer);
        GetWorld()->GetTimerManager().ClearTimer(OptimizationTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronVFXBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronVFXBridge, CurrentVFXQuality);
}

void UAuracronVFXBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized)
        return;

    // Processar efeitos ativos
    ProcessActiveVFX(DeltaTime);
}

// === Core VFX Management ===

UNiagaraComponent* UAuracronVFXBridge::SpawnNiagaraEffect(const FAuracronVFXConfiguration& VFXConfig, const FVector& Location, const FRotator& Rotation)
{
    if (!bSystemInitialized || !VFXConfig.NiagaraSystem.IsValid())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado ou Niagara System inválido"));
        return nullptr;
    }

    if (!ValidateVFXConfiguration(VFXConfig))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de VFX inválida"));
        return nullptr;
    }

    // Carregar sistema Niagara se necessário
    UNiagaraSystem* NiagaraSystem = VFXConfig.NiagaraSystem.LoadSynchronous();
    if (!NiagaraSystem)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao carregar Niagara System"));
        return nullptr;
    }

    // Obter componente do pool ou criar novo
    UNiagaraComponent* NiagaraComponent = GetPooledNiagaraComponent();
    if (!NiagaraComponent)
    {
        NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            NiagaraSystem,
            Location,
            Rotation
        );
    }
    else
    {
        NiagaraComponent->SetAsset(NiagaraSystem);
        NiagaraComponent->SetWorldLocationAndRotation(Location, Rotation);
        NiagaraComponent->Activate();
    }

    if (NiagaraComponent)
    {
        // Aplicar configurações usando FString (API UE 5.6)
        NiagaraComponent->SetVariableFloat(FName("User.Scale"), VFXConfig.Scale.X);
        NiagaraComponent->SetVariableLinearColor(FName("User.Color"), VFXConfig.Color);
        NiagaraComponent->SetVariableFloat(FName("User.Intensity"), VFXConfig.Intensity);
        NiagaraComponent->SetVariableFloat(FName("User.Speed"), VFXConfig.Speed);

        // Aplicar parâmetros customizados
        for (const auto& Parameter : VFXConfig.CustomParameters)
        {
            NiagaraComponent->SetVariableFloat(FName(*Parameter.Key), Parameter.Value);
        }

        for (const auto& VectorParam : VFXConfig.VectorParameters)
        {
            NiagaraComponent->SetVariableVec3(FName(*VectorParam.Key), VectorParam.Value);
        }

        for (const auto& ColorParam : VFXConfig.ColorParameters)
        {
            NiagaraComponent->SetVariableLinearColor(FName(*ColorParam.Key), ColorParam.Value);
        }

        // Configurar duração
        if (VFXConfig.Duration > 0.0f)
        {
            FTimerHandle TempHandle;
            GetWorld()->GetTimerManager().SetTimer(
                TempHandle,
                [this, NiagaraComponent]()
                {
                    if (IsValid(NiagaraComponent))
                    {
                        StopVFXEffect(NiagaraComponent);
                    }
                },
                VFXConfig.Duration,
                false
            );
        }

        // Adicionar à lista de ativos
        ActiveVFXComponents.Add(NiagaraComponent);

        UE_LOG(LogTemp, Log, TEXT("AURACRON: Efeito Niagara spawnado em %s"), *Location.ToString());

        // Broadcast evento
        OnVFXSpawned.Broadcast(NiagaraComponent, VFXConfig);

        return NiagaraComponent;
    }

    return nullptr;
}

UNiagaraComponent* UAuracronVFXBridge::SpawnAttachedNiagaraEffect(const FAuracronVFXConfiguration& VFXConfig, AActor* AttachActor, const FName& SocketName)
{
    if (!AttachActor)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Ator para anexar é inválido"));
        return nullptr;
    }

    UNiagaraComponent* NiagaraComponent = SpawnNiagaraEffect(VFXConfig, AttachActor->GetActorLocation(), AttachActor->GetActorRotation());
    
    if (NiagaraComponent)
    {
        // Anexar ao ator
        if (USceneComponent* RootComponent = AttachActor->GetRootComponent())
        {
            NiagaraComponent->AttachToComponent(
                RootComponent,
                FAttachmentTransformRules::KeepWorldTransform,
                SocketName
            );

            // Aplicar offsets
            NiagaraComponent->SetRelativeLocation(VFXConfig.PositionOffset);
            NiagaraComponent->SetRelativeRotation(VFXConfig.RotationOffset);

            UE_LOG(LogTemp, Log, TEXT("AURACRON: Efeito Niagara anexado a %s"), *AttachActor->GetName());
        }
    }

    return NiagaraComponent;
}

bool UAuracronVFXBridge::StopVFXEffect(UNiagaraComponent* EffectComponent)
{
    if (!EffectComponent || !IsValid(EffectComponent))
    {
        return false;
    }

    EffectComponent->Deactivate();
    
    // Remover da lista de ativos
    ActiveVFXComponents.Remove(EffectComponent);
    
    // Retornar ao pool se estiver usando pooling
    if (RealmVFXConfiguration.AmbientEffectDensity > 0.0f) // Usando como flag de pooling
    {
        ReturnComponentToPool(EffectComponent);
    }
    else
    {
        EffectComponent->DestroyComponent();
    }

    return true;
}

bool UAuracronVFXBridge::PlayRealmTransitionVFX(int32 FromRealm, int32 ToRealm, const FVector& Location)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    // Configurar efeito de transição baseado nos realms
    FAuracronVFXConfiguration TransitionConfig;
    TransitionConfig.VFXType = EAuracronVFXType::Environmental;
    TransitionConfig.Scale = FVector(2.0f, 2.0f, 2.0f);
    TransitionConfig.Intensity = 1.5f;
    TransitionConfig.Duration = 3.0f;

    // Carregar sistema de transição específico
    FString TransitionPath = FString::Printf(TEXT("/Game/VFX/Realms/Transitions/NS_RealmTransition_%d_to_%d"), FromRealm, ToRealm);
    UNiagaraSystem* TransitionSystem = LoadObject<UNiagaraSystem>(nullptr, *TransitionPath);
    
    if (!TransitionSystem)
    {
        // Fallback para transição genérica
        TransitionPath = TEXT("/Game/VFX/Realms/Transitions/NS_GenericRealmTransition");
        TransitionSystem = LoadObject<UNiagaraSystem>(nullptr, *TransitionPath);
    }

    if (!TransitionSystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Sistema de transição não encontrado"));
        return false;
    }

    TransitionConfig.NiagaraSystem = TransitionSystem;
    
    UNiagaraComponent* TransitionComponent = SpawnNiagaraEffect(TransitionConfig, Location, FRotator::ZeroRotator);
    if (TransitionComponent)
    {
        // Configurar parâmetros específicos da transição
        TransitionComponent->SetVariableInt(FName("User.FromRealm"), FromRealm);
        TransitionComponent->SetVariableInt(FName("User.ToRealm"), ToRealm);
        TransitionComponent->SetVariableFloat(FName("User.TransitionIntensity"), 1.5f);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Transição de realm %d para %d iniciada"), FromRealm, ToRealm);
        return true;
    }

    return false;
}

bool UAuracronVFXBridge::UpdateAmbientVFX(int32 RealmIndex, float Intensity)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    int32 UpdatedCount = 0;
    
    // Atualizar todos os efeitos ambientais ativos
    for (UNiagaraComponent* Component : ActiveVFXComponents)
    {
        if (!IsValid(Component))
        {
            continue;
        }

        // Verificar se é um efeito ambiental
        FString ComponentName = Component->GetName();
        if (ComponentName.Contains(TEXT("Ambient")) || ComponentName.Contains(TEXT("Environmental")))
        {
            // Atualizar intensidade
            Component->SetVariableFloat(FName("User.Intensity"), Intensity);
            Component->SetVariableFloat(FName("User.AmbientScale"), Intensity);
            Component->SetVariableInt(FName("User.RealmIndex"), RealmIndex);
            
            // Ajustar visibilidade baseado na intensidade
            Component->SetVisibility(Intensity > 0.1f);
            
            UpdatedCount++;
        }
    }

    if (UpdatedCount > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: %d efeitos ambientais atualizados para realm %d com intensidade %.2f"), UpdatedCount, RealmIndex, Intensity);
    }

    return UpdatedCount > 0;
}

UNiagaraComponent* UAuracronVFXBridge::SpawnPortalVFX(const FVector& Location, int32 DestinationRealm)
{
    if (!bSystemInitialized)
    {
        return nullptr;
    }

    // Configurar efeito de portal
    FAuracronVFXConfiguration PortalConfig;
    PortalConfig.VFXType = EAuracronVFXType::Environmental;
    PortalConfig.Scale = FVector(1.5f, 1.5f, 2.0f);
    PortalConfig.Intensity = 2.0f;
    PortalConfig.Duration = 0.0f; // Portal permanente

    // Carregar sistema de portal específico para o realm de destino
    FString PortalPath = FString::Printf(TEXT("/Game/VFX/Portals/NS_Portal_Realm_%d"), DestinationRealm);
    UNiagaraSystem* PortalSystem = LoadObject<UNiagaraSystem>(nullptr, *PortalPath);
    
    if (!PortalSystem)
    {
        // Fallback para portal genérico
        PortalPath = TEXT("/Game/VFX/Portals/NS_GenericPortal");
        PortalSystem = LoadObject<UNiagaraSystem>(nullptr, *PortalPath);
    }

    if (!PortalSystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Sistema de portal não encontrado"));
        return nullptr;
    }

    PortalConfig.NiagaraSystem = PortalSystem;
    
    UNiagaraComponent* PortalComponent = SpawnNiagaraEffect(PortalConfig, Location, FRotator::ZeroRotator);
    if (PortalComponent)
    {
        // Configurar parâmetros específicos do portal
        PortalComponent->SetVariableInt(FName("User.DestinationRealm"), DestinationRealm);
        PortalComponent->SetVariableFloat(FName("User.PortalIntensity"), 2.0f);
        FVector RealmColorVec = GetRealmColor(DestinationRealm);
        FLinearColor RealmColor(RealmColorVec.X, RealmColorVec.Y, RealmColorVec.Z, 1.0f);
        PortalComponent->SetVariableLinearColor(FName("User.PortalColor"), RealmColor);
        
        // Portal é permanente, não adicionar timer de destruição
        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Portal para realm %d criado em %s"), DestinationRealm, *Location.ToString());
    }

    return PortalComponent;
}

bool UAuracronVFXBridge::ApplyDissolveEffect(AActor* TargetActor, float DissolveTime)
{
    if (!IsValid(TargetActor) || !bSystemInitialized)
    {
        return false;
    }

    // Obter todos os componentes de mesh do ator
    TArray<UMeshComponent*> MeshComponents;
    TargetActor->GetComponents<UMeshComponent>(MeshComponents);

    if (MeshComponents.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Nenhum componente de mesh encontrado em %s"), *TargetActor->GetName());
        return false;
    }

    bool bAppliedToAny = false;
    
    for (UMeshComponent* MeshComp : MeshComponents)
    {
        if (!IsValid(MeshComp))
        {
            continue;
        }

        // Criar material dinâmico para dissolve
        UMaterialInterface* OriginalMaterial = MeshComp->GetMaterial(0);
        if (!OriginalMaterial)
        {
            continue;
        }

        // Carregar material de dissolve
        UMaterialInterface* DissolveMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Game/Materials/VFX/M_DissolveEffect"));
        if (!DissolveMaterial)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Material de dissolve não encontrado"));
            continue;
        }

        UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(DissolveMaterial, this);
        if (!DynamicMaterial)
        {
            continue;
        }

        // Configurar parâmetros do material
        DynamicMaterial->SetTextureParameterValue(TEXT("OriginalTexture"), Cast<UTexture>(OriginalMaterial));
        DynamicMaterial->SetScalarParameterValue(TEXT("DissolveAmount"), 0.0f);
        DynamicMaterial->SetVectorParameterValue(TEXT("DissolveColor"), FLinearColor(1.0f, 0.5f, 0.0f, 1.0f));

        // Aplicar material ao mesh
        MeshComp->SetMaterial(0, DynamicMaterial);
        ActiveDynamicMaterials.Add(DynamicMaterial);

        // Criar timeline para animar o dissolve
        FTimerHandle DissolveTimer;
        FTimerDelegate DissolveDelegate;
        
        // Capturar variáveis por valor para o lambda
        DissolveDelegate.BindLambda([this, DynamicMaterial, DissolveTime, MeshComp, OriginalMaterial]()
        {
            static float CurrentTime = 0.0f;
            CurrentTime += 0.1f; // Incremento de 0.1 segundos
            
            float DissolveProgress = FMath::Clamp(CurrentTime / DissolveTime, 0.0f, 1.0f);
            
            if (IsValid(DynamicMaterial))
            {
                DynamicMaterial->SetScalarParameterValue(TEXT("DissolveAmount"), DissolveProgress);
            }
            
            // Quando o dissolve terminar, restaurar material original
            if (DissolveProgress >= 1.0f)
            {
                CurrentTime = 0.0f;
                if (IsValid(MeshComp) && IsValid(OriginalMaterial))
                {
                    MeshComp->SetMaterial(0, OriginalMaterial);
                }
            }
        });

        GetWorld()->GetTimerManager().SetTimer(DissolveTimer, DissolveDelegate, 0.1f, true, 0.0f);
        
        // Parar o timer após o tempo de dissolve
        FTimerHandle StopTimer;
        GetWorld()->GetTimerManager().SetTimer(StopTimer, [this, &DissolveTimer]()
        {
            GetWorld()->GetTimerManager().ClearTimer(DissolveTimer);
        }, DissolveTime + 0.1f, false);

        bAppliedToAny = true;
    }

    if (bAppliedToAny)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Efeito de dissolve aplicado a %s por %.2f segundos"), *TargetActor->GetName(), DissolveTime);
    }

    return bAppliedToAny;
}

FVector UAuracronVFXBridge::GetRealmColor(int32 RealmIndex)
{
    // Cores específicas para cada realm
    switch (RealmIndex)
    {
        case 0: return FVector(0.2f, 0.8f, 1.0f);   // Azul claro - Realm Celestial
        case 1: return FVector(1.0f, 0.3f, 0.1f);   // Vermelho - Realm Infernal
        case 2: return FVector(0.1f, 1.0f, 0.3f);   // Verde - Realm Natural
        case 3: return FVector(0.8f, 0.2f, 1.0f);   // Roxo - Realm Arcano
        case 4: return FVector(1.0f, 1.0f, 0.2f);   // Amarelo - Realm Solar
        case 5: return FVector(0.3f, 0.3f, 0.9f);   // Azul escuro - Realm Lunar
        case 6: return FVector(0.9f, 0.9f, 0.9f);   // Branco - Realm Etéreo
        case 7: return FVector(0.1f, 0.1f, 0.1f);   // Preto - Realm Sombrio
        default: return FVector(0.5f, 0.5f, 0.5f);  // Cinza - Realm desconhecido
    }
}

bool UAuracronVFXBridge::PauseAllVFXEffects()
{
    if (ActiveVFXComponents.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Nenhum efeito ativo para pausar"));
        return false;
    }

    int32 PausedCount = 0;
    
    // Pausar todos os componentes Niagara ativos
    for (UNiagaraComponent* Component : ActiveVFXComponents)
    {
        if (IsValid(Component) && Component->IsActive())
        {
            Component->SetPaused(true);
            PausedCount++;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: %d efeitos pausados com sucesso"), PausedCount);
    return PausedCount > 0;
}

bool UAuracronVFXBridge::ResumeAllVFXEffects()
{
    if (ActiveVFXComponents.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Nenhum efeito ativo para retomar"));
        return false;
    }

    int32 ResumedCount = 0;
    
    // Retomar todos os componentes Niagara pausados
    for (UNiagaraComponent* Component : ActiveVFXComponents)
    {
        if (IsValid(Component) && Component->IsPaused())
        {
            Component->SetPaused(false);
            ResumedCount++;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: %d efeitos retomados com sucesso"), ResumedCount);
    return ResumedCount > 0;
}

// === Helper Functions ===

UNiagaraComponent* UAuracronVFXBridge::GetComponentFromPool()
{
    // Implementar pooling de componentes para otimização
    if (ComponentPool.Num() > 0)
    {
        return ComponentPool.Pop();
    }
    
    // Criar novo componente se pool estiver vazio
    return NewObject<UNiagaraComponent>(GetOwner());
}

bool UAuracronVFXBridge::ReturnComponentToPool(UNiagaraComponent* Component)
{
    if (IsValid(Component))
    {
        Component->Deactivate();
        Component->SetAsset(nullptr);
        ComponentPool.Add(Component);
        return true;
    }
    return false;
}

// === Ability VFX ===

bool UAuracronVFXBridge::PlayAbilityVFX(const FString& ChampionID, const FString& AbilitySlot, const FVector& Location, const FVector& TargetLocation)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Sistema não inicializado"));
        return false;
    }

    // Criar configuração baseada no champion e slot
    FAuracronVFXConfiguration VFXConfig;
    VFXConfig.VFXType = EAuracronVFXType::Ability;
    VFXConfig.Duration = 3.0f;
    VFXConfig.Scale = FVector(1.0f);
    VFXConfig.Intensity = 1.0f;
    
    // Definir cor baseada no slot da habilidade
    if (AbilitySlot == "Q")
    {
        VFXConfig.Color = FLinearColor::Red;
    }
    else if (AbilitySlot == "W")
    {
        VFXConfig.Color = FLinearColor::Green;
    }
    else if (AbilitySlot == "E")
    {
        VFXConfig.Color = FLinearColor::Blue;
    }
    else if (AbilitySlot == "R")
    {
        VFXConfig.Color = FLinearColor::Yellow;
    }
    
    // Carregar sistema Niagara específico baseado no ChampionID e AbilitySlot
    FString NiagaraSystemPath = FString::Printf(TEXT("/Game/VFX/Niagara/Champions/%s/Abilities/NS_%s_%s.NS_%s_%s"), 
        *ChampionID, *ChampionID, *AbilitySlot, *ChampionID, *AbilitySlot);
    
    // Tentar carregar sistema específico do campeão
    FSoftObjectPath SystemPath(NiagaraSystemPath);
    UNiagaraSystem* NiagaraSystem = Cast<UNiagaraSystem>(SystemPath.TryLoad());
    
    // Se não encontrar sistema específico, usar sistema genérico baseado no slot
    if (!NiagaraSystem)
    {
        FString GenericPath = FString::Printf(TEXT("/Game/VFX/Niagara/Abilities/Generic/NS_Ability_%s.NS_Ability_%s"), 
            *AbilitySlot, *AbilitySlot);
        FSoftObjectPath GenericSystemPath(GenericPath);
        NiagaraSystem = Cast<UNiagaraSystem>(GenericSystemPath.TryLoad());

        if (!NiagaraSystem)
        {
            // Fallback para sistema base
            FSoftObjectPath BaseSystemPath(TEXT("/Game/VFX/Niagara/Abilities/NS_AbilityBase.NS_AbilityBase"));
            NiagaraSystem = Cast<UNiagaraSystem>(BaseSystemPath.TryLoad());
        }
    }
    
    // Atualizar configuração com o sistema carregado
    if (NiagaraSystem)
    {
        VFXConfig.NiagaraSystem = NiagaraSystem;
        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Sistema Niagara carregado para %s-%s: %s"), 
            *ChampionID, *AbilitySlot, *NiagaraSystem->GetName());
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Nenhum sistema Niagara encontrado para %s-%s"), 
            *ChampionID, *AbilitySlot);
    }
    
    UNiagaraComponent* EffectComponent = SpawnNiagaraEffect(VFXConfig, Location);
    
    if (EffectComponent && TargetLocation != FVector::ZeroVector)
    {
        // Configurar direção para o alvo
        FVector Direction = (TargetLocation - Location).GetSafeNormal();
        EffectComponent->SetVariableVec3(FName("User.Direction"), Direction);
        EffectComponent->SetVariableVec3(FName("User.TargetLocation"), TargetLocation);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Efeito de habilidade %s-%s reproduzido"), *ChampionID, *AbilitySlot);
    return EffectComponent != nullptr;
}

bool UAuracronVFXBridge::PlayImpactVFX(const FVector& Location, const FString& ImpactType, float Intensity)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Sistema não inicializado"));
        return false;
    }

    FAuracronVFXConfiguration VFXConfig;
    VFXConfig.VFXType = EAuracronVFXType::Impact;
    VFXConfig.Duration = 1.5f;
    VFXConfig.Scale = FVector(Intensity);
    VFXConfig.Intensity = Intensity;
    
    // Definir cor baseada no tipo de impacto
    if (ImpactType == "Fire")
    {
        VFXConfig.Color = FLinearColor(1.0f, 0.3f, 0.0f, 1.0f); // Laranja-vermelho
    }
    else if (ImpactType == "Ice")
    {
        VFXConfig.Color = FLinearColor(0.0f, 0.7f, 1.0f, 1.0f); // Azul claro
    }
    else if (ImpactType == "Lightning")
    {
        VFXConfig.Color = FLinearColor(1.0f, 1.0f, 0.0f, 1.0f); // Amarelo
    }
    else if (ImpactType == "Physical")
    {
        VFXConfig.Color = FLinearColor(0.8f, 0.8f, 0.8f, 1.0f); // Cinza
    }
    else
    {
        VFXConfig.Color = FLinearColor::White; // Padrão
    }
    
    UNiagaraComponent* EffectComponent = SpawnNiagaraEffect(VFXConfig, Location);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Efeito de impacto %s reproduzido com intensidade %.2f"), *ImpactType, Intensity);
    return EffectComponent != nullptr;
}

bool UAuracronVFXBridge::PlayHealingVFX(AActor* TargetActor, float HealAmount)
{
    if (!bSystemInitialized || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Sistema não inicializado ou ator inválido"));
        return false;
    }

    FAuracronVFXConfiguration VFXConfig;
    VFXConfig.VFXType = EAuracronVFXType::Healing;
    VFXConfig.Duration = 2.0f;
    VFXConfig.Scale = FVector(FMath::Clamp(HealAmount / 100.0f, 0.5f, 2.0f)); // Escala baseada na quantidade de cura
    VFXConfig.Intensity = 1.0f;
    VFXConfig.Color = FLinearColor(0.0f, 1.0f, 0.3f, 1.0f); // Verde claro
    VFXConfig.bFollowActor = true;
    VFXConfig.ActorToFollow = TargetActor;
    
    UNiagaraComponent* EffectComponent = SpawnAttachedNiagaraEffect(VFXConfig, TargetActor);
    
    if (EffectComponent)
    {
        // Configurar parâmetros específicos de cura
        EffectComponent->SetVariableFloat(FName("User.HealAmount"), HealAmount);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Efeito de cura reproduzido em %s (%.2f HP)"), *TargetActor->GetName(), HealAmount);
    return EffectComponent != nullptr;
}

bool UAuracronVFXBridge::PlayStatusEffectVFX(AActor* TargetActor, const FString& EffectType, float Duration)
{
    if (!bSystemInitialized || !IsValid(TargetActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Sistema não inicializado ou ator inválido"));
        return false;
    }

    FAuracronVFXConfiguration VFXConfig;
    VFXConfig.Duration = Duration;
    VFXConfig.Scale = FVector(1.0f);
    VFXConfig.Intensity = 1.0f;
    VFXConfig.bFollowActor = true;
    VFXConfig.ActorToFollow = TargetActor;
    
    // Definir tipo e cor baseado no efeito
    if (EffectType.Contains("Buff"))
    {
        VFXConfig.VFXType = EAuracronVFXType::Buff;
        VFXConfig.Color = FLinearColor(0.0f, 0.8f, 1.0f, 1.0f); // Azul claro
    }
    else if (EffectType.Contains("Debuff"))
    {
        VFXConfig.VFXType = EAuracronVFXType::Debuff;
        VFXConfig.Color = FLinearColor(0.8f, 0.0f, 0.8f, 1.0f); // Roxo
    }
    else
    {
        VFXConfig.VFXType = EAuracronVFXType::Aura;
        VFXConfig.Color = FLinearColor::White;
    }
    
    UNiagaraComponent* EffectComponent = SpawnAttachedNiagaraEffect(VFXConfig, TargetActor);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Efeito de status %s aplicado em %s por %.2f segundos"), *EffectType, *TargetActor->GetName(), Duration);
    return EffectComponent != nullptr;
}

// === Realm VFX ===

bool UAuracronVFXBridge::ActivateRealmVFX(int32 RealmIndex)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Sistema não inicializado"));
        return false;
    }

    if (RealmIndex < 0 || RealmIndex >= 3) // 0=Planície, 1=Firmamento, 2=Abismo
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Índice de realm inválido: %d"), RealmIndex);
        return false;
    }

    // Selecionar efeitos baseados no realm
    TArray<TSoftObjectPtr<UNiagaraSystem>>* AmbientEffects = nullptr;
    
    switch (RealmIndex)
    {
        case 0: // Planície
            AmbientEffects = &RealmVFXConfiguration.SurfaceAmbientEffects;
            break;
        case 1: // Firmamento
            AmbientEffects = &RealmVFXConfiguration.SkyAmbientEffects;
            break;
        case 2: // Abismo
            AmbientEffects = &RealmVFXConfiguration.UndergroundAmbientEffects;
            break;
    }

    if (!AmbientEffects || AmbientEffects->IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Nenhum efeito ambiente configurado para realm %d"), RealmIndex);
        return false;
    }

    // Spawnar efeitos ambientes
    int32 SpawnedCount = 0;
    for (const auto& EffectAsset : *AmbientEffects)
    {
        if (EffectAsset.IsValid())
        {
            FAuracronVFXConfiguration VFXConfig;
            VFXConfig.NiagaraSystem = EffectAsset;
            VFXConfig.VFXType = EAuracronVFXType::Environmental;
            VFXConfig.Duration = 0.0f; // Permanente
            VFXConfig.Intensity = RealmVFXConfiguration.AmbientEffectDensity;
            
            // Spawnar em posição aleatória próxima ao jogador
            FVector SpawnLocation = FVector::ZeroVector;
            if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
            {
                if (APawn* Pawn = PC->GetPawn())
                {
                    SpawnLocation = Pawn->GetActorLocation() + FVector(
                        FMath::RandRange(-1000.0f, 1000.0f),
                        FMath::RandRange(-1000.0f, 1000.0f),
                        FMath::RandRange(-100.0f, 100.0f)
                    );
                }
            }
            
            if (SpawnNiagaraEffect(VFXConfig, SpawnLocation))
            {
                SpawnedCount++;
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: %d efeitos ambientes ativados para realm %d"), SpawnedCount, RealmIndex);
    return SpawnedCount > 0;
}

bool UAuracronVFXBridge::DeactivateRealmVFX(int32 RealmIndex)
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Sistema não inicializado"));
        return false;
    }

    // Parar todos os efeitos ambientes ativos
    int32 StoppedCount = 0;
    for (int32 i = ActiveVFXComponents.Num() - 1; i >= 0; i--)
    {
        UNiagaraComponent* Component = ActiveVFXComponents[i];
        if (IsValid(Component) && Component->GetAsset())
        {
            // Verificar se é um efeito ambiental
            if (Component->GetAsset()->GetName().Contains("Ambient") || 
                Component->GetAsset()->GetName().Contains("Environment"))
            {
                StopVFXEffect(Component);
                StoppedCount++;
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: %d efeitos ambientes desativados para realm %d"), StoppedCount, RealmIndex);
    return StoppedCount > 0;
}

// === Dynamic Materials ===

UMaterialInstanceDynamic* UAuracronVFXBridge::CreateDynamicMaterial(UMaterialInterface* BaseMaterial, const FString& MaterialName)
{
    if (!IsValid(BaseMaterial))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Material base inválido para %s"), *MaterialName);
        return nullptr;
    }

    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this, FName(*MaterialName));
    if (!DynamicMaterial)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON VFX: Falha ao criar material dinâmico %s"), *MaterialName);
        return nullptr;
    }

    // Armazenar referência para gerenciamento
    DynamicMaterials.Add(MaterialName, DynamicMaterial);
    
    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Material dinâmico %s criado com sucesso"), *MaterialName);
    return DynamicMaterial;
}

UMaterialInstanceDynamic* UAuracronVFXBridge::CreateDynamicMaterial(UMaterialInterface* BaseMaterial, const TMap<FString, float>& Parameters)
{
    if (!IsValid(BaseMaterial))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Material base inválido"));
        return nullptr;
    }

    // Criar material dinâmico
    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
    if (!DynamicMaterial)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON VFX: Falha ao criar material dinâmico"));
        return nullptr;
    }

    // Aplicar parâmetros fornecidos
    int32 AppliedCount = 0;
    for (const auto& Parameter : Parameters)
    {
        const FString& ParameterName = Parameter.Key;
        const float& ParameterValue = Parameter.Value;

        if (!ParameterName.IsEmpty())
        {
            DynamicMaterial->SetScalarParameterValue(FName(*ParameterName), ParameterValue);
            AppliedCount++;
            UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON VFX: Parâmetro %s definido como %.2f"), *ParameterName, ParameterValue);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Material dinâmico criado com %d parâmetros aplicados"), AppliedCount);
    return DynamicMaterial;
}

bool UAuracronVFXBridge::UpdateDynamicMaterial(const FString& MaterialName, const FString& ParameterName, float Value)
{
    TObjectPtr<UMaterialInstanceDynamic>* FoundMaterial = DynamicMaterials.Find(MaterialName);
    if (!FoundMaterial || !IsValid(*FoundMaterial))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Material dinâmico %s não encontrado"), *MaterialName);
        return false;
    }

    (*FoundMaterial)->SetScalarParameterValue(FName(*ParameterName), Value);
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON VFX: Parâmetro %s do material %s atualizado para %.2f"), *ParameterName, *MaterialName, Value);
    return true;
}

bool UAuracronVFXBridge::UpdateDynamicMaterialVector(const FString& MaterialName, const FString& ParameterName, const FLinearColor& Value)
{
    TObjectPtr<UMaterialInstanceDynamic>* FoundMaterial = DynamicMaterials.Find(MaterialName);
    if (!FoundMaterial || !IsValid(*FoundMaterial))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Material dinâmico %s não encontrado"), *MaterialName);
        return false;
    }

    (*FoundMaterial)->SetVectorParameterValue(FName(*ParameterName), Value);
    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON VFX: Parâmetro vetorial %s do material %s atualizado"), *ParameterName, *MaterialName);
    return true;
}

bool UAuracronVFXBridge::UpdateMaterialParameters(UMaterialInstanceDynamic* Material, const TMap<FString, float>& Parameters)
{
    if (!IsValid(Material))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Material inválido para atualização de parâmetros"));
        return false;
    }

    if (Parameters.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Nenhum parâmetro fornecido para atualização"));
        return false;
    }

    int32 UpdatedCount = 0;
    for (const auto& Parameter : Parameters)
    {
        const FString& ParameterName = Parameter.Key;
        const float& ParameterValue = Parameter.Value;

        if (!ParameterName.IsEmpty())
        {
            Material->SetScalarParameterValue(FName(*ParameterName), ParameterValue);
            UpdatedCount++;
            UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON VFX: Parâmetro %s atualizado para %.2f"), *ParameterName, ParameterValue);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: %d parâmetros de material atualizados com sucesso"), UpdatedCount);
    return UpdatedCount > 0;
}

// === Performance Management ===

bool UAuracronVFXBridge::SetVFXQuality(EAuracronVFXQuality NewQuality)
{
    if (CurrentVFXQuality == NewQuality)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Qualidade já está definida como %d"), (int32)NewQuality);
        return true;
    }

    EAuracronVFXQuality OldQuality = CurrentVFXQuality;
    CurrentVFXQuality = NewQuality;

    // Aplicar configurações baseadas na qualidade
    switch (NewQuality)
    {
        case EAuracronVFXQuality::Low:
            MaxActiveVFXComponents = 20;
            RealmVFXConfiguration.AmbientEffectDensity = 0.3f;
            break;
        case EAuracronVFXQuality::Medium:
            MaxActiveVFXComponents = 50;
            RealmVFXConfiguration.AmbientEffectDensity = 0.6f;
            break;
        case EAuracronVFXQuality::High:
            MaxActiveVFXComponents = 100;
            RealmVFXConfiguration.AmbientEffectDensity = 1.0f;
            break;
        case EAuracronVFXQuality::Ultra:
            MaxActiveVFXComponents = 200;
            RealmVFXConfiguration.AmbientEffectDensity = 1.5f;
            break;
    }

    // Ajustar efeitos ativos se necessário
    if (ActiveVFXComponents.Num() > MaxActiveVFXComponents)
    {
        int32 ExcessCount = ActiveVFXComponents.Num() - MaxActiveVFXComponents;
        for (int32 i = 0; i < ExcessCount; i++)
        {
            if (ActiveVFXComponents.IsValidIndex(0))
            {
                StopVFXEffect(ActiveVFXComponents[0]);
            }
        }
        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: %d efeitos removidos devido à mudança de qualidade"), ExcessCount);
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Qualidade alterada de %d para %d"), (int32)OldQuality, (int32)NewQuality);
    return true;
}

bool UAuracronVFXBridge::InitializeVFXSystem()
{
    if (bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Sistema já inicializado"));
        return true;
    }

    // Verificar se o mundo é válido
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON VFX: Mundo inválido durante inicialização"));
        return false;
    }

    // Inicializar pools de componentes
    if (!SetupComponentPooling())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON VFX: Falha ao configurar pooling de componentes"));
        return false;
    }

    // Configurar sistema Niagara
    if (!SetupNiagara())
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON VFX: Falha ao configurar sistema Niagara"));
        return false;
    }

    // Configurar timers de otimização usando FTimerDelegate
    World->GetTimerManager().SetTimer(CleanupTimer,
        FTimerDelegate::CreateUObject(this, &UAuracronVFXBridge::PerformCleanup), 30.0f, true);
    World->GetTimerManager().SetTimer(OptimizationTimer, [this]()
    {
        if (AActor* Owner = GetOwner())
        {
            OptimizeVFXByDistance(Owner->GetActorLocation());
        }
    }, 5.0f, true);

    bSystemInitialized = true;
    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Sistema inicializado com sucesso"));
    return true;
}

bool UAuracronVFXBridge::SetupNiagara()
{
    // Verificar se o subsistema Niagara está disponível
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    // Pré-carregar sistemas Niagara essenciais
    TArray<FSoftObjectPath> NiagaraSystems = {
        FSoftObjectPath(TEXT("/Game/VFX/Niagara/Abilities/NS_AbilityBase.NS_AbilityBase")),
        FSoftObjectPath(TEXT("/Game/VFX/Niagara/Impact/NS_ImpactBase.NS_ImpactBase")),
        FSoftObjectPath(TEXT("/Game/VFX/Niagara/Healing/NS_HealingBase.NS_HealingBase")),
        FSoftObjectPath(TEXT("/Game/VFX/Niagara/StatusEffects/NS_BuffBase.NS_BuffBase")),
        FSoftObjectPath(TEXT("/Game/VFX/Niagara/Realm/NS_RealmTransition.NS_RealmTransition"))
    };

    // Carregar sistemas de forma assíncrona
    for (const FSoftObjectPath& SystemPath : NiagaraSystems)
    {
        if (SystemPath.IsValid())
        {
            // Usar StreamableManager para carregamento assíncrono no UE 5.6
            if (UAssetManager* AssetManager = UAssetManager::GetIfInitialized())
            {
                FStreamableManager& StreamableManager = AssetManager->GetStreamableManager();
                StreamableManager.RequestAsyncLoad(SystemPath, FStreamableDelegate::CreateLambda([this, SystemPath]()
                {
                    if (UNiagaraSystem* LoadedSystem = Cast<UNiagaraSystem>(SystemPath.ResolveObject()))
                    {
                        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Sistema Niagara carregado: %s"), *SystemPath.ToString());
                    }
                    else
                    {
                        UE_LOG(LogTemp, Warning, TEXT("AURACRON VFX: Falha ao carregar sistema Niagara: %s"), *SystemPath.ToString());
                    }
                }));
            }
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Configuração Niagara iniciada"));
    return true;
}

bool UAuracronVFXBridge::SetupComponentPooling()
{
    // Inicializar pool de componentes Niagara
    const int32 InitialPoolSize = 20;
    NiagaraComponentPool.Reserve(InitialPoolSize);
    ComponentPool.Reserve(InitialPoolSize);

    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    // Criar componentes iniciais para o pool
    for (int32 i = 0; i < InitialPoolSize; i++)
    {
        UNiagaraComponent* NewComponent = NewObject<UNiagaraComponent>(this);
        if (NewComponent)
        {
            NewComponent->SetAutoDestroy(false);
            NewComponent->SetVisibility(false);
            NewComponent->Deactivate();
            NiagaraComponentPool.Add(NewComponent);
            ComponentPool.Add(NewComponent);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Pool de componentes inicializado com %d componentes"), InitialPoolSize);
    return true;
}

bool UAuracronVFXBridge::ValidateVFXConfiguration(const FAuracronVFXConfiguration& VFXConfig) const
{
    // Verificar se o sistema Niagara é válido
    if (!VFXConfig.NiagaraSystem.IsValid())
    {
        return false;
    }

    // Verificar se a escala é válida
    if (VFXConfig.Scale.IsNearlyZero() || VFXConfig.Scale.ContainsNaN())
    {
        return false;
    }

    // Verificar se a intensidade é válida
    if (VFXConfig.Intensity < 0.0f || FMath::IsNaN(VFXConfig.Intensity))
    {
        return false;
    }

    // Verificar se a duração é válida (0 = infinito é válido)
    if (VFXConfig.Duration < 0.0f || FMath::IsNaN(VFXConfig.Duration))
    {
        return false;
    }

    return true;
}

UNiagaraComponent* UAuracronVFXBridge::GetPooledNiagaraComponent()
{
    // Tentar obter componente do pool
    if (NiagaraComponentPool.Num() > 0)
    {
        UNiagaraComponent* Component = NiagaraComponentPool.Pop();
        if (IsValid(Component))
        {
            Component->SetVisibility(true);
            Component->Activate();
            return Component;
        }
    }

    // Se não há componentes no pool, criar um novo
    UNiagaraComponent* NewComponent = NewObject<UNiagaraComponent>(this);
    if (NewComponent)
    {
        NewComponent->SetAutoDestroy(false);
        NewComponent->SetVisibility(true);
    }

    return NewComponent;
}

// Implementação duplicada removida - usando a implementação void acima

void UAuracronVFXBridge::ProcessActiveVFX(float DeltaTime)
{
    // Processar componentes ativos para otimização e limpeza
    for (int32 i = ActiveVFXComponents.Num() - 1; i >= 0; i--)
    {
        UNiagaraComponent* Component = ActiveVFXComponents[i];
        if (!IsValid(Component) || !Component->IsActive())
        {
            // Remover componentes inválidos ou inativos
            ActiveVFXComponents.RemoveAt(i);
            continue;
        }

        // Verificar se o componente deve ser removido por distância
        if (AActor* Owner = GetOwner())
        {
            float Distance = FVector::Dist(Component->GetComponentLocation(), Owner->GetActorLocation());
            const float MaxDistance = 5000.0f; // 50 metros
            
            if (Distance > MaxDistance)
            {
                StopVFXEffect(Component);
                continue;
            }
        }
     }
}

bool UAuracronVFXBridge::OptimizeVFXByDistance(const FVector& ViewerLocation)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    int32 OptimizedCount = 0;
    const float NearDistance = 1000.0f;   // 10 metros
    const float MediumDistance = 3000.0f; // 30 metros
    const float FarDistance = 5000.0f;    // 50 metros

    for (UNiagaraComponent* Component : ActiveVFXComponents)
    {
        if (!IsValid(Component))
        {
            continue;
        }

        float Distance = FVector::Dist(Component->GetComponentLocation(), ViewerLocation);
        
        // Ajustar qualidade baseado na distância
        if (Distance > FarDistance)
        {
            // Muito longe - desativar
            Component->SetVisibility(false);
            OptimizedCount++;
        }
        else if (Distance > MediumDistance)
        {
            // Distância média - reduzir qualidade
            Component->SetVariableFloat(FName("User.QualityScale"), 0.5f);
            Component->SetVisibility(true);
            OptimizedCount++;
        }
        else if (Distance > NearDistance)
        {
            // Distância próxima - qualidade normal
            Component->SetVariableFloat(FName("User.QualityScale"), 0.8f);
            Component->SetVisibility(true);
            OptimizedCount++;
        }
        else
        {
            // Muito próximo - qualidade máxima
            Component->SetVariableFloat(FName("User.QualityScale"), 1.0f);
            Component->SetVisibility(true);
        }
    }

    if (OptimizedCount > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: %d componentes otimizados por distância"), OptimizedCount);
    }

    return true;
}

bool UAuracronVFXBridge::CleanupInactiveVFX()
{
    if (!bSystemInitialized)
    {
        return false;
    }

    int32 CleanedCount = 0;
    
    // Limpar componentes inativos
    for (int32 i = ActiveVFXComponents.Num() - 1; i >= 0; i--)
    {
        UNiagaraComponent* Component = ActiveVFXComponents[i];
        if (!IsValid(Component) || !Component->IsActive() || !Component->GetSystemInstanceController().IsValid())
        {
            ActiveVFXComponents.RemoveAt(i);
            if (IsValid(Component))
            {
                ReturnComponentToPool(Component);
            }
            CleanedCount++;
        }
    }

    // Limpar materiais dinâmicos órfãos
    for (int32 i = ActiveDynamicMaterials.Num() - 1; i >= 0; i--)
    {
        if (!IsValid(ActiveDynamicMaterials[i]))
        {
            ActiveDynamicMaterials.RemoveAt(i);
            CleanedCount++;
        }
    }

    if (CleanedCount > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: %d elementos inativos limpos"), CleanedCount);
    }

    return true;
}

void UAuracronVFXBridge::PerformCleanup()
{
    // Chama o método de limpeza existente
    CleanupInactiveVFX();
}

// === Replication Callbacks ===

void UAuracronVFXBridge::OnRep_VFXQuality()
{
    static EAuracronVFXQuality PreviousQuality = EAuracronVFXQuality::High;
    EAuracronVFXQuality OldQuality = PreviousQuality;
    PreviousQuality = CurrentVFXQuality;

    UE_LOG(LogTemp, Log, TEXT("AURACRON VFX: Qualidade VFX replicada alterada de %d para %d"), (int32)OldQuality, (int32)CurrentVFXQuality);

    // Aplicar nova qualidade a todos os efeitos ativos
    for (UNiagaraComponent* Component : ActiveVFXComponents)
    {
        if (IsValid(Component))
        {
            // Ajustar qualidade baseado no nível atual
            switch (CurrentVFXQuality)
            {
                case EAuracronVFXQuality::Low:
                    Component->SetVariableFloat(FName("QualityMultiplier"), 0.5f);
                    Component->SetVariableInt(FName("MaxParticles"), 100);
                    break;
                case EAuracronVFXQuality::Medium:
                    Component->SetVariableFloat(FName("QualityMultiplier"), 0.75f);
                    Component->SetVariableInt(FName("MaxParticles"), 250);
                    break;
                case EAuracronVFXQuality::High:
                    Component->SetVariableFloat(FName("QualityMultiplier"), 1.0f);
                    Component->SetVariableInt(FName("MaxParticles"), 500);
                    break;
                case EAuracronVFXQuality::Ultra:
                    Component->SetVariableFloat(FName("QualityMultiplier"), 1.5f);
                    Component->SetVariableInt(FName("MaxParticles"), 1000);
                    break;
            }
        }
    }

    // Notificar outros sistemas sobre mudança de qualidade
    OnVFXQualityChanged.Broadcast(OldQuality, CurrentVFXQuality);
}
