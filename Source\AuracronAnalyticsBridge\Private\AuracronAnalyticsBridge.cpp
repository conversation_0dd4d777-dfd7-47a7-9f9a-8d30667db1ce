// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Telemetria e Analytics Bridge Implementation

#include "AuracronAnalyticsBridge.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "Analytics.h"
#include "AnalyticsEventAttribute.h"
#include "Interfaces/IAnalyticsProvider.h"
#include "Json.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "GameFramework/GameStateBase.h"
#include "GameFramework/PlayerState.h"
#include "Engine/NetConnection.h"
#include "AudioDevice.h"
#include "AudioMixerDevice.h"
#include "Kismet/GameplayStatics.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/Guid.h"

UAuracronAnalyticsBridge::UAuracronAnalyticsBridge()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 1.0f; // 1 FPS para analytics
    
    // Configurar replicação
    SetIsReplicatedByDefault(true);
    
    // Gerar ID de sessão único
    CurrentSessionID = FGuid::NewGuid().ToString();
    
    // Configurações padrão
    bAnalyticsEnabled = true;
}

void UAuracronAnalyticsBridge::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Inicializando Sistema de Analytics"));

    // Inicializar sistema
    bSystemInitialized = InitializeAnalyticsSystem();
    
    if (bSystemInitialized)
    {
        // Configurar timers
        GetWorld()->GetTimerManager().SetTimer(
            EventProcessingTimer,
            [this]()
            {
                ProcessEventQueue(1.0f);
            },
            1.0f,
            true
        );

        GetWorld()->GetTimerManager().SetTimer(
            AutoCollectionTimer,
            [this]()
            {
                CollectAutomaticMetrics(5.0f);
            },
            5.0f,
            true
        );
        
        // Registrar início de sessão
        FAuracronAnalyticsEvent SessionStartEvent;
        SessionStartEvent.EventID = FGuid::NewGuid().ToString();
        SessionStartEvent.EventName = TEXT("SessionStart");
        SessionStartEvent.EventType = EAuracronAnalyticsEventType::UserBehavior;
        SessionStartEvent.EventCategory = TEXT("Session");
        SessionStartEvent.EventTimestamp = FDateTime::Now();
        SessionStartEvent.SessionID = CurrentSessionID;
        SessionStartEvent.Priority = 8;

        RecordAnalyticsEvent(SessionStartEvent);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de Analytics inicializado - Sessão: %s"), *CurrentSessionID);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao inicializar Sistema de Analytics"));
    }
}

void UAuracronAnalyticsBridge::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Registrar fim de sessão
    if (bSystemInitialized)
    {
        FAuracronAnalyticsEvent SessionEndEvent;
        SessionEndEvent.EventID = FGuid::NewGuid().ToString();
        SessionEndEvent.EventName = TEXT("SessionEnd");
        SessionEndEvent.EventType = EAuracronAnalyticsEventType::UserBehavior;
        SessionEndEvent.EventCategory = TEXT("Session");
        SessionEndEvent.EventTimestamp = FDateTime::Now();
        SessionEndEvent.SessionID = CurrentSessionID;
        SessionEndEvent.Priority = 8;

        RecordAnalyticsEvent(SessionEndEvent);
        
        // Processar fila final
        ProcessEventQueue(0.0f);
    }

    // Limpar timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearTimer(EventProcessingTimer);
        GetWorld()->GetTimerManager().ClearTimer(AutoCollectionTimer);
    }

    Super::EndPlay(EndPlayReason);
}

void UAuracronAnalyticsBridge::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(UAuracronAnalyticsBridge, BalanceMetrics);
    DOREPLIFETIME(UAuracronAnalyticsBridge, ActiveABTests);
}

void UAuracronAnalyticsBridge::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

    if (!bSystemInitialized || !bAnalyticsEnabled)
        return;

    // Processar fila de eventos
    ProcessEventQueue(DeltaTime);
    
    // Coletar métricas automáticas
    CollectAutomaticMetrics(DeltaTime);
}

// === Core Analytics ===

bool UAuracronAnalyticsBridge::RecordAnalyticsEvent(const FAuracronAnalyticsEvent& Event)
{
    if (!bSystemInitialized || !bAnalyticsEnabled)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de analytics não inicializado ou desabilitado"));
        return false;
    }

    if (!ValidateAnalyticsEvent(Event))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evento analítico inválido: %s"), *Event.EventName);
        return false;
    }

    FScopeLock Lock(&AnalyticsMutex);

    // Adicionar à fila de eventos
    EventQueue.Add(Event);

    UE_LOG(LogTemp, VeryVerbose, TEXT("AURACRON: Evento analítico registrado: %s"), *Event.EventName);

    // Broadcast evento
    OnAnalyticsEventRecorded.Broadcast(Event);

    return true;
}

// === Internal Helper Functions ===

bool UAuracronAnalyticsBridge::InitializeAnalyticsSystem()
{
    if (bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de analytics já inicializado"));
        return true;
    }

    // Initialize analytics provider
    if (!AnalyticsProvider.IsValid())
    {
        // Try to get default analytics provider
        AnalyticsProvider = FAnalytics::Get().GetDefaultConfiguredProvider();
        if (!AnalyticsProvider.IsValid())
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Nenhum provider de analytics configurado"));
        }
    }

    // Clear existing data
    EventQueue.Empty();
    EventCache.Empty();
    LocalAnalyticsData.Empty();
    BalanceMetrics.Empty();
    ActiveABTests.Empty();

    // Initialize session statistics
    SessionStatistics.Empty();
    SessionStatistics.Add(TEXT("SessionStartTime"), FDateTime::Now().ToString());
    SessionStatistics.Add(TEXT("Platform"), UGameplayStatics::GetPlatformName());
    SessionStatistics.Add(TEXT("BuildVersion"), FString(FApp::GetBuildVersion()));
    SessionStatistics.Add(TEXT("EngineVersion"), FEngineVersion::Current().ToString());

    // Set system as initialized
    bSystemInitialized = true;
    bAnalyticsEnabled = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sistema de analytics inicializado com sucesso"));
    FString ProviderName = AnalyticsProvider.IsValid() ? TEXT("Valid Provider") : TEXT("No Provider");
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Provider: %s, SessionID: %s"), *ProviderName, *CurrentSessionID);

    return true;
}

bool UAuracronAnalyticsBridge::ValidateAnalyticsEvent(const FAuracronAnalyticsEvent& Event) const
{
    // Check required fields
    if (Event.EventID.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evento inválido - EventID vazio"));
        return false;
    }

    if (Event.EventName.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evento inválido - EventName vazio para ID: %s"), *Event.EventID);
        return false;
    }

    if (Event.EventCategory.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evento inválido - EventCategory vazio para: %s"), *Event.EventName);
        return false;
    }

    // Validate timestamp
    if (Event.EventTimestamp == FDateTime::MinValue())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evento inválido - Timestamp inválido para: %s"), *Event.EventName);
        return false;
    }

    // Validate priority range
    if (Event.Priority < 0 || Event.Priority > 10)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evento inválido - Priority fora do range (0-10) para: %s"), *Event.EventName);
        return false;
    }

    // Check for reasonable parameter limits
    if (Event.EventParameters.Num() > 50)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evento com muitos parâmetros (%d) para: %s"), Event.EventParameters.Num(), *Event.EventName);
        return false;
    }

    if (Event.NumericValues.Num() > 50)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Evento com muitos valores numéricos (%d) para: %s"), Event.NumericValues.Num(), *Event.EventName);
        return false;
    }

    // Validate parameter and numeric value keys
    for (const auto& Param : Event.EventParameters)
    {
        if (Param.Key.IsEmpty() || Param.Key.Len() > 100)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Chave de parâmetro inválida para evento: %s"), *Event.EventName);
            return false;
        }
        
        if (Param.Value.Len() > 1000)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Valor de parâmetro muito longo para evento: %s"), *Event.EventName);
            return false;
        }
    }

    for (const auto& NumValue : Event.NumericValues)
    {
        if (NumValue.Key.IsEmpty() || NumValue.Key.Len() > 100)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Chave de valor numérico inválida para evento: %s"), *Event.EventName);
            return false;
        }
        
        // Check for reasonable numeric ranges
        if (!FMath::IsFinite(NumValue.Value))
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: Valor numérico inválido (NaN/Inf) para evento: %s"), *Event.EventName);
            return false;
        }
    }

    return true;
}

// === A/B Testing ===

bool UAuracronAnalyticsBridge::StartABTest(const FAuracronABTestConfiguration& TestConfig)
{
    if (!bSystemInitialized || !bAnalyticsEnabled)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema de analytics não inicializado para A/B Test"));
        return false;
    }

    if (TestConfig.TestID.IsEmpty() || TestConfig.TestName.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Configuração de A/B Test inválida"));
        return false;
    }

    FScopeLock Lock(&AnalyticsMutex);

    // Verificar se o teste já existe
    for (const FAuracronABTestConfiguration& ExistingTest : ActiveABTests)
    {
        if (ExistingTest.TestID == TestConfig.TestID)
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: A/B Test %s já está ativo"), *TestConfig.TestID);
            return false;
        }
    }

    // Adicionar teste ativo
    FAuracronABTestConfiguration NewTest = TestConfig;
    NewTest.bIsActive = true;
    ActiveABTests.Add(NewTest);

    // Registrar evento de início do teste
    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("ABTestStarted");
    Event.EventType = EAuracronAnalyticsEventType::ABTestEvent;
    Event.EventCategory = TEXT("ABTest");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.EventParameters.Add(TEXT("TestID"), TestConfig.TestID);
    Event.EventParameters.Add(TEXT("TestName"), TestConfig.TestName);
    Event.NumericValues.Add(TEXT("VariantBPercentage"), TestConfig.VariantBPercentage);
    Event.Priority = 8;

    RecordAnalyticsEvent(Event);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: A/B Test iniciado: %s"), *TestConfig.TestID);
    return true;
}

bool UAuracronAnalyticsBridge::StopABTest(const FString& TestID)
{
    if (!bSystemInitialized || TestID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&AnalyticsMutex);

    // Encontrar e remover o teste
    for (int32 i = ActiveABTests.Num() - 1; i >= 0; i--)
    {
        if (ActiveABTests[i].TestID == TestID)
        {
            ActiveABTests[i].bIsActive = false;
            
            // Registrar evento de fim do teste
            FAuracronAnalyticsEvent Event;
            Event.EventID = FGuid::NewGuid().ToString();
            Event.EventName = TEXT("ABTestStopped");
            Event.EventType = EAuracronAnalyticsEventType::ABTestEvent;
            Event.EventCategory = TEXT("ABTest");
            Event.EventTimestamp = FDateTime::Now();
            Event.SessionID = CurrentSessionID;
            Event.EventParameters.Add(TEXT("TestID"), TestID);
            Event.Priority = 8;

            RecordAnalyticsEvent(Event);
            
            ActiveABTests.RemoveAt(i);
            UE_LOG(LogTemp, Log, TEXT("AURACRON: A/B Test parado: %s"), *TestID);
            return true;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("AURACRON: A/B Test não encontrado: %s"), *TestID);
    return false;
}

FString UAuracronAnalyticsBridge::GetPlayerVariant(const FString& TestID, const FString& PlayerID)
{
    if (!bSystemInitialized || TestID.IsEmpty() || PlayerID.IsEmpty())
    {
        return TEXT("VariantA"); // Default
    }

    FScopeLock Lock(&AnalyticsMutex);

    // Encontrar o teste ativo
    const FAuracronABTestConfiguration* ActiveTest = nullptr;
    for (const FAuracronABTestConfiguration& Test : ActiveABTests)
    {
        if (Test.TestID == TestID && Test.bIsActive)
        {
            ActiveTest = &Test;
            break;
        }
    }

    if (!ActiveTest)
    {
        return TEXT("VariantA"); // Default se teste não encontrado
    }

    // Usar hash do PlayerID para determinar variante de forma consistente
    uint32 PlayerHash = GetTypeHash(PlayerID);
    float Percentage = (PlayerHash % 100) / 100.0f;

    TMap<FString, FString> SelectedVariant = (Percentage < ActiveTest->VariantBPercentage / 100.0f) ? ActiveTest->VariantB : ActiveTest->VariantA;
    FString VariantName = (Percentage < ActiveTest->VariantBPercentage / 100.0f) ? TEXT("VariantB") : TEXT("VariantA");

    // Registrar atribuição de variante
    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("ABTestVariantAssigned");
    Event.EventType = EAuracronAnalyticsEventType::ABTestEvent;
    Event.EventCategory = TEXT("ABTest");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.PlayerID = PlayerID;
    Event.EventParameters.Add(TEXT("TestID"), TestID);
    Event.EventParameters.Add(TEXT("Variant"), VariantName);
    Event.Priority = 6;

    RecordAnalyticsEvent(Event);

    return VariantName;
}

bool UAuracronAnalyticsBridge::RecordABTestResult(const FString& TestID, const FString& PlayerID, const FString& Outcome, const TMap<FString, float>& Metrics)
{
    if (!bSystemInitialized || TestID.IsEmpty() || PlayerID.IsEmpty() || Outcome.IsEmpty())
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("ABTestResult");
    Event.EventType = EAuracronAnalyticsEventType::ABTestEvent;
    Event.EventCategory = TEXT("ABTest");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.PlayerID = PlayerID;
    Event.EventParameters.Add(TEXT("TestID"), TestID);
    Event.EventParameters.Add(TEXT("Outcome"), Outcome);
    Event.NumericValues = Metrics;
    Event.Priority = 8;

    return RecordAnalyticsEvent(Event);
}

// === Balance Analytics ===

bool UAuracronAnalyticsBridge::CollectChampionMetrics(const FString& ChampionID, const TMap<FString, float>& Metrics)
{
    if (!bSystemInitialized || ChampionID.IsEmpty())
    {
        return false;
    }

    FAuracronBalanceMetrics BalanceData;
    BalanceData.Category = EAuracronBalanceMetricCategory::ChampionPerformance;
    BalanceData.ObjectID = ChampionID;
    BalanceData.WinRate = Metrics.Contains(TEXT("WinRate")) ? Metrics[TEXT("WinRate")] : 0.0f;
    BalanceData.PickRate = Metrics.Contains(TEXT("PickRate")) ? Metrics[TEXT("PickRate")] : 0.0f;
    BalanceData.BanRate = Metrics.Contains(TEXT("BanRate")) ? Metrics[TEXT("BanRate")] : 0.0f;
    BalanceData.AverageKDA = Metrics.Contains(TEXT("AverageKDA")) ? Metrics[TEXT("AverageKDA")] : 0.0f;
    BalanceData.AverageDamagePerMinute = Metrics.Contains(TEXT("DamagePerMinute")) ? Metrics[TEXT("DamagePerMinute")] : 0.0f;
    BalanceData.AverageGoldPerMinute = Metrics.Contains(TEXT("GoldPerMinute")) ? Metrics[TEXT("GoldPerMinute")] : 0.0f;
    BalanceData.SampleSize = Metrics.Contains(TEXT("SampleSize")) ? static_cast<int32>(Metrics[TEXT("SampleSize")]) : 1;

    return RecordBalanceData(BalanceData);
}

// === User Behavior Tracking ===

bool UAuracronAnalyticsBridge::TrackPlayerBehavior(const FString& BehaviorType, const TMap<FString, FString>& BehaviorData)
{
    if (!bSystemInitialized || BehaviorType.IsEmpty())
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = FString::Printf(TEXT("PlayerBehavior_%s"), *BehaviorType);
    Event.EventType = EAuracronAnalyticsEventType::UserBehavior;
    Event.EventCategory = TEXT("Behavior");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.EventParameters = BehaviorData;
    Event.Priority = 5;

    // Obter informações do jogador
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        if (APlayerState* PS = PC->GetPlayerState<APlayerState>())
        {
            Event.PlayerID = PS->GetUniqueId().IsValid() ? PS->GetUniqueId()->ToString() : TEXT("InvalidPlayerID");
        }
    }

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::RecordGameSession(int32 Duration, const FString& GameMode, const TMap<FString, FString>& SessionData)
{
    if (!bSystemInitialized || Duration <= 0 || GameMode.IsEmpty())
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("GameSessionCompleted");
    Event.EventType = EAuracronAnalyticsEventType::UserBehavior;
    Event.EventCategory = TEXT("Session");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.EventParameters = SessionData;
    Event.EventParameters.Add(TEXT("GameMode"), GameMode);
    Event.NumericValues.Add(TEXT("Duration"), static_cast<float>(Duration));
    Event.Priority = 8;

    // Obter informações do jogador
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        if (APlayerState* PS = PC->GetPlayerState<APlayerState>())
        {
            Event.PlayerID = PS->GetUniqueId().IsValid() ? PS->GetUniqueId()->ToString() : TEXT("InvalidPlayerID");
        }
    }

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::TrackEngagement(const FString& EngagementType, float Value, const FString& Context)
{
    if (!bSystemInitialized || EngagementType.IsEmpty())
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = FString::Printf(TEXT("Engagement_%s"), *EngagementType);
    Event.EventType = EAuracronAnalyticsEventType::UserBehavior;
    Event.EventCategory = TEXT("Engagement");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.NumericValues.Add(TEXT("Value"), Value);
    Event.EventParameters.Add(TEXT("Context"), Context);
    Event.Priority = 6;

    // Obter informações do jogador
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        if (APlayerState* PS = PC->GetPlayerState<APlayerState>())
        {
            Event.PlayerID = PS->GetUniqueId().IsValid() ? PS->GetUniqueId()->ToString() : TEXT("InvalidPlayerID");
        }
    }

    return RecordAnalyticsEvent(Event);
}

// === Performance Monitoring ===

bool UAuracronAnalyticsBridge::MonitorFrameRate(float CurrentFPS, float MinFPS, float MaxFPS)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("FrameRateMonitoring");
    Event.EventType = EAuracronAnalyticsEventType::PerformanceMetric;
    Event.EventCategory = TEXT("Performance");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.NumericValues.Add(TEXT("CurrentFPS"), CurrentFPS);
    Event.NumericValues.Add(TEXT("MinFPS"), MinFPS);
    Event.NumericValues.Add(TEXT("MaxFPS"), MaxFPS);
    Event.Priority = 4;

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::MonitorMemoryUsage(float CurrentMemoryMB, float MaxMemoryMB)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("MemoryUsageMonitoring");
    Event.EventType = EAuracronAnalyticsEventType::PerformanceMetric;
    Event.EventCategory = TEXT("Performance");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.NumericValues.Add(TEXT("CurrentMemoryMB"), CurrentMemoryMB);
    Event.NumericValues.Add(TEXT("MaxMemoryMB"), MaxMemoryMB);
    Event.Priority = 4;

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::MonitorNetworkLatency(float CurrentPing, float AveragePing)
{
    if (!bSystemInitialized)
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("NetworkLatencyMonitoring");
    Event.EventType = EAuracronAnalyticsEventType::PerformanceMetric;
    Event.EventCategory = TEXT("Performance");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.NumericValues.Add(TEXT("CurrentPing"), CurrentPing);
    Event.NumericValues.Add(TEXT("AveragePing"), AveragePing);
    Event.Priority = 4;

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::MonitorLoadingTimes(const FString& LoadingContext, float LoadingTimeSeconds)
{
    if (!bSystemInitialized || LoadingContext.IsEmpty())
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("LoadingTimeMonitoring");
    Event.EventType = EAuracronAnalyticsEventType::PerformanceMetric;
    Event.EventCategory = TEXT("Performance");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.EventParameters.Add(TEXT("Context"), LoadingContext);
    Event.NumericValues.Add(TEXT("LoadingTimeSeconds"), LoadingTimeSeconds);
    Event.Priority = 5;

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::CollectAbilityMetrics(const FString& ChampionID, const FString& AbilityID, const TMap<FString, float>& Metrics)
{
    if (!bSystemInitialized || ChampionID.IsEmpty() || AbilityID.IsEmpty())
    {
        return false;
    }

    FAuracronBalanceMetrics BalanceData;
    BalanceData.Category = EAuracronBalanceMetricCategory::AbilityUsage;
    BalanceData.ObjectID = FString::Printf(TEXT("%s_%s"), *ChampionID, *AbilityID);
    BalanceData.WinRate = Metrics.Contains(TEXT("SuccessRate")) ? Metrics[TEXT("SuccessRate")] : 0.0f;
    BalanceData.PickRate = Metrics.Contains(TEXT("UsageRate")) ? Metrics[TEXT("UsageRate")] : 0.0f;
    BalanceData.AverageDamagePerMinute = Metrics.Contains(TEXT("DamagePerUse")) ? Metrics[TEXT("DamagePerUse")] : 0.0f;
    BalanceData.SampleSize = Metrics.Contains(TEXT("SampleSize")) ? static_cast<int32>(Metrics[TEXT("SampleSize")]) : 1;

    return RecordBalanceData(BalanceData);
}

bool UAuracronAnalyticsBridge::CollectSigiloMetrics(const FString& SigiloID, const TMap<FString, float>& Metrics)
{
    if (!bSystemInitialized || SigiloID.IsEmpty())
    {
        return false;
    }

    FAuracronBalanceMetrics BalanceData;
    BalanceData.Category = EAuracronBalanceMetricCategory::SigiloImpact;
    BalanceData.ObjectID = SigiloID;
    BalanceData.WinRate = Metrics.Contains(TEXT("WinRate")) ? Metrics[TEXT("WinRate")] : 0.0f;
    BalanceData.PickRate = Metrics.Contains(TEXT("PickRate")) ? Metrics[TEXT("PickRate")] : 0.0f;
    BalanceData.ObjectiveEffectiveness = Metrics.Contains(TEXT("Effectiveness")) ? Metrics[TEXT("Effectiveness")] : 0.0f;
    BalanceData.SampleSize = Metrics.Contains(TEXT("SampleSize")) ? static_cast<int32>(Metrics[TEXT("SampleSize")]) : 1;

    return RecordBalanceData(BalanceData);
}

bool UAuracronAnalyticsBridge::CollectRealmMetrics(int32 RealmIndex, const TMap<FString, float>& Metrics)
{
    if (!bSystemInitialized || RealmIndex < 0)
    {
        return false;
    }

    FAuracronBalanceMetrics BalanceData;
    BalanceData.Category = EAuracronBalanceMetricCategory::RealmBalance;
    BalanceData.ObjectID = FString::Printf(TEXT("Realm_%d"), RealmIndex);
    BalanceData.WinRate = Metrics.Contains(TEXT("WinRate")) ? Metrics[TEXT("WinRate")] : 0.0f;
    BalanceData.PickRate = Metrics.Contains(TEXT("PopularityRate")) ? Metrics[TEXT("PopularityRate")] : 0.0f;
    BalanceData.AverageTimeToFirstDeath = Metrics.Contains(TEXT("AvgSurvivalTime")) ? Metrics[TEXT("AvgSurvivalTime")] : 0.0f;
    BalanceData.ObjectiveEffectiveness = Metrics.Contains(TEXT("ObjectiveControl")) ? Metrics[TEXT("ObjectiveControl")] : 0.0f;
    BalanceData.SampleSize = Metrics.Contains(TEXT("SampleSize")) ? static_cast<int32>(Metrics[TEXT("SampleSize")]) : 1;

    return RecordBalanceData(BalanceData);
}

// === Private Helper Methods ===

void UAuracronAnalyticsBridge::ProcessEventQueue(float DeltaTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronAnalyticsBridge::ProcessEventQueue);
    
    if (EventQueue.IsEmpty())
    {
        return;
    }
    
    // Process events in batches to avoid frame drops
    const int32 MaxEventsPerFrame = 10;
    int32 ProcessedEvents = 0;
    
    while (!EventQueue.IsEmpty() && ProcessedEvents < MaxEventsPerFrame)
    {
        FAuracronAnalyticsEvent Event;
        if (EventQueue.Num() > 0)
        {
            Event = EventQueue[0];
            EventQueue.RemoveAt(0);

            // Process the event
            // Send event to analytics provider
            if (AnalyticsProvider.IsValid())
            {
                TArray<FAnalyticsEventAttribute> Attributes;
                
                // Add string parameters
                for (const auto& Param : Event.EventParameters)
                {
                    Attributes.Add(FAnalyticsEventAttribute(Param.Key, Param.Value));
                }
                
                // Add numeric values
                for (const auto& NumValue : Event.NumericValues)
                {
                    Attributes.Add(FAnalyticsEventAttribute(NumValue.Key, NumValue.Value));
                }
                
                // Add metadata
                Attributes.Add(FAnalyticsEventAttribute(TEXT("SessionID"), Event.SessionID));
                Attributes.Add(FAnalyticsEventAttribute(TEXT("Priority"), Event.Priority));
                Attributes.Add(FAnalyticsEventAttribute(TEXT("Timestamp"), Event.EventTimestamp.ToString()));
                
                AnalyticsProvider->RecordEvent(Event.EventName, Attributes);
            }
            
            // Store for local analytics if needed
            if (bStoreLocalAnalytics)
            {
                LocalAnalyticsData.Add(Event);
            }
            
            ProcessedEvents++;
        }
    }
    
    // Flush analytics provider periodically
    static float FlushTimer = 0.0f;
    FlushTimer += DeltaTime;
    
    if (FlushTimer >= 30.0f) // Flush every 30 seconds
    {
        if (AnalyticsProvider.IsValid())
        {
            AnalyticsProvider->FlushEvents();
        }
        FlushTimer = 0.0f;
    }
}

void UAuracronAnalyticsBridge::CollectAutomaticMetrics(float DeltaTime)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronAnalyticsBridge::CollectAutomaticMetrics);
    
    static float MetricsTimer = 0.0f;
    MetricsTimer += DeltaTime;
    
    // Collect metrics every 5 seconds
    if (MetricsTimer < 5.0f)
    {
        return;
    }
    
    MetricsTimer = 0.0f;
    
    // Collect performance metrics
    if (UWorld* World = GetWorld())
    {
        // FPS metrics
        float CurrentFPS = 1.0f / DeltaTime;
        RecordPerformanceMetric(TEXT("FPS"), CurrentFPS, TEXT("Automatic"));
        
        // Memory usage
        FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
        float MemoryUsageMB = MemStats.UsedPhysical / (1024.0f * 1024.0f);
        RecordPerformanceMetric(TEXT("MemoryUsage"), MemoryUsageMB, TEXT("Automatic"));
        
        // Player count
        if (AGameStateBase* GameState = World->GetGameState())
        {
            int32 PlayerCount = GameState->PlayerArray.Num();
            RecordPerformanceMetric(TEXT("PlayerCount"), static_cast<float>(PlayerCount), TEXT("Automatic"));
        }
        
        // Network metrics
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            if (UNetConnection* NetConnection = PC->GetNetConnection())
            {
                float Ping = NetConnection->AvgLag * 1000.0f; // Convert to ms
                RecordPerformanceMetric(TEXT("Ping"), Ping, TEXT("Automatic"));
                
                // PacketLoss calculation for UE 5.6
                float PacketLoss = NetConnection->InPacketsLost > 0 ?
                    (float)NetConnection->InPacketsLost / (float)FMath::Max(1, NetConnection->InPackets) : 0.0f;
                RecordPerformanceMetric(TEXT("PacketLoss"), PacketLoss, TEXT("Automatic"));
            }
        }
        
        // GPU metrics (using RHI stats for UE 5.6)
        if (GEngine)
        {
            // Use RHI stats for GPU usage in UE 5.6
            float GPUUsage = 0.0f;
            if (FApp::CanEverRender())
            {
                // Get GPU frame time as a proxy for usage
                GPUUsage = FPlatformTime::ToMilliseconds(RHIGetGPUFrameCycles()) / 16.67f; // Normalize to 60fps
                GPUUsage = FMath::Clamp(GPUUsage, 0.0f, 100.0f);
            }
            RecordPerformanceMetric(TEXT("GPUUsage"), GPUUsage, TEXT("Automatic"));
        }
        
        // Audio metrics (UE 5.6 compatible)
        if (FAudioDeviceHandle AudioDeviceHandle = World->GetAudioDevice())
        {
            if (FAudioDevice* AudioDevice = AudioDeviceHandle.GetAudioDevice())
            {
                // Use GetNumActiveSources for UE 5.6 compatibility
                int32 ActiveSounds = AudioDevice->GetNumActiveSources();
                RecordPerformanceMetric(TEXT("ActiveSounds"), static_cast<float>(ActiveSounds), TEXT("Automatic"));
            }
        }
    }
}

bool UAuracronAnalyticsBridge::RecordGameplayEvent(const FString& EventName, const TMap<FString, FString>& Parameters)
{
    if (EventName.IsEmpty())
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = EventName;
    Event.EventType = EAuracronAnalyticsEventType::GameplayEvent;
    Event.EventCategory = TEXT("Gameplay");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.EventParameters = Parameters;
    Event.Priority = 6;

    // Obter informações do jogador
    if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
    {
        if (APlayerState* PS = PC->GetPlayerState<APlayerState>())
        {
            Event.PlayerID = PS->GetUniqueId().IsValid() ? PS->GetUniqueId()->ToString() : TEXT("InvalidPlayerID");
        }
    }

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::RecordPerformanceMetric(const FString& MetricName, float Value, const FString& Context)
{
    if (MetricName.IsEmpty())
    {
        return false;
    }

    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = FString::Printf(TEXT("Performance_%s"), *MetricName);
    Event.EventType = EAuracronAnalyticsEventType::PerformanceMetric;
    Event.EventCategory = TEXT("Performance");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.NumericValues.Add(MetricName, Value);
    Event.EventParameters.Add(TEXT("Context"), Context);
    Event.Priority = 4;

    return RecordAnalyticsEvent(Event);
}

bool UAuracronAnalyticsBridge::RecordBalanceData(const FAuracronBalanceMetrics& BalanceData)
{
    if (BalanceData.ObjectID.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&AnalyticsMutex);

    // Atualizar ou adicionar métricas de balanceamento
    bool bFound = false;
    for (FAuracronBalanceMetrics& ExistingMetrics : BalanceMetrics)
    {
        if (ExistingMetrics.Category == BalanceData.Category && ExistingMetrics.ObjectID == BalanceData.ObjectID)
        {
            ExistingMetrics = BalanceData;
            ExistingMetrics.LastUpdated = FDateTime::Now();
            bFound = true;
            break;
        }
    }

    if (!bFound)
    {
        FAuracronBalanceMetrics NewMetrics = BalanceData;
        NewMetrics.LastUpdated = FDateTime::Now();
        BalanceMetrics.Add(NewMetrics);
    }

    // Criar evento analítico
    FAuracronAnalyticsEvent Event;
    Event.EventID = FGuid::NewGuid().ToString();
    Event.EventName = TEXT("BalanceData");
    Event.EventType = EAuracronAnalyticsEventType::BalanceData;
    Event.EventCategory = TEXT("Balance");
    Event.EventTimestamp = FDateTime::Now();
    Event.SessionID = CurrentSessionID;
    Event.EventParameters.Add(TEXT("ObjectID"), BalanceData.ObjectID);
    Event.EventParameters.Add(TEXT("Category"), UEnum::GetValueAsString(BalanceData.Category));
    Event.NumericValues.Add(TEXT("WinRate"), BalanceData.WinRate);
    Event.NumericValues.Add(TEXT("PickRate"), BalanceData.PickRate);
    Event.NumericValues.Add(TEXT("BanRate"), BalanceData.BanRate);
    Event.NumericValues.Add(TEXT("AverageKDA"), BalanceData.AverageKDA);
    Event.Priority = 7;

    RecordAnalyticsEvent(Event);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Dados de balanceamento registrados para %s"), *BalanceData.ObjectID);

    return true;
}

// === Data Export and Sync ===

bool UAuracronAnalyticsBridge::ExportAnalyticsData(const FString& FilePath)
{
    if (!bSystemInitialized || FilePath.IsEmpty())
    {
        return false;
    }

    FScopeLock Lock(&AnalyticsMutex);

    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    
    // Export session info
    JsonObject->SetStringField(TEXT("SessionID"), CurrentSessionID);
    JsonObject->SetStringField(TEXT("ExportTimestamp"), FDateTime::Now().ToString());
    
    // Export events
    TArray<TSharedPtr<FJsonValue>> EventsArray;
    for (const FAuracronAnalyticsEvent& Event : LocalAnalyticsData)
    {
        TSharedPtr<FJsonObject> EventJson = MakeShareable(new FJsonObject);
        EventJson->SetStringField(TEXT("EventID"), Event.EventID);
        EventJson->SetStringField(TEXT("EventName"), Event.EventName);
        EventJson->SetStringField(TEXT("EventCategory"), Event.EventCategory);
        EventJson->SetStringField(TEXT("Timestamp"), Event.EventTimestamp.ToString());
        EventJson->SetNumberField(TEXT("Priority"), Event.Priority);
        
        // Add parameters
        TSharedPtr<FJsonObject> ParamsJson = MakeShareable(new FJsonObject);
        for (const auto& Param : Event.EventParameters)
        {
            ParamsJson->SetStringField(Param.Key, Param.Value);
        }
        EventJson->SetObjectField(TEXT("Parameters"), ParamsJson);
        
        // Add numeric values
        TSharedPtr<FJsonObject> NumericJson = MakeShareable(new FJsonObject);
        for (const auto& NumValue : Event.NumericValues)
        {
            NumericJson->SetNumberField(NumValue.Key, NumValue.Value);
        }
        EventJson->SetObjectField(TEXT("NumericValues"), NumericJson);
        
        EventsArray.Add(MakeShareable(new FJsonValueObject(EventJson)));
    }
    JsonObject->SetArrayField(TEXT("Events"), EventsArray);
    
    // Export balance metrics
    TArray<TSharedPtr<FJsonValue>> BalanceArray;
    for (const FAuracronBalanceMetrics& Balance : BalanceMetrics)
    {
        TSharedPtr<FJsonObject> BalanceJson = MakeShareable(new FJsonObject);
        BalanceJson->SetStringField(TEXT("ObjectID"), Balance.ObjectID);
        BalanceJson->SetStringField(TEXT("Category"), UEnum::GetValueAsString(Balance.Category));
        BalanceJson->SetNumberField(TEXT("WinRate"), Balance.WinRate);
        BalanceJson->SetNumberField(TEXT("PickRate"), Balance.PickRate);
        BalanceJson->SetNumberField(TEXT("SampleSize"), Balance.SampleSize);
        BalanceArray.Add(MakeShareable(new FJsonValueObject(BalanceJson)));
    }
    JsonObject->SetArrayField(TEXT("BalanceMetrics"), BalanceArray);
    
    // Write to file
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
    
    bool bSuccess = FFileHelper::SaveStringToFile(OutputString, *FilePath);
    
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Dados analíticos exportados para %s"), *FilePath);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("AURACRON: Falha ao exportar dados para %s"), *FilePath);
    }
    
    return bSuccess;
}

bool UAuracronAnalyticsBridge::SyncWithFirebaseAnalytics()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado para sync Firebase"));
        return false;
    }

    // Placeholder para integração com Firebase
    // Em uma implementação real, aqui seria feita a sincronização com Firebase Analytics
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Sincronização com Firebase Analytics iniciada"));
    
    // Simular sucesso da sincronização
    OnAnalyticsDataSynced.Broadcast(true);
    
    return true;
}

bool UAuracronAnalyticsBridge::SendDataToAnalyticsServer()
{
    if (!bSystemInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sistema não inicializado para envio de dados"));
        return false;
    }

    // Placeholder para envio de dados para servidor de analytics
    // Em uma implementação real, aqui seria feito o envio HTTP dos dados
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Envio de dados para servidor de analytics iniciado"));
    
    // Simular sucesso do envio
    OnAnalyticsDataSynced.Broadcast(true);
    
    return true;
}
