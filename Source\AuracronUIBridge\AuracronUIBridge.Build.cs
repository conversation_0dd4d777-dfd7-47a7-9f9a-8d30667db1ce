﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Interface do UsuÃ¡rio Bridge Build Configuration
using UnrealBuildTool;
public class AuracronUIBridge : ModuleRules
{
    public AuracronUIBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicIncludePaths.AddRange(
            new string[] {
                // ... add public include paths required here ...
            }
        );
        PrivateIncludePaths.AddRange(
            new string[] {
                // ... add other private include paths required here ...
            }
        );
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "UMG",
                "Slate",
                "SlateCore",
                "CommonInput",
                "CommonUI",
                "GameplayTags",
                "ModularGameplay",
                "EnhancedInput",
                "InputCore",
                "DeveloperSettings",
                "EngineSettings",
                "RenderCore",
                "RHI"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "Json",
                "AudioMixer",
                "SignalProcessing",
                "ImageWrapper",
                "ImageCore",
                "GameplayAbilities",
                "GameplayTasks",
                "NetCore",
                "ReplicationGraph",
                "OnlineSubsystemUtils",
                "Sockets",
                "Networking",
                "MediaAssets",
                "MediaUtils",
                "WebBrowserWidget",
                "HTTPServer",
                "Localization",
                "ICU"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "BlueprintGraph",
                    "CommonUIEditor",
                    "EditorStyle",
                    "EditorWidgets",
                    "Kismet",
                    "KismetCompiler",
                    "PropertyEditor",
                    "ToolMenus",
                    "UMGEditor",
                    "UnrealEd",
                    "MediaPlayerEditor"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // Enable optimization for shipping builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            OptimizeCode = CodeOptimization.InShippingBuildsOnly;
            bUseUnity = true;
        }
        // Enable additional features for development builds
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_UI_DEBUG=1");
            PublicDefinitions.Add("AURACRON_UI_PROFILING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_UI_DEBUG=0");
            PublicDefinitions.Add("AURACRON_UI_PROFILING=0");
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=1");
            PublicDefinitions.Add("AURACRON_TOUCH_UI=1");
            // Mobile-specific optimizations
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=0");
            PublicDefinitions.Add("AURACRON_TOUCH_UI=0");
        }
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_COMMON_UI=1");
        PublicDefinitions.Add("WITH_COMMON_INPUT=1");
        PublicDefinitions.Add("WITH_ENHANCED_INPUT=1");
        PublicDefinitions.Add("WITH_SLATE_DEBUGGING=1");
        PublicDefinitions.Add("WITH_UMG_DEBUGGING=1");
        PublicDefinitions.Add("WITH_ACCESSIBILITY=1");
        PublicDefinitions.Add("WITH_LOCALIZATION=1");
        // UI Features
        PublicDefinitions.Add("AURACRON_3D_MINIMAP=1");
        PublicDefinitions.Add("AURACRON_ADAPTIVE_UI=1");
        PublicDefinitions.Add("AURACRON_CROSS_PLATFORM_UI=1");
        PublicDefinitions.Add("AURACRON_ACCESSIBILITY_SUPPORT=1");
        PublicDefinitions.Add("AURACRON_DYNAMIC_SCALING=1");
        PublicDefinitions.Add("AURACRON_TOUCH_GESTURES=1");
        // Performance optimizations for UI
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_UI=1");
            PublicDefinitions.Add("AURACRON_CACHE_UI_ELEMENTS=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_OPTIMIZE_UI=0");
            PublicDefinitions.Add("AURACRON_CACHE_UI_ELEMENTS=0");
        }
        // Accessibility and localization
        PublicDefinitions.Add("AURACRON_SCREEN_READER_SUPPORT=1");
        PublicDefinitions.Add("AURACRON_COLOR_BLIND_SUPPORT=1");
        PublicDefinitions.Add("AURACRON_HIGH_CONTRAST_MODE=1");
        PublicDefinitions.Add("AURACRON_MULTI_LANGUAGE=1");
    }
}



