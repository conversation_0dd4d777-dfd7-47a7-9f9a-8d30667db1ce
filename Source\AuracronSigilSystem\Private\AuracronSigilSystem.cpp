// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sigil System Module Implementation

#include "AuracronSigilSystem.h"
#include "Modules/ModuleManager.h"

#define LOCTEXT_NAMESPACE "FAuracronSigilSystemModule"

void FAuracronSigilSystemModule::StartupModule()
{
    // This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sigil System Module Started"));
}

void FAuracronSigilSystemModule::ShutdownModule()
{
    // This function may be called during shutdown to clean up your module. For modules that support dynamic reloading,
    // we call this function before unloading the module.
    UE_LOG(LogTemp, Warning, TEXT("AURACRON: Sigil System Module Shutdown"));
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FAuracronSigilSystemModule, AuracronSigilSystem)
